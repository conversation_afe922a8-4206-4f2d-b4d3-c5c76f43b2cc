{
  "expo": {
    "name": "IsotopeAI",
    "slug": "isotope-ai-mobile",
    "version": "1.0.0",
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "userInterfaceStyle": "automatic",
    "newArchEnabled": true,
    "scheme": "isotope-ai",
    "splash": {
      "image": "./assets/splash-icon.png",
      "resizeMode": "contain",
      "backgroundColor": "#6750A4"
    },
    "assetBundlePatterns": [
      "**/*"
    ],
    "ios": {
      "supportsTablet": true,
      "bundleIdentifier": "com.isotopeai.mobile"
      // "googleServicesFile": "./GoogleService-Info.plist" // Temporarily disabled
    },
    "android": {
      "adaptiveIcon": {
        "foregroundImage": "./assets/adaptive-icon.png",
        "backgroundColor": "#6750A4"
      },
      "edgeToEdgeEnabled": true,
      "package": "com.isotopeai.mobile"
      // "googleServicesFile": "./google-services.json" // Temporarily disabled
    },
    "web": {
      "favicon": "./assets/favicon.png",
      "bundler": "metro"
    },
    "plugins": [
      // "@react-native-google-signin/google-signin" // Temporarily disabled - requires Google Services files
    ],
    "extra": {
      "router": {
        "origin": false
      },
      "eas": {
        "projectId": "your-project-id-here"
      }
    }
  }
}
