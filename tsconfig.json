{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/screens/*": ["src/screens/*"], "@/services/*": ["src/services/*"], "@/stores/*": ["src/stores/*"], "@/hooks/*": ["src/hooks/*"], "@/utils/*": ["src/utils/*"], "@/types/*": ["src/types/*"], "@/constants/*": ["src/constants/*"]}, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "declaration": false, "declarationMap": false, "sourceMap": true, "allowJs": false, "checkJs": false, "target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "jsx": "react-jsx", "skipLibCheck": true}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}