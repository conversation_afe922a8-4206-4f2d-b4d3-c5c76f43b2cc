<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>120215440192-p6rq1rri0h4vq7i689lfcedi7u9spp19.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.120215440192-p6rq1rri0h4vq7i689lfcedi7u9spp19</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>120215440192-1botkoeudg4frfpo4tu7dnae107c6pug.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyC8OiBakwQTI64J6ZxfS99D63uWaoKwdzI</string>
	<key>GCM_SENDER_ID</key>
	<string>120215440192</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.isotopeai.mobile</string>
	<key>PROJECT_ID</key>
	<string>doubtgpt</string>
	<key>STORAGE_BUCKET</key>
	<string>doubtgpt.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:120215440192:ios:951b14fad9c07d90bd48a5</string>
</dict>
</plist>