import { useEffect, useRef } from 'react';
import * as Notifications from 'expo-notifications';
import { timerNotificationService } from '../services/notifications/timerNotificationService';
import { useTimerStore } from '../stores/timerStore';
import { getPhaseDuration } from '../utils/timerUtils';

/**
 * Hook to manage timer notifications
 */
export const useTimerNotifications = () => {
  const isInitialized = useRef(false);
  const currentNotificationId = useRef<string | null>(null);
  const intervalNotificationIds = useRef<string[]>([]);
  
  const {
    status,
    mode,
    currentPhase,
    displayTime,
    elapsedTime,
    selectedSubject,
    workDuration,
    shortBreakDuration,
    longBreakDuration,
    notificationIntervals,
    soundEnabled,
    vibrationEnabled,
  } = useTimerStore();

  // Initialize notification service
  useEffect(() => {
    const initializeService = async () => {
      if (!isInitialized.current) {
        const success = await timerNotificationService.initialize();
        if (success) {
          isInitialized.current = true;
          console.log('Timer notifications initialized');
        }
      }
    };

    initializeService();
  }, []);

  // Handle notification responses
  useEffect(() => {
    const subscription = Notifications.addNotificationResponseReceivedListener(
      (response) => {
        timerNotificationService.handleNotificationResponse(response);
      }
    );

    return () => subscription.remove();
  }, []);

  // Schedule phase completion notification for Pomodoro mode
  useEffect(() => {
    if (!isInitialized.current || mode !== 'pomodoro' || status !== 'running') {
      return;
    }

    const schedulePhaseNotification = async () => {
      // Cancel existing notification
      if (currentNotificationId.current) {
        await timerNotificationService.cancelNotification(currentNotificationId.current);
        currentNotificationId.current = null;
      }

      if (displayTime > 0 && soundEnabled) {
        const nextPhase = getNextPhase();
        const notificationId = await timerNotificationService.schedulePhaseCompleteNotification(
          currentPhase,
          nextPhase,
          displayTime,
          selectedSubject || undefined
        );
        
        if (notificationId) {
          currentNotificationId.current = notificationId;
        }
      }
    };

    schedulePhaseNotification();
  }, [
    mode,
    status,
    currentPhase,
    displayTime,
    selectedSubject,
    soundEnabled,
    isInitialized.current,
  ]);

  // Schedule interval notifications for Stopwatch mode
  useEffect(() => {
    if (!isInitialized.current || mode !== 'stopwatch' || status !== 'running') {
      return;
    }

    const scheduleIntervalNotifications = async () => {
      // Cancel existing interval notifications
      for (const id of intervalNotificationIds.current) {
        await timerNotificationService.cancelNotification(id);
      }
      intervalNotificationIds.current = [];

      if (soundEnabled && notificationIntervals.length > 0) {
        const currentMinutes = Math.floor(elapsedTime / 60);
        
        // Schedule notifications for upcoming intervals
        for (const interval of notificationIntervals) {
          if (interval > currentMinutes) {
            const delaySeconds = (interval * 60) - elapsedTime;
            if (delaySeconds > 0) {
              const notificationId = await timerNotificationService.scheduleIntervalNotification(
                interval,
                delaySeconds,
                selectedSubject || undefined
              );
              
              if (notificationId) {
                intervalNotificationIds.current.push(notificationId);
              }
            }
          }
        }
      }
    };

    scheduleIntervalNotifications();
  }, [
    mode,
    status,
    elapsedTime,
    notificationIntervals,
    selectedSubject,
    soundEnabled,
    isInitialized.current,
  ]);

  // Cancel notifications when timer stops or pauses
  useEffect(() => {
    if (status === 'idle' || status === 'paused') {
      const cancelNotifications = async () => {
        if (currentNotificationId.current) {
          await timerNotificationService.cancelNotification(currentNotificationId.current);
          currentNotificationId.current = null;
        }

        for (const id of intervalNotificationIds.current) {
          await timerNotificationService.cancelNotification(id);
        }
        intervalNotificationIds.current = [];
      };

      cancelNotifications();
    }
  }, [status]);

  // Helper function to get next phase
  const getNextPhase = () => {
    if (currentPhase === 'work') {
      // Simplified logic - in real implementation, consider pomodoro count
      return 'shortBreak';
    }
    return 'work';
  };

  // Manual notification functions
  const sendSessionCompleteNotification = async (sessionData: any) => {
    if (!isInitialized.current) return;

    const config = {
      title: '🎉 Study Session Complete!',
      body: `Great work on ${sessionData.subject}! Duration: ${Math.floor(sessionData.duration / 60)} minutes`,
      sound: soundEnabled,
      data: sessionData,
    };

    await timerNotificationService.sendImmediateNotification(config);
  };

  const sendBreakReminder = async (breakType: 'short' | 'long') => {
    if (!isInitialized.current) return;

    const config = {
      title: breakType === 'short' ? '☕ Short Break Time!' : '🌟 Long Break Time!',
      body: `Time to take a ${breakType} break. You've earned it!`,
      sound: soundEnabled,
      data: { type: 'break_reminder', breakType },
    };

    await timerNotificationService.sendImmediateNotification(config);
  };

  const sendCustomReminder = async (title: string, body: string, delaySeconds?: number) => {
    if (!isInitialized.current) return;

    if (delaySeconds && delaySeconds > 0) {
      return await timerNotificationService.scheduleSessionReminder(
        title,
        body,
        delaySeconds,
        { type: 'custom_reminder' }
      );
    } else {
      const config = {
        title,
        body,
        sound: soundEnabled,
        data: { type: 'custom_reminder' },
      };

      await timerNotificationService.sendImmediateNotification(config);
    }
  };

  const cancelAllNotifications = async () => {
    if (!isInitialized.current) return;

    await timerNotificationService.cancelAllNotifications();
    currentNotificationId.current = null;
    intervalNotificationIds.current = [];
  };

  const getScheduledNotifications = async () => {
    if (!isInitialized.current) return [];

    return await timerNotificationService.getScheduledNotifications();
  };

  return {
    sendSessionCompleteNotification,
    sendBreakReminder,
    sendCustomReminder,
    cancelAllNotifications,
    getScheduledNotifications,
    isInitialized: isInitialized.current,
  };
};
