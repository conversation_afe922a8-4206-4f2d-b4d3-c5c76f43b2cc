import { useEffect, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { backgroundTimerService } from '../services/timer/backgroundTimerService';
import { useTimerStore } from '../stores/timerStore';

/**
 * Hook to manage background timer functionality
 */
export const useBackgroundTimer = () => {
  const isInitialized = useRef(false);
  const { status, persistState } = useTimerStore();

  // Initialize background timer service
  useEffect(() => {
    const initializeService = async () => {
      if (!isInitialized.current) {
        await backgroundTimerService.initialize();
        isInitialized.current = true;
      }
    };

    initializeService();

    // Cleanup on unmount
    return () => {
      backgroundTimerService.cleanup();
      isInitialized.current = false;
    };
  }, []);

  // Auto-save timer state when it changes
  useEffect(() => {
    const saveState = async () => {
      if (isInitialized.current) {
        await persistState();
      }
    };

    // Debounce state saving to avoid excessive writes
    const timeoutId = setTimeout(saveState, 1000);
    return () => clearTimeout(timeoutId);
  }, [status, persistState]);

  // Manual save function
  const saveCurrentState = async () => {
    await backgroundTimerService.saveCurrentState();
  };

  // Check if timer is running in background
  const isRunningInBackground = async () => {
    return await backgroundTimerService.isRunningInBackground();
  };

  // Get background session duration
  const getBackgroundDuration = async () => {
    return await backgroundTimerService.getBackgroundDuration();
  };

  return {
    saveCurrentState,
    isRunningInBackground,
    getBackgroundDuration,
  };
};

/**
 * Hook to handle app state changes for timer persistence
 */
export const useAppStateTimer = () => {
  const appState = useRef(AppState.currentState);
  const { status, persistState } = useTimerStore();

  useEffect(() => {
    const handleAppStateChange = async (nextAppState: AppStateStatus) => {
      // Save state when app goes to background
      if (appState.current.match(/active|foreground/) && nextAppState === 'background') {
        if (status === 'running') {
          await persistState();
        }
      }

      appState.current = nextAppState;
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [status, persistState]);

  return {
    currentAppState: appState.current,
  };
};
