import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';
import { useDynamicTheme } from '../contexts/DynamicThemeProvider';
import {
  ExpressiveEasing,
  ExpressiveDurations,
  ExpressiveSprings,
  EmotionalMotion,
  GestureMotion,
  LoadingMotion,
  MotionUtils,
} from '../constants/expressiveMotion';

const { width: screenWidth } = Dimensions.get('window');

/**
 * Motion Demo Component
 * Demonstrates the Material 3 Expressive Motion System
 */
export const MotionDemo: React.FC = () => {
  const theme = useDynamicTheme();
  const [activeDemo, setActiveDemo] = useState<string>('easing');

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const bounceAnim = useRef(new Animated.Value(1)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  // Demo functions
  const runFadeAnimation = () => {
    fadeAnim.setValue(0);
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: ExpressiveDurations.fadeIn,
      easing: ExpressiveEasing.gentle,
      useNativeDriver: true,
    }).start();
  };

  const runScaleAnimation = () => {
    scaleAnim.setValue(0.8);
    Animated.timing(scaleAnim, {
      toValue: 1,
      duration: ExpressiveDurations.scaleIn,
      easing: ExpressiveEasing.bounce,
      useNativeDriver: true,
    }).start();
  };

  const runSlideAnimation = () => {
    slideAnim.setValue(-screenWidth);
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: ExpressiveDurations.slideIn,
      easing: ExpressiveEasing.decelerate,
      useNativeDriver: true,
    }).start();
  };

  const runRotateAnimation = () => {
    rotateAnim.setValue(0);
    Animated.timing(rotateAnim, {
      toValue: 1,
      duration: ExpressiveDurations.dramatic,
      easing: ExpressiveEasing.creative,
      useNativeDriver: true,
    }).start();
  };

  const runBounceAnimation = () => {
    bounceAnim.setValue(1);
    Animated.sequence([
      Animated.timing(bounceAnim, {
        toValue: 1.2,
        duration: ExpressiveDurations.quick,
        easing: ExpressiveEasing.energetic,
        useNativeDriver: true,
      }),
      Animated.timing(bounceAnim, {
        toValue: 1,
        duration: ExpressiveDurations.medium,
        easing: ExpressiveEasing.bounce,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const runPulseAnimation = () => {
    const pulse = () => {
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: ExpressiveDurations.relaxed / 2,
          easing: ExpressiveEasing.gentle,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: ExpressiveDurations.relaxed / 2,
          easing: ExpressiveEasing.gentle,
          useNativeDriver: true,
        }),
      ]).start(() => pulse());
    };
    pulse();
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      padding: 16,
    },
    header: {
      marginBottom: 24,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.primary,
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.onSurfaceVariant,
    },
    tabContainer: {
      flexDirection: 'row',
      marginBottom: 24,
      backgroundColor: theme.colors.surfaceContainer,
      borderRadius: 12,
      padding: 4,
    },
    tab: {
      flex: 1,
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 8,
      alignItems: 'center',
    },
    activeTab: {
      backgroundColor: theme.colors.primary,
    },
    tabText: {
      fontSize: 12,
      fontWeight: '500',
      color: theme.colors.onSurfaceVariant,
    },
    activeTabText: {
      color: theme.colors.onPrimary,
    },
    demoSection: {
      backgroundColor: theme.colors.surfaceContainer,
      borderRadius: 16,
      padding: 20,
      marginBottom: 16,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.onSurface,
      marginBottom: 16,
    },
    demoGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
    },
    demoButton: {
      backgroundColor: theme.colors.primaryContainer,
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 12,
      minWidth: 100,
      alignItems: 'center',
    },
    demoButtonText: {
      color: theme.colors.onPrimaryContainer,
      fontWeight: '500',
      fontSize: 12,
    },
    animationBox: {
      width: 80,
      height: 80,
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
      marginVertical: 16,
      alignSelf: 'center',
    },
    infoText: {
      fontSize: 14,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginTop: 8,
    },
    easingDemo: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginVertical: 8,
    },
    easingName: {
      fontSize: 14,
      color: theme.colors.onSurface,
      flex: 1,
    },
    easingBar: {
      width: 100,
      height: 4,
      backgroundColor: theme.colors.outline,
      borderRadius: 2,
      overflow: 'hidden',
    },
    easingProgress: {
      height: '100%',
      backgroundColor: theme.colors.primary,
      borderRadius: 2,
    },
  });

  const renderEasingDemo = () => (
    <View style={styles.demoSection}>
      <Text style={styles.sectionTitle}>Easing Curves</Text>
      {Object.entries(ExpressiveEasing).slice(0, 8).map(([name, easing]) => (
        <View key={name} style={styles.easingDemo}>
          <Text style={styles.easingName}>{name}</Text>
          <View style={styles.easingBar}>
            <View style={[styles.easingProgress, { width: '60%' }]} />
          </View>
        </View>
      ))}
      <Text style={styles.infoText}>
        Each curve creates different emotional impacts
      </Text>
    </View>
  );

  const renderAnimationDemo = () => (
    <View style={styles.demoSection}>
      <Text style={styles.sectionTitle}>Basic Animations</Text>
      
      <View style={styles.demoGrid}>
        <TouchableOpacity style={styles.demoButton} onPress={runFadeAnimation}>
          <Text style={styles.demoButtonText}>Fade In</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.demoButton} onPress={runScaleAnimation}>
          <Text style={styles.demoButtonText}>Scale Bounce</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.demoButton} onPress={runSlideAnimation}>
          <Text style={styles.demoButtonText}>Slide In</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.demoButton} onPress={runRotateAnimation}>
          <Text style={styles.demoButtonText}>Creative Rotate</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.demoButton} onPress={runBounceAnimation}>
          <Text style={styles.demoButtonText}>Energy Bounce</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.demoButton} onPress={runPulseAnimation}>
          <Text style={styles.demoButtonText}>Gentle Pulse</Text>
        </TouchableOpacity>
      </View>

      <Animated.View
        style={[
          styles.animationBox,
          {
            opacity: fadeAnim,
            transform: [
              { scale: Animated.multiply(scaleAnim, bounceAnim, pulseAnim) },
              { translateX: slideAnim },
              {
                rotate: rotateAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '360deg'],
                }),
              },
            ],
          },
        ]}
      />
      
      <Text style={styles.infoText}>
        Tap buttons to see different animation styles
      </Text>
    </View>
  );

  const renderDurationDemo = () => (
    <View style={styles.demoSection}>
      <Text style={styles.sectionTitle}>Duration System</Text>
      
      <View style={styles.demoGrid}>
        {Object.entries(ExpressiveDurations).slice(0, 12).map(([name, duration]) => (
          <View key={name} style={styles.demoButton}>
            <Text style={styles.demoButtonText}>{name}</Text>
            <Text style={[styles.demoButtonText, { fontSize: 10, opacity: 0.7 }]}>
              {duration}ms
            </Text>
          </View>
        ))}
      </View>
      
      <Text style={styles.infoText}>
        Optimized durations for different interaction types
      </Text>
    </View>
  );

  const renderEmotionalDemo = () => (
    <View style={styles.demoSection}>
      <Text style={styles.sectionTitle}>Emotional Motion</Text>
      
      <View style={styles.demoGrid}>
        {Object.entries(EmotionalMotion).map(([emotion, config]) => (
          <View key={emotion} style={styles.demoButton}>
            <Text style={styles.demoButtonText}>{emotion}</Text>
            <Text style={[styles.demoButtonText, { fontSize: 10, opacity: 0.7 }]}>
              {config.duration}ms
            </Text>
          </View>
        ))}
      </View>
      
      <Text style={styles.infoText}>
        Animations that convey specific emotions and states
      </Text>
    </View>
  );

  const tabs = [
    { key: 'easing', label: 'Easing' },
    { key: 'animations', label: 'Animations' },
    { key: 'durations', label: 'Durations' },
    { key: 'emotional', label: 'Emotional' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Motion System</Text>
        <Text style={styles.subtitle}>Material 3 Expressive Animations</Text>
      </View>

      <View style={styles.tabContainer}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.key}
            style={[styles.tab, activeDemo === tab.key && styles.activeTab]}
            onPress={() => setActiveDemo(tab.key)}
          >
            <Text
              style={[
                styles.tabText,
                activeDemo === tab.key && styles.activeTabText,
              ]}
            >
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {activeDemo === 'easing' && renderEasingDemo()}
      {activeDemo === 'animations' && renderAnimationDemo()}
      {activeDemo === 'durations' && renderDurationDemo()}
      {activeDemo === 'emotional' && renderEmotionalDemo()}
    </ScrollView>
  );
};

export default MotionDemo;
