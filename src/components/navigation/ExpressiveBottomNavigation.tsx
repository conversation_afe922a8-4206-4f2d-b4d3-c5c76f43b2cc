import React, { useRef, useEffect } from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  Platform,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypographyVariants } from '../../constants/expressiveTheme';
import { getElevationStyle } from '../../constants/surfaceElevation';
import { ExpressiveEasing, ExpressiveDurations } from '../../constants/expressiveMotion';

// ===========================================
// 🧭 EXPRESSIVE BOTTOM NAVIGATION
// ===========================================

export interface NavigationItem {
  key: string;
  label: string;
  icon: React.ReactNode;
  activeIcon?: React.ReactNode;
  badge?: number | string;
}

interface ExpressiveBottomNavigationProps {
  items: NavigationItem[];
  activeKey: string;
  onItemPress: (key: string) => void;
  showLabels?: boolean;
  elevation?: number;
  backgroundColor?: string;
  style?: any;
}

export const ExpressiveBottomNavigation: React.FC<ExpressiveBottomNavigationProps> = ({
  items,
  activeKey,
  onItemPress,
  showLabels = true,
  elevation = 3,
  backgroundColor,
  style,
}) => {
  const theme = useDynamicTheme();
  const { width: screenWidth } = Dimensions.get('window');
  
  // Animation values for each item
  const animationValues = useRef(
    items.reduce((acc, item) => {
      acc[item.key] = {
        scale: new Animated.Value(item.key === activeKey ? 1.1 : 1),
        translateY: new Animated.Value(item.key === activeKey ? -2 : 0),
        opacity: new Animated.Value(item.key === activeKey ? 1 : 0.7),
        indicatorWidth: new Animated.Value(item.key === activeKey ? 32 : 0),
      };
      return acc;
    }, {} as Record<string, any>)
  ).current;

  // Animate active item changes
  useEffect(() => {
    items.forEach((item) => {
      const isActive = item.key === activeKey;
      const animations = animationValues[item.key];

      Animated.parallel([
        Animated.timing(animations.scale, {
          toValue: isActive ? 1.1 : 1,
          duration: ExpressiveDurations.medium,
          easing: ExpressiveEasing.bounce,
          useNativeDriver: true,
        }),
        Animated.timing(animations.translateY, {
          toValue: isActive ? -2 : 0,
          duration: ExpressiveDurations.medium,
          easing: ExpressiveEasing.bounce,
          useNativeDriver: true,
        }),
        Animated.timing(animations.opacity, {
          toValue: isActive ? 1 : 0.7,
          duration: ExpressiveDurations.short,
          easing: ExpressiveEasing.standard,
          useNativeDriver: true,
        }),
        Animated.timing(animations.indicatorWidth, {
          toValue: isActive ? 32 : 0,
          duration: ExpressiveDurations.medium,
          easing: ExpressiveEasing.decelerate,
          useNativeDriver: false,
        }),
      ]).start();
    });
  }, [activeKey, animationValues, items]);

  const handleItemPress = (item: NavigationItem) => {
    // Haptic feedback would go here
    onItemPress(item.key);
  };

  const styles = StyleSheet.create({
    container: {
      backgroundColor: backgroundColor || theme.colors.surface,
      borderTopWidth: 1,
      borderTopColor: theme.colors.outline,
      paddingBottom: Platform.OS === 'ios' ? 20 : 8, // Account for safe area
      paddingTop: 8,
      ...getElevationStyle(elevation, 'primary'),
    },
    content: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
      paddingHorizontal: 16,
    },
    item: {
      flex: 1,
      alignItems: 'center',
      paddingVertical: 8,
      paddingHorizontal: 4,
      position: 'relative',
    },
    iconContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      marginBottom: showLabels ? 4 : 0,
    },
    activeIndicator: {
      position: 'absolute',
      top: -6,
      height: 3,
      backgroundColor: theme.colors.primary,
      borderRadius: 1.5,
    },
    label: {
      ...ExpressiveTypographyVariants.navigationLabel,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginTop: 2,
    },
    activeLabel: {
      color: theme.colors.primary,
      fontWeight: '600',
    },
    badge: {
      position: 'absolute',
      top: -4,
      right: -8,
      backgroundColor: theme.colors.error,
      borderRadius: 10,
      minWidth: 20,
      height: 20,
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: 6,
    },
    badgeText: {
      ...ExpressiveTypographyVariants.labelSmall,
      color: theme.colors.onError,
      fontSize: 10,
      fontWeight: '600',
    },
  });

  const renderNavigationItem = (item: NavigationItem, index: number) => {
    const isActive = item.key === activeKey;
    const animations = animationValues[item.key];

    return (
      <TouchableOpacity
        key={item.key}
        style={styles.item}
        onPress={() => handleItemPress(item)}
        activeOpacity={0.7}
      >
        {/* Active Indicator */}
        <Animated.View
          style={[
            styles.activeIndicator,
            {
              width: animations.indicatorWidth,
            },
          ]}
        />

        {/* Icon Container */}
        <Animated.View
          style={[
            styles.iconContainer,
            {
              transform: [
                { scale: animations.scale },
                { translateY: animations.translateY },
              ],
              opacity: animations.opacity,
            },
          ]}
        >
          {isActive && item.activeIcon ? item.activeIcon : item.icon}
          
          {/* Badge */}
          {item.badge && (
            <View style={styles.badge}>
              <Text style={styles.badgeText}>
                {typeof item.badge === 'number' && item.badge > 99 ? '99+' : item.badge}
              </Text>
            </View>
          )}
        </Animated.View>

        {/* Label */}
        {showLabels && (
          <Animated.View style={{ opacity: animations.opacity }}>
            <Text
              style={[
                styles.label,
                isActive && styles.activeLabel,
              ]}
              numberOfLines={1}
            >
              {item.label}
            </Text>
          </Animated.View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, style]}>
      <View style={styles.content}>
        {items.map((item, index) => renderNavigationItem(item, index))}
      </View>
    </View>
  );
};

export default ExpressiveBottomNavigation;
