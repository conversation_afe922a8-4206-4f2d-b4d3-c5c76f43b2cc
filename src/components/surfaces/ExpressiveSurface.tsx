import React, { useRef, useEffect } from 'react';
import {
  View,
  ViewStyle,
  Animated,
  StyleSheet,
  ViewProps,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import {
  ElevationLevel,
  SurfaceContainer,
  getElevationStyle,
  getAnimatedElevationStyle,
  getSurfaceContainerColor,
  getSurfaceElevation,
  ColoredElevation,
} from '../../constants/surfaceElevation';

// ===========================================
// 🏔️ EXPRESSIVE SURFACE COMPONENT
// ===========================================

interface ExpressiveSurfaceProps extends ViewProps {
  children?: React.ReactNode;
  elevation?: ElevationLevel;
  animatedElevation?: Animated.Value;
  container?: SurfaceContainer;
  colorVariant?: keyof typeof ColoredElevation;
  borderRadius?: number;
  animated?: boolean;
  onPress?: () => void;
  onLongPress?: () => void;
  disabled?: boolean;
  style?: ViewStyle;
}

export const ExpressiveSurface: React.FC<ExpressiveSurfaceProps> = ({
  children,
  elevation = 1,
  animatedElevation,
  container = 'surface',
  colorVariant,
  borderRadius = 12,
  animated = false,
  onPress,
  onLongPress,
  disabled = false,
  style,
  ...props
}) => {
  const theme = useDynamicTheme();
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const elevationAnim = useRef(new Animated.Value(elevation)).current;

  // Handle press animations
  const handlePressIn = () => {
    if (disabled || !animated) return;
    
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 0.98,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(elevationAnim, {
        toValue: Math.min(elevation + 1, 5),
        duration: 100,
        useNativeDriver: false,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    if (disabled || !animated) return;
    
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(elevationAnim, {
        toValue: elevation,
        duration: 150,
        useNativeDriver: false,
      }),
    ]).start();
  };

  // Update elevation animation when prop changes
  useEffect(() => {
    if (!animated) return;
    
    Animated.timing(elevationAnim, {
      toValue: elevation,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [elevation, animated, elevationAnim]);

  // Get surface styles
  const getSurfaceStyles = () => {
    const backgroundColor = getSurfaceContainerColor(container, theme.isDark);
    
    if (animatedElevation) {
      // Use provided animated value
      return {
        backgroundColor,
        borderRadius,
        ...getAnimatedElevationStyle(animatedElevation, colorVariant),
      };
    } else if (animated) {
      // Use internal animated elevation
      return {
        backgroundColor,
        borderRadius,
        ...getAnimatedElevationStyle(elevationAnim, colorVariant),
      };
    } else {
      // Use static elevation
      return {
        backgroundColor,
        borderRadius,
        ...getElevationStyle(elevation, colorVariant),
      };
    }
  };

  const styles = StyleSheet.create({
    surface: {
      ...getSurfaceStyles(),
    },
    pressable: {
      opacity: disabled ? 0.6 : 1,
    },
  });

  const Component = animated ? Animated.View : View;
  const animatedStyle = animated ? { transform: [{ scale: scaleAnim }] } : {};

  if (onPress || onLongPress) {
    return (
      <Component
        {...props}
        style={[styles.surface, styles.pressable, animatedStyle, style]}
        onTouchStart={handlePressIn}
        onTouchEnd={handlePressOut}
        onTouchCancel={handlePressOut}
        onResponderGrant={handlePressIn}
        onResponderRelease={handlePressOut}
        onResponderTerminate={handlePressOut}
      >
        {children}
      </Component>
    );
  }

  return (
    <Component
      {...props}
      style={[styles.surface, animatedStyle, style]}
    >
      {children}
    </Component>
  );
};

export default ExpressiveSurface;
