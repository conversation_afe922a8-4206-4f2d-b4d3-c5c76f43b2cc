import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypography, ExpressiveTypographyVariants } from '../../constants/expressiveTheme';
import ExpressiveSurface from './ExpressiveSurface';
import {
  ElevationLevel,
  SurfaceContainer,
  ColoredElevation,
} from '../../constants/surfaceElevation';

// ===========================================
// 🃏 EXPRESSIVE CARD COMPONENT
// ===========================================

interface ExpressiveCardProps {
  children?: React.ReactNode;
  title?: string;
  subtitle?: string;
  description?: string;
  elevation?: ElevationLevel;
  container?: SurfaceContainer;
  colorVariant?: keyof typeof ColoredElevation;
  borderRadius?: number;
  padding?: number;
  onPress?: () => void;
  onLongPress?: () => void;
  disabled?: boolean;
  style?: ViewStyle;
  titleStyle?: TextStyle;
  subtitleStyle?: TextStyle;
  descriptionStyle?: TextStyle;
  headerAction?: React.ReactNode;
  footerAction?: React.ReactNode;
  variant?: 'elevated' | 'filled' | 'outlined';
}

export const ExpressiveCard: React.FC<ExpressiveCardProps> = ({
  children,
  title,
  subtitle,
  description,
  elevation = 1,
  container = 'surfaceContainer',
  colorVariant,
  borderRadius = 16,
  padding = 16,
  onPress,
  onLongPress,
  disabled = false,
  style,
  titleStyle,
  subtitleStyle,
  descriptionStyle,
  headerAction,
  footerAction,
  variant = 'elevated',
}) => {
  const theme = useDynamicTheme();

  const styles = StyleSheet.create({
    card: {
      overflow: 'hidden',
    },
    filledCard: {
      backgroundColor: theme.colors.surfaceContainerHigh,
      elevation: 0,
    },
    outlinedCard: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: theme.colors.outline,
      elevation: 0,
    },
    content: {
      padding,
    },
    header: {
      marginBottom: title && (subtitle || description) ? 8 : 0,
    },
    headerRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
    },
    headerText: {
      flex: 1,
      marginRight: headerAction ? 12 : 0,
    },
    title: {
      ...ExpressiveTypography.headlineSmall,
      color: theme.colors.onSurface,
      marginBottom: subtitle ? 4 : 0,
    },
    subtitle: {
      ...ExpressiveTypography.titleMedium,
      color: theme.colors.onSurfaceVariant,
      marginBottom: description ? 8 : 0,
    },
    description: {
      ...ExpressiveTypography.bodyMedium,
      color: theme.colors.onSurfaceVariant,
      lineHeight: 20,
    },
    body: {
      marginVertical: (title || subtitle || description) && children ? 12 : 0,
    },
    footer: {
      marginTop: footerAction ? 12 : 0,
    },
    pressable: {
      opacity: disabled ? 0.6 : 1,
    },
  });

  const getCardElevation = () => {
    switch (variant) {
      case 'filled':
      case 'outlined':
        return 0;
      default:
        return elevation;
    }
  };

  const getCardStyle = () => {
    switch (variant) {
      case 'filled':
        return styles.filledCard;
      case 'outlined':
        return styles.outlinedCard;
      default:
        return {};
    }
  };

  const renderHeader = () => {
    if (!title && !subtitle && !description && !headerAction) return null;

    return (
      <View style={styles.header}>
        <View style={styles.headerRow}>
          <View style={styles.headerText}>
            {title && (
              <Text style={[styles.title, titleStyle]}>
                {title}
              </Text>
            )}
            {subtitle && (
              <Text style={[styles.subtitle, subtitleStyle]}>
                {subtitle}
              </Text>
            )}
            {description && (
              <Text style={[styles.description, descriptionStyle]}>
                {description}
              </Text>
            )}
          </View>
          {headerAction}
        </View>
      </View>
    );
  };

  const renderContent = () => (
    <View style={styles.content}>
      {renderHeader()}
      {children && (
        <View style={styles.body}>
          {children}
        </View>
      )}
      {footerAction && (
        <View style={styles.footer}>
          {footerAction}
        </View>
      )}
    </View>
  );

  if (onPress || onLongPress) {
    return (
      <TouchableOpacity
        onPress={onPress}
        onLongPress={onLongPress}
        disabled={disabled}
        activeOpacity={0.95}
        style={[styles.pressable]}
      >
        <ExpressiveSurface
          elevation={getCardElevation()}
          container={container}
          colorVariant={colorVariant}
          borderRadius={borderRadius}
          animated={true}
          style={[styles.card, getCardStyle(), style]}
        >
          {renderContent()}
        </ExpressiveSurface>
      </TouchableOpacity>
    );
  }

  return (
    <ExpressiveSurface
      elevation={getCardElevation()}
      container={container}
      colorVariant={colorVariant}
      borderRadius={borderRadius}
      style={[styles.card, getCardStyle(), style]}
    >
      {renderContent()}
    </ExpressiveSurface>
  );
};

export default ExpressiveCard;
