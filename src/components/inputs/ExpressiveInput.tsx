import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  Animated,
  ViewStyle,
  TextStyle,
  TextInputProps,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypography, ExpressiveTypographyVariants } from '../../constants/expressiveTheme';
import { ExpressiveEasing, ExpressiveDurations } from '../../constants/expressiveMotion';

// ===========================================
// 📝 EXPRESSIVE INPUT COMPONENT
// ===========================================

export type InputVariant = 'filled' | 'outlined';
export type InputSize = 'small' | 'medium' | 'large';

interface ExpressiveInputProps extends Omit<TextInputProps, 'style'> {
  label?: string;
  helperText?: string;
  errorText?: string;
  variant?: InputVariant;
  size?: InputSize;
  disabled?: boolean;
  required?: boolean;
  leadingIcon?: React.ReactNode;
  trailingIcon?: React.ReactNode;
  onFocus?: () => void;
  onBlur?: () => void;
  style?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
  helperTextStyle?: TextStyle;
  borderRadius?: number;
}

export const ExpressiveInput: React.FC<ExpressiveInputProps> = ({
  label,
  helperText,
  errorText,
  variant = 'outlined',
  size = 'medium',
  disabled = false,
  required = false,
  leadingIcon,
  trailingIcon,
  onFocus,
  onBlur,
  style,
  inputStyle,
  labelStyle,
  helperTextStyle,
  borderRadius = 12,
  value,
  placeholder,
  ...textInputProps
}) => {
  const theme = useDynamicTheme();
  const [isFocused, setIsFocused] = useState(false);
  const [hasValue, setHasValue] = useState(!!value);
  
  const labelAnim = useRef(new Animated.Value(hasValue || !!placeholder ? 1 : 0)).current;
  const borderAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  // Update hasValue when value prop changes
  useEffect(() => {
    setHasValue(!!value);
  }, [value]);

  // Animate label position
  useEffect(() => {
    Animated.timing(labelAnim, {
      toValue: isFocused || hasValue || !!placeholder ? 1 : 0,
      duration: ExpressiveDurations.short,
      easing: ExpressiveEasing.standard,
      useNativeDriver: false,
    }).start();
  }, [isFocused, hasValue, placeholder, labelAnim]);

  // Animate border and scale
  useEffect(() => {
    Animated.parallel([
      Animated.timing(borderAnim, {
        toValue: isFocused ? 1 : 0,
        duration: ExpressiveDurations.short,
        easing: ExpressiveEasing.standard,
        useNativeDriver: false,
      }),
      Animated.timing(scaleAnim, {
        toValue: isFocused ? 1.02 : 1,
        duration: ExpressiveDurations.short,
        easing: ExpressiveEasing.gentle,
        useNativeDriver: true,
      }),
    ]).start();
  }, [isFocused, borderAnim, scaleAnim]);

  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
  };

  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
  };

  const handleChangeText = (text: string) => {
    setHasValue(!!text);
    textInputProps.onChangeText?.(text);
  };

  // Get input colors based on state
  const getInputColors = () => {
    const colors = theme.colors;
    const hasError = !!errorText;
    
    if (disabled) {
      return {
        backgroundColor: variant === 'filled' ? colors.surfaceDisabled : 'transparent',
        borderColor: colors.outline,
        textColor: colors.onSurfaceDisabled,
        labelColor: colors.onSurfaceDisabled,
        placeholderColor: colors.onSurfaceDisabled,
      };
    }
    
    if (hasError) {
      return {
        backgroundColor: variant === 'filled' ? colors.errorContainer : 'transparent',
        borderColor: colors.error,
        textColor: colors.onSurface,
        labelColor: colors.error,
        placeholderColor: colors.onSurfaceVariant,
      };
    }
    
    if (isFocused) {
      return {
        backgroundColor: variant === 'filled' ? colors.surfaceContainerHigh : 'transparent',
        borderColor: colors.primary,
        textColor: colors.onSurface,
        labelColor: colors.primary,
        placeholderColor: colors.onSurfaceVariant,
      };
    }
    
    return {
      backgroundColor: variant === 'filled' ? colors.surfaceContainer : 'transparent',
      borderColor: colors.outline,
      textColor: colors.onSurface,
      labelColor: colors.onSurfaceVariant,
      placeholderColor: colors.onSurfaceVariant,
    };
  };

  // Get size styles
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          height: 40,
          paddingHorizontal: 12,
          fontSize: 14,
          labelFontSize: 12,
        };
      
      case 'large':
        return {
          height: 64,
          paddingHorizontal: 20,
          fontSize: 18,
          labelFontSize: 16,
        };
      
      default: // medium
        return {
          height: 52,
          paddingHorizontal: 16,
          fontSize: 16,
          labelFontSize: 14,
        };
    }
  };

  const inputColors = getInputColors();
  const sizeStyles = getSizeStyles();

  const styles = StyleSheet.create({
    container: {
      marginBottom: 4,
    },
    inputContainer: {
      position: 'relative',
      borderRadius,
      backgroundColor: inputColors.backgroundColor,
      borderWidth: variant === 'outlined' ? 1 : 0,
      borderColor: inputColors.borderColor,
      height: sizeStyles.height,
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: sizeStyles.paddingHorizontal,
    },
    focusedBorder: {
      borderWidth: variant === 'outlined' ? 2 : 0,
    },
    labelContainer: {
      position: 'absolute',
      left: leadingIcon ? sizeStyles.paddingHorizontal + 32 : sizeStyles.paddingHorizontal,
      backgroundColor: variant === 'outlined' ? theme.colors.background : 'transparent',
      paddingHorizontal: variant === 'outlined' ? 4 : 0,
      zIndex: 1,
    },
    label: {
      ...ExpressiveTypographyVariants.inputLabel,
      color: inputColors.labelColor,
      fontSize: sizeStyles.labelFontSize,
    },
    requiredIndicator: {
      color: theme.colors.error,
      marginLeft: 2,
    },
    leadingIcon: {
      marginRight: 12,
      alignItems: 'center',
      justifyContent: 'center',
    },
    input: {
      flex: 1,
      ...ExpressiveTypographyVariants.inputText,
      color: inputColors.textColor,
      fontSize: sizeStyles.fontSize,
      paddingVertical: 0,
      textAlignVertical: 'center',
    },
    trailingIcon: {
      marginLeft: 12,
      alignItems: 'center',
      justifyContent: 'center',
    },
    helperTextContainer: {
      marginTop: 4,
      paddingHorizontal: sizeStyles.paddingHorizontal,
    },
    helperText: {
      ...ExpressiveTypographyVariants.helperText,
      color: errorText ? theme.colors.error : theme.colors.onSurfaceVariant,
    },
  });

  const labelTop = labelAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [sizeStyles.height / 2 - sizeStyles.labelFontSize / 2, -sizeStyles.labelFontSize / 2],
  });

  const labelScale = labelAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 0.85],
  });

  const animatedBorderColor = borderAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [inputColors.borderColor, inputColors.borderColor],
  });

  return (
    <Animated.View style={[styles.container, { transform: [{ scale: scaleAnim }] }, style]}>
      <Animated.View
        style={[
          styles.inputContainer,
          isFocused && styles.focusedBorder,
          variant === 'outlined' && {
            borderColor: animatedBorderColor,
          },
        ]}
      >
        {/* Floating Label */}
        {label && (
          <Animated.View
            style={[
              styles.labelContainer,
              {
                top: labelTop,
                transform: [{ scale: labelScale }],
              },
            ]}
          >
            <Text style={[styles.label, labelStyle]}>
              {label}
              {required && <Text style={styles.requiredIndicator}>*</Text>}
            </Text>
          </Animated.View>
        )}

        {/* Leading Icon */}
        {leadingIcon && (
          <View style={styles.leadingIcon}>
            {leadingIcon}
          </View>
        )}

        {/* Text Input */}
        <TextInput
          {...textInputProps}
          style={[styles.input, inputStyle]}
          value={value}
          placeholder={isFocused || !label ? placeholder : undefined}
          placeholderTextColor={inputColors.placeholderColor}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onChangeText={handleChangeText}
          editable={!disabled}
          selectTextOnFocus={!disabled}
        />

        {/* Trailing Icon */}
        {trailingIcon && (
          <View style={styles.trailingIcon}>
            {trailingIcon}
          </View>
        )}
      </Animated.View>

      {/* Helper Text */}
      {(helperText || errorText) && (
        <View style={styles.helperTextContainer}>
          <Text style={[styles.helperText, helperTextStyle]}>
            {errorText || helperText}
          </Text>
        </View>
      )}
    </Animated.View>
  );
};

export default ExpressiveInput;
