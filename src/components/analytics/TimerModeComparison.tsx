import React, { useMemo, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {
  <PERSON><PERSON>hart,
  VictoryBar,
  VictoryLine,
  VictoryArea,
  VictoryAxis,
  VictoryTheme,
  VictoryTooltip,
  VictoryContainer,
  VictoryScatter,
} from 'victory-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../../constants/expressiveTheme';
import { StudySession } from '../../stores/analyticsStore';
import ExpressiveCard from '../surfaces/ExpressiveCard';

const { width: screenWidth } = Dimensions.get('window');

interface TimerModeComparisonProps {
  sessions: StudySession[];
  chartType?: 'bar' | 'line' | 'area';
  showProductivity?: boolean;
  onModePress?: (mode: 'pomodoro' | 'stopwatch') => void;
}

interface ModeStats {
  mode: 'pomodoro' | 'stopwatch';
  totalTime: number;
  totalSessions: number;
  completedSessions: number;
  averageSessionLength: number;
  averageProductivity: number;
  completionRate: number;
  totalBreakTime: number;
  averageBreakLength: number;
}

export const TimerModeComparison: React.FC<TimerModeComparisonProps> = ({
  sessions,
  chartType = 'bar',
  showProductivity = true,
  onModePress,
}) => {
  const theme = useDynamicTheme();
  const [selectedMetric, setSelectedMetric] = useState<'time' | 'sessions' | 'productivity' | 'completion'>('time');

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
    },
    title: {
      ...ExpressiveTypography.titleMedium,
      color: theme.colors.onSurface,
    },
    controls: {
      flexDirection: 'row',
      gap: 8,
    },
    controlGroup: {
      flexDirection: 'row',
      backgroundColor: theme.colors.surfaceContainer,
      borderRadius: 16,
      padding: 2,
    },
    controlButton: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 14,
      minWidth: 50,
      alignItems: 'center',
    },
    activeControlButton: {
      backgroundColor: theme.colors.primary,
    },
    controlButtonText: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onSurfaceVariant,
    },
    activeControlButtonText: {
      color: theme.colors.onPrimary,
      fontWeight: '600',
    },
    metricsSelector: {
      flexDirection: 'row',
      paddingHorizontal: 16,
      paddingVertical: 12,
      gap: 8,
      backgroundColor: theme.colors.surfaceContainer,
    },
    metricButton: {
      flex: 1,
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 12,
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.outline,
      alignItems: 'center',
    },
    selectedMetricButton: {
      backgroundColor: theme.colors.primaryContainer,
      borderColor: theme.colors.primary,
    },
    metricButtonText: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onSurface,
      textAlign: 'center',
    },
    selectedMetricButtonText: {
      color: theme.colors.onPrimaryContainer,
      fontWeight: '600',
    },
    chartContainer: {
      alignItems: 'center',
      paddingVertical: 16,
    },
    comparisonContainer: {
      flexDirection: 'row',
      paddingHorizontal: 16,
      paddingVertical: 16,
      gap: 12,
    },
    modeCard: {
      flex: 1,
      padding: 16,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.outline,
    },
    pomodoroCard: {
      backgroundColor: theme.colors.primaryContainer,
      borderColor: theme.colors.primary,
    },
    stopwatchCard: {
      backgroundColor: theme.colors.secondaryContainer,
      borderColor: theme.colors.secondary,
    },
    modeTitle: {
      ...ExpressiveTypography.titleSmall,
      fontWeight: '600',
      marginBottom: 8,
      textAlign: 'center',
    },
    pomodoroTitle: {
      color: theme.colors.onPrimaryContainer,
    },
    stopwatchTitle: {
      color: theme.colors.onSecondaryContainer,
    },
    statRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 6,
    },
    statLabel: {
      ...ExpressiveTypography.bodySmall,
      color: theme.colors.onSurfaceVariant,
    },
    statValue: {
      ...ExpressiveTypography.bodySmall,
      fontWeight: '600',
    },
    pomodoroStatValue: {
      color: theme.colors.onPrimaryContainer,
    },
    stopwatchStatValue: {
      color: theme.colors.onSecondaryContainer,
    },
    winnerBadge: {
      backgroundColor: theme.colors.tertiary,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      alignSelf: 'center',
      marginTop: 8,
    },
    winnerText: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onTertiary,
      fontWeight: '600',
    },
    emptyState: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 48,
      paddingHorizontal: 32,
    },
    emptyText: {
      ...ExpressiveTypography.bodyLarge,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
    },
    insightContainer: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: theme.colors.tertiaryContainer,
      borderTopWidth: 1,
      borderTopColor: theme.colors.outline,
    },
    insightText: {
      ...ExpressiveTypography.bodyMedium,
      color: theme.colors.onTertiaryContainer,
      textAlign: 'center',
      lineHeight: 20,
    },
  });

  // Calculate statistics for each mode
  const modeStats = useMemo((): { pomodoro: ModeStats; stopwatch: ModeStats } => {
    const pomodoroSessions = sessions.filter(s => s.mode === 'pomodoro');
    const stopwatchSessions = sessions.filter(s => s.mode === 'stopwatch');

    const calculateStats = (modeSessions: StudySession[], mode: 'pomodoro' | 'stopwatch'): ModeStats => {
      if (modeSessions.length === 0) {
        return {
          mode,
          totalTime: 0,
          totalSessions: 0,
          completedSessions: 0,
          averageSessionLength: 0,
          averageProductivity: 0,
          completionRate: 0,
          totalBreakTime: 0,
          averageBreakLength: 0,
        };
      }

      const totalTime = modeSessions.reduce((sum, s) => sum + s.duration, 0);
      const completedSessions = modeSessions.filter(s => s.completed).length;
      const averageSessionLength = totalTime / modeSessions.length;
      
      const productivityRatings = modeSessions
        .filter(s => s.productivity_rating !== null)
        .map(s => s.productivity_rating!);
      const averageProductivity = productivityRatings.length > 0 
        ? productivityRatings.reduce((sum, r) => sum + r, 0) / productivityRatings.length 
        : 0;
      
      const completionRate = (completedSessions / modeSessions.length) * 100;

      // Calculate break time (for Pomodoro sessions)
      const breakSessions = modeSessions.filter(s => s.phase === 'shortBreak' || s.phase === 'longBreak');
      const totalBreakTime = breakSessions.reduce((sum, s) => sum + s.duration, 0);
      const averageBreakLength = breakSessions.length > 0 ? totalBreakTime / breakSessions.length : 0;

      return {
        mode,
        totalTime,
        totalSessions: modeSessions.length,
        completedSessions,
        averageSessionLength,
        averageProductivity,
        completionRate,
        totalBreakTime,
        averageBreakLength,
      };
    };

    return {
      pomodoro: calculateStats(pomodoroSessions, 'pomodoro'),
      stopwatch: calculateStats(stopwatchSessions, 'stopwatch'),
    };
  }, [sessions]);

  // Prepare chart data based on selected metric
  const chartData = useMemo(() => {
    const { pomodoro, stopwatch } = modeStats;
    
    let pomodoroValue: number;
    let stopwatchValue: number;
    let unit: string;

    switch (selectedMetric) {
      case 'time':
        pomodoroValue = pomodoro.totalTime / 3600; // Convert to hours
        stopwatchValue = stopwatch.totalTime / 3600;
        unit = 'hours';
        break;
      case 'sessions':
        pomodoroValue = pomodoro.totalSessions;
        stopwatchValue = stopwatch.totalSessions;
        unit = 'sessions';
        break;
      case 'productivity':
        pomodoroValue = pomodoro.averageProductivity;
        stopwatchValue = stopwatch.averageProductivity;
        unit = 'rating';
        break;
      case 'completion':
        pomodoroValue = pomodoro.completionRate;
        stopwatchValue = stopwatch.completionRate;
        unit = '%';
        break;
      default:
        pomodoroValue = 0;
        stopwatchValue = 0;
        unit = '';
    }

    return [
      {
        x: 'Pomodoro',
        y: pomodoroValue,
        fill: theme.colors.primary,
        label: `${pomodoroValue.toFixed(1)} ${unit}`,
      },
      {
        x: 'Stopwatch',
        y: stopwatchValue,
        fill: theme.colors.secondary,
        label: `${stopwatchValue.toFixed(1)} ${unit}`,
      },
    ];
  }, [modeStats, selectedMetric, theme]);

  // Format time duration
  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  // Get insights based on the data
  const getInsight = (): string => {
    const { pomodoro, stopwatch } = modeStats;
    
    if (pomodoro.totalSessions === 0 && stopwatch.totalSessions === 0) {
      return "Start using both timer modes to see which works better for you!";
    }
    
    if (pomodoro.totalSessions === 0) {
      return "Try using Pomodoro technique for structured study sessions with breaks.";
    }
    
    if (stopwatch.totalSessions === 0) {
      return "Try using Stopwatch mode for flexible, uninterrupted study sessions.";
    }

    const pomodoroEfficiency = pomodoro.averageProductivity * pomodoro.completionRate / 100;
    const stopwatchEfficiency = stopwatch.averageProductivity * stopwatch.completionRate / 100;

    if (pomodoroEfficiency > stopwatchEfficiency) {
      return "Pomodoro technique seems more effective for you! The structured breaks help maintain focus.";
    } else if (stopwatchEfficiency > pomodoroEfficiency) {
      return "Stopwatch mode works better for you! You prefer longer, uninterrupted focus sessions.";
    } else {
      return "Both modes work equally well for you! Use Pomodoro for structured tasks and Stopwatch for deep work.";
    }
  };

  // Determine winner for each metric
  const getWinner = (metric: keyof ModeStats): 'pomodoro' | 'stopwatch' | 'tie' => {
    const pomodoroValue = modeStats.pomodoro[metric] as number;
    const stopwatchValue = modeStats.stopwatch[metric] as number;
    
    if (pomodoroValue > stopwatchValue) return 'pomodoro';
    if (stopwatchValue > pomodoroValue) return 'stopwatch';
    return 'tie';
  };

  // Render empty state
  if (sessions.length === 0) {
    return (
      <ExpressiveCard style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Timer Mode Comparison</Text>
        </View>
        <View style={styles.emptyState}>
          <Text style={styles.emptyText}>
            Start using both Pomodoro and Stopwatch modes to see which works better for you!
          </Text>
        </View>
      </ExpressiveCard>
    );
  }

  // Render chart
  const renderChart = () => {
    const ChartComponent = chartType === 'line' ? VictoryLine : chartType === 'area' ? VictoryArea : VictoryBar;

    return (
      <VictoryChart
        theme={VictoryTheme.material}
        width={screenWidth - 32}
        height={280}
        domainPadding={{ x: 50 }}
        padding={{ left: 60, top: 20, right: 40, bottom: 60 }}
        containerComponent={<VictoryContainer responsive={false} />}
      >
        <VictoryAxis
          dependentAxis
          style={{
            tickLabels: {
              fontSize: 10,
              fill: theme.colors.onSurfaceVariant,
            },
            grid: {
              stroke: theme.colors.outline,
              strokeWidth: 0.5,
            },
          }}
        />
        <VictoryAxis
          style={{
            tickLabels: {
              fontSize: 12,
              fill: theme.colors.onSurfaceVariant,
            },
          }}
        />
        <ChartComponent
          data={chartData}
          style={{
            data: {
              fill: ({ datum }) => datum.fill,
              fillOpacity: chartType === 'area' ? 0.3 : undefined,
              stroke: chartType === 'line' ? ({ datum }) => datum.fill : undefined,
              strokeWidth: chartType === 'line' ? 3 : undefined,
            },
          }}
          labelComponent={<VictoryTooltip />}
          animate={{
            duration: 1000,
            onLoad: { duration: 500 },
          }}
        />
        {chartType === 'line' && (
          <VictoryScatter
            data={chartData}
            size={6}
            style={{
              data: { fill: ({ datum }) => datum.fill },
            }}
          />
        )}
      </VictoryChart>
    );
  };

  return (
    <ExpressiveCard style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Timer Mode Comparison</Text>
        <View style={styles.controls}>
          <View style={styles.controlGroup}>
            {(['bar', 'line', 'area'] as const).map((type) => (
              <TouchableOpacity
                key={type}
                style={[
                  styles.controlButton,
                  chartType === type && styles.activeControlButton,
                ]}
                onPress={() => {/* Chart type change handled by parent */}}
              >
                <Text
                  style={[
                    styles.controlButtonText,
                    chartType === type && styles.activeControlButtonText,
                  ]}
                >
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>

      {/* Metrics Selector */}
      <View style={styles.metricsSelector}>
        {([
          { key: 'time', label: 'Time' },
          { key: 'sessions', label: 'Sessions' },
          { key: 'productivity', label: 'Rating' },
          { key: 'completion', label: 'Completion' },
        ] as const).map(({ key, label }) => (
          <TouchableOpacity
            key={key}
            style={[
              styles.metricButton,
              selectedMetric === key && styles.selectedMetricButton,
            ]}
            onPress={() => setSelectedMetric(key)}
          >
            <Text
              style={[
                styles.metricButtonText,
                selectedMetric === key && styles.selectedMetricButtonText,
              ]}
            >
              {label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Chart */}
      <View style={styles.chartContainer}>
        {renderChart()}
      </View>

      {/* Detailed Comparison */}
      <View style={styles.comparisonContainer}>
        {/* Pomodoro Stats */}
        <TouchableOpacity
          style={[styles.modeCard, styles.pomodoroCard]}
          onPress={() => onModePress?.('pomodoro')}
          activeOpacity={0.7}
        >
          <Text style={[styles.modeTitle, styles.pomodoroTitle]}>🍅 Pomodoro</Text>
          
          <View style={styles.statRow}>
            <Text style={styles.statLabel}>Total Time</Text>
            <Text style={[styles.statValue, styles.pomodoroStatValue]}>
              {formatDuration(modeStats.pomodoro.totalTime)}
            </Text>
          </View>
          
          <View style={styles.statRow}>
            <Text style={styles.statLabel}>Sessions</Text>
            <Text style={[styles.statValue, styles.pomodoroStatValue]}>
              {modeStats.pomodoro.totalSessions}
            </Text>
          </View>
          
          <View style={styles.statRow}>
            <Text style={styles.statLabel}>Completion</Text>
            <Text style={[styles.statValue, styles.pomodoroStatValue]}>
              {modeStats.pomodoro.completionRate.toFixed(1)}%
            </Text>
          </View>
          
          <View style={styles.statRow}>
            <Text style={styles.statLabel}>Avg Rating</Text>
            <Text style={[styles.statValue, styles.pomodoroStatValue]}>
              {modeStats.pomodoro.averageProductivity.toFixed(1)}⭐
            </Text>
          </View>

          {getWinner('averageProductivity') === 'pomodoro' && (
            <View style={styles.winnerBadge}>
              <Text style={styles.winnerText}>🏆 Winner</Text>
            </View>
          )}
        </TouchableOpacity>

        {/* Stopwatch Stats */}
        <TouchableOpacity
          style={[styles.modeCard, styles.stopwatchCard]}
          onPress={() => onModePress?.('stopwatch')}
          activeOpacity={0.7}
        >
          <Text style={[styles.modeTitle, styles.stopwatchTitle]}>⏱️ Stopwatch</Text>
          
          <View style={styles.statRow}>
            <Text style={styles.statLabel}>Total Time</Text>
            <Text style={[styles.statValue, styles.stopwatchStatValue]}>
              {formatDuration(modeStats.stopwatch.totalTime)}
            </Text>
          </View>
          
          <View style={styles.statRow}>
            <Text style={styles.statLabel}>Sessions</Text>
            <Text style={[styles.statValue, styles.stopwatchStatValue]}>
              {modeStats.stopwatch.totalSessions}
            </Text>
          </View>
          
          <View style={styles.statRow}>
            <Text style={styles.statLabel}>Completion</Text>
            <Text style={[styles.statValue, styles.stopwatchStatValue]}>
              {modeStats.stopwatch.completionRate.toFixed(1)}%
            </Text>
          </View>
          
          <View style={styles.statRow}>
            <Text style={styles.statLabel}>Avg Rating</Text>
            <Text style={[styles.statValue, styles.stopwatchStatValue]}>
              {modeStats.stopwatch.averageProductivity.toFixed(1)}⭐
            </Text>
          </View>

          {getWinner('averageProductivity') === 'stopwatch' && (
            <View style={styles.winnerBadge}>
              <Text style={styles.winnerText}>🏆 Winner</Text>
            </View>
          )}
        </TouchableOpacity>
      </View>

      {/* Insights */}
      <View style={styles.insightContainer}>
        <Text style={styles.insightText}>{getInsight()}</Text>
      </View>
    </ExpressiveCard>
  );
};

export default TimerModeComparison;
