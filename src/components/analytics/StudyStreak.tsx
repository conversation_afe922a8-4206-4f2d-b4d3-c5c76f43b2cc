import React, { useEffect, useRef, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../../constants/expressiveTheme';
import ExpressiveCard from '../surfaces/ExpressiveCard';

const { width: screenWidth } = Dimensions.get('window');

interface StudyStreakProps {
  currentStreak: number;
  longestStreak: number;
  streakData?: { date: string; hasSession: boolean }[];
  showAnimation?: boolean;
}

export const StudyStreak: React.FC<StudyStreakProps> = ({
  currentStreak,
  longestStreak,
  streakData = [],
  showAnimation = true,
}) => {
  const theme = useDynamicTheme();
  
  // Animation values
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const streakCountAnim = useRef(new Animated.Value(0)).current;
  const fireAnimations = useRef(
    Array.from({ length: 7 }, () => new Animated.Value(0))
  ).current;

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
    },
    header: {
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 20,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
    },
    streakIcon: {
      fontSize: 48,
      marginBottom: 8,
    },
    currentStreakContainer: {
      alignItems: 'center',
      marginBottom: 16,
    },
    currentStreakNumber: {
      ...ExpressiveTypography.displayMedium,
      color: theme.colors.primary,
      fontWeight: '700',
      textAlign: 'center',
    },
    currentStreakLabel: {
      ...ExpressiveTypography.titleMedium,
      color: theme.colors.onSurface,
      textAlign: 'center',
      marginTop: 4,
    },
    currentStreakSubtitle: {
      ...ExpressiveTypography.bodyMedium,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginTop: 2,
    },
    statsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingHorizontal: 16,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
    },
    statItem: {
      alignItems: 'center',
      flex: 1,
    },
    statValue: {
      ...ExpressiveTypography.headlineSmall,
      color: theme.colors.secondary,
      fontWeight: '600',
    },
    statLabel: {
      ...ExpressiveTypography.labelMedium,
      color: theme.colors.onSurfaceVariant,
      marginTop: 4,
    },
    calendarContainer: {
      paddingHorizontal: 16,
      paddingVertical: 16,
    },
    calendarTitle: {
      ...ExpressiveTypography.titleSmall,
      color: theme.colors.onSurface,
      marginBottom: 12,
      textAlign: 'center',
    },
    calendarGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'center',
      gap: 4,
    },
    dayContainer: {
      width: 32,
      height: 32,
      borderRadius: 16,
      justifyContent: 'center',
      alignItems: 'center',
      margin: 2,
    },
    dayWithSession: {
      backgroundColor: theme.colors.primary,
    },
    dayWithoutSession: {
      backgroundColor: theme.colors.surfaceVariant,
    },
    todayContainer: {
      borderWidth: 2,
      borderColor: theme.colors.secondary,
    },
    dayText: {
      ...ExpressiveTypography.labelSmall,
      fontWeight: '600',
    },
    dayTextWithSession: {
      color: theme.colors.onPrimary,
    },
    dayTextWithoutSession: {
      color: theme.colors.onSurfaceVariant,
    },
    motivationContainer: {
      paddingHorizontal: 16,
      paddingVertical: 16,
      backgroundColor: theme.colors.primaryContainer,
      borderTopWidth: 1,
      borderTopColor: theme.colors.outline,
    },
    motivationText: {
      ...ExpressiveTypography.bodyMedium,
      color: theme.colors.onPrimaryContainer,
      textAlign: 'center',
      lineHeight: 20,
    },
    achievementBadge: {
      backgroundColor: theme.colors.tertiaryContainer,
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      alignSelf: 'center',
      marginTop: 8,
    },
    achievementText: {
      ...ExpressiveTypography.labelMedium,
      color: theme.colors.onTertiaryContainer,
      fontWeight: '600',
    },
    fireContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 8,
    },
    fireEmoji: {
      fontSize: 20,
      marginHorizontal: 2,
    },
  });

  // Get motivation message based on streak
  const getMotivationMessage = (streak: number): string => {
    if (streak === 0) {
      return "Start your study journey today! Every expert was once a beginner.";
    } else if (streak === 1) {
      return "Great start! One day down, keep the momentum going!";
    } else if (streak < 7) {
      return `${streak} days strong! You're building a powerful habit.`;
    } else if (streak < 30) {
      return `Amazing ${streak}-day streak! You're on fire! 🔥`;
    } else if (streak < 100) {
      return `Incredible ${streak}-day streak! You're a study champion!`;
    } else {
      return `Legendary ${streak}-day streak! You're an inspiration!`;
    }
  };

  // Get achievement level
  const getAchievementLevel = (streak: number): string | null => {
    if (streak >= 100) return "🏆 Century Club";
    if (streak >= 50) return "💎 Diamond Streak";
    if (streak >= 30) return "🥇 Gold Streak";
    if (streak >= 14) return "🥈 Silver Streak";
    if (streak >= 7) return "🥉 Bronze Streak";
    if (streak >= 3) return "⭐ Rising Star";
    return null;
  };

  // Generate last 30 days data if not provided
  const last30Days = useMemo(() => {
    if (streakData.length > 0) {
      return streakData.slice(-30);
    }

    // Generate mock data for last 30 days
    const days = [];
    const today = new Date();
    
    for (let i = 29; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      
      // Mock logic: assume sessions exist for current streak days
      const daysSinceToday = i;
      const hasSession = daysSinceToday < currentStreak;
      
      days.push({
        date: date.toISOString().split('T')[0],
        hasSession,
      });
    }
    
    return days;
  }, [streakData, currentStreak]);

  // Animation effects
  useEffect(() => {
    if (showAnimation) {
      // Main container animation
      Animated.parallel([
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ]).start();

      // Streak counter animation
      Animated.timing(streakCountAnim, {
        toValue: currentStreak,
        duration: 1500,
        useNativeDriver: false,
      }).start();

      // Fire animations for high streaks
      if (currentStreak >= 7) {
        const fireAnimationSequence = fireAnimations.map((anim, index) =>
          Animated.sequence([
            Animated.delay(index * 100),
            Animated.loop(
              Animated.sequence([
                Animated.timing(anim, {
                  toValue: 1,
                  duration: 300,
                  useNativeDriver: true,
                }),
                Animated.timing(anim, {
                  toValue: 0.7,
                  duration: 300,
                  useNativeDriver: true,
                }),
              ])
            ),
          ])
        );

        Animated.parallel(fireAnimationSequence).start();
      }
    } else {
      scaleAnim.setValue(1);
      fadeAnim.setValue(1);
      streakCountAnim.setValue(currentStreak);
    }
  }, [currentStreak, showAnimation]);

  // Render calendar day
  const renderDay = (dayData: { date: string; hasSession: boolean }, index: number) => {
    const date = new Date(dayData.date);
    const dayNumber = date.getDate();
    const isToday = dayData.date === new Date().toISOString().split('T')[0];

    return (
      <View
        key={dayData.date}
        style={[
          styles.dayContainer,
          dayData.hasSession ? styles.dayWithSession : styles.dayWithoutSession,
          isToday && styles.todayContainer,
        ]}
      >
        <Text
          style={[
            styles.dayText,
            dayData.hasSession ? styles.dayTextWithSession : styles.dayTextWithoutSession,
          ]}
        >
          {dayNumber}
        </Text>
      </View>
    );
  };

  // Render fire animation
  const renderFireAnimation = () => {
    if (currentStreak < 7) return null;

    const fireCount = Math.min(Math.floor(currentStreak / 7), 7);
    
    return (
      <View style={styles.fireContainer}>
        {Array.from({ length: fireCount }, (_, index) => (
          <Animated.Text
            key={index}
            style={[
              styles.fireEmoji,
              {
                opacity: fireAnimations[index],
                transform: [
                  {
                    scale: fireAnimations[index].interpolate({
                      inputRange: [0, 1],
                      outputRange: [0.8, 1.2],
                    }),
                  },
                ],
              },
            ]}
          >
            🔥
          </Animated.Text>
        ))}
      </View>
    );
  };

  const achievementLevel = getAchievementLevel(currentStreak);
  const motivationMessage = getMotivationMessage(currentStreak);

  return (
    <Animated.View
      style={[
        {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        },
      ]}
    >
      <ExpressiveCard style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.streakIcon}>
            {currentStreak > 0 ? '🔥' : '📚'}
          </Text>
          
          <View style={styles.currentStreakContainer}>
            <Animated.Text style={styles.currentStreakNumber}>
              {streakCountAnim}
            </Animated.Text>
            <Text style={styles.currentStreakLabel}>
              {currentStreak === 1 ? 'Day Streak' : 'Days Streak'}
            </Text>
            <Text style={styles.currentStreakSubtitle}>
              Keep it going!
            </Text>
          </View>

          {renderFireAnimation()}
        </View>

        {/* Statistics */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{longestStreak}</Text>
            <Text style={styles.statLabel}>Best Streak</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {last30Days.filter(day => day.hasSession).length}
            </Text>
            <Text style={styles.statLabel}>Days This Month</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {Math.round((last30Days.filter(day => day.hasSession).length / 30) * 100)}%
            </Text>
            <Text style={styles.statLabel}>Consistency</Text>
          </View>
        </View>

        {/* Calendar */}
        <View style={styles.calendarContainer}>
          <Text style={styles.calendarTitle}>Last 30 Days</Text>
          <View style={styles.calendarGrid}>
            {last30Days.map(renderDay)}
          </View>
        </View>

        {/* Motivation */}
        <View style={styles.motivationContainer}>
          <Text style={styles.motivationText}>{motivationMessage}</Text>
          
          {achievementLevel && (
            <View style={styles.achievementBadge}>
              <Text style={styles.achievementText}>{achievementLevel}</Text>
            </View>
          )}
        </View>
      </ExpressiveCard>
    </Animated.View>
  );
};

export default StudyStreak;
