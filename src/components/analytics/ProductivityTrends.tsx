import React, { useMemo, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {
  VictoryLine,
  VictoryChart,
  VictoryAxis,
  VictoryTheme,
  VictoryScatter,
  VictoryArea,
  VictoryTooltip,
  VictoryContainer,
  VictoryLabel,
} from 'victory-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../../constants/expressiveTheme';
import { ProductivityTrend } from '../../stores/analyticsStore';
import ExpressiveCard from '../surfaces/ExpressiveCard';

const { width: screenWidth } = Dimensions.get('window');

interface ProductivityTrendsProps {
  data: ProductivityTrend[];
  chartType?: 'line' | 'area';
  showAverage?: boolean;
  showDataPoints?: boolean;
  timeRange?: 'day' | 'week' | 'month';
}

export const ProductivityTrends: React.FC<ProductivityTrendsProps> = ({
  data,
  chartType = 'line',
  showAverage = true,
  showDataPoints = true,
  timeRange = 'week',
}) => {
  const theme = useDynamicTheme();
  const [selectedPoint, setSelectedPoint] = useState<ProductivityTrend | null>(null);

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
    },
    title: {
      ...ExpressiveTypography.titleMedium,
      color: theme.colors.onSurface,
    },
    chartTypeToggle: {
      flexDirection: 'row',
      backgroundColor: theme.colors.surfaceContainer,
      borderRadius: 16,
      padding: 2,
    },
    toggleButton: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 14,
      minWidth: 50,
      alignItems: 'center',
    },
    activeToggleButton: {
      backgroundColor: theme.colors.primary,
    },
    toggleButtonText: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onSurfaceVariant,
    },
    activeToggleButtonText: {
      color: theme.colors.onPrimary,
      fontWeight: '600',
    },
    chartContainer: {
      alignItems: 'center',
      paddingVertical: 16,
    },
    statsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderTopWidth: 1,
      borderTopColor: theme.colors.outline,
    },
    statItem: {
      alignItems: 'center',
    },
    statValue: {
      ...ExpressiveTypography.titleMedium,
      color: theme.colors.primary,
      fontWeight: '600',
    },
    statLabel: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onSurfaceVariant,
      marginTop: 2,
    },
    emptyState: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 48,
      paddingHorizontal: 32,
    },
    emptyText: {
      ...ExpressiveTypography.bodyLarge,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
    },
    selectedPointInfo: {
      backgroundColor: theme.colors.primaryContainer,
      paddingHorizontal: 16,
      paddingVertical: 12,
      marginHorizontal: 16,
      marginBottom: 16,
      borderRadius: 12,
    },
    selectedPointDate: {
      ...ExpressiveTypography.titleSmall,
      color: theme.colors.onPrimaryContainer,
      marginBottom: 4,
    },
    selectedPointStats: {
      ...ExpressiveTypography.bodyMedium,
      color: theme.colors.onPrimaryContainer,
    },
  });

  // Format date based on time range
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    
    switch (timeRange) {
      case 'day':
        return date.toLocaleDateString('en-US', { 
          month: 'short', 
          day: 'numeric',
          hour: 'numeric',
        });
      case 'week':
        return date.toLocaleDateString('en-US', { 
          month: 'short', 
          day: 'numeric' 
        });
      case 'month':
        return date.toLocaleDateString('en-US', { 
          month: 'short', 
          year: 'numeric' 
        });
      default:
        return date.toLocaleDateString();
    }
  };

  // Prepare chart data
  const chartData = useMemo(() => {
    return data.map((item, index) => ({
      x: index + 1,
      y: item.rating,
      date: item.date,
      sessions: item.sessions,
      totalTime: item.totalTime,
      label: `${item.rating.toFixed(1)} ⭐`,
    }));
  }, [data]);

  // Calculate statistics
  const stats = useMemo(() => {
    if (data.length === 0) {
      return {
        average: 0,
        highest: 0,
        lowest: 0,
        trend: 'stable' as 'up' | 'down' | 'stable',
      };
    }

    const ratings = data.map(item => item.rating);
    const average = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length;
    const highest = Math.max(...ratings);
    const lowest = Math.min(...ratings);

    // Calculate trend (simple comparison of first and last values)
    const firstHalf = ratings.slice(0, Math.floor(ratings.length / 2));
    const secondHalf = ratings.slice(Math.floor(ratings.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, rating) => sum + rating, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, rating) => sum + rating, 0) / secondHalf.length;
    
    let trend: 'up' | 'down' | 'stable' = 'stable';
    if (secondAvg > firstAvg + 0.2) trend = 'up';
    else if (secondAvg < firstAvg - 0.2) trend = 'down';

    return { average, highest, lowest, trend };
  }, [data]);

  // Calculate average line data
  const averageLineData = useMemo(() => {
    if (!showAverage || data.length === 0) return [];
    
    return [
      { x: 1, y: stats.average },
      { x: data.length, y: stats.average },
    ];
  }, [showAverage, data.length, stats.average]);

  // Handle point selection
  const handlePointPress = (point: any) => {
    const dataPoint = data[point.x - 1];
    setSelectedPoint(selectedPoint?.date === dataPoint.date ? null : dataPoint);
  };

  // Format time duration
  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  // Get trend emoji
  const getTrendEmoji = (trend: string): string => {
    switch (trend) {
      case 'up': return '📈';
      case 'down': return '📉';
      default: return '➡️';
    }
  };

  // Render empty state
  if (data.length === 0) {
    return (
      <ExpressiveCard style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Productivity Trends</Text>
        </View>
        <View style={styles.emptyState}>
          <Text style={styles.emptyText}>
            No productivity data available for the selected time period.
            Rate your study sessions to see trends here.
          </Text>
        </View>
      </ExpressiveCard>
    );
  }

  // Render chart
  const renderChart = () => (
    <VictoryChart
      theme={VictoryTheme.material}
      width={screenWidth - 32}
      height={280}
      padding={{ left: 60, top: 20, right: 40, bottom: 60 }}
      containerComponent={<VictoryContainer responsive={false} />}
      domain={{ y: [0, 5] }}
    >
      {/* Y-axis */}
      <VictoryAxis
        dependentAxis
        tickFormat={(t) => `${t}⭐`}
        style={{
          tickLabels: {
            fontSize: 10,
            fill: theme.colors.onSurfaceVariant,
          },
          grid: {
            stroke: theme.colors.outline,
            strokeWidth: 0.5,
          },
        }}
      />
      
      {/* X-axis */}
      <VictoryAxis
        tickFormat={(t) => {
          const dataPoint = data[t - 1];
          return dataPoint ? formatDate(dataPoint.date) : '';
        }}
        style={{
          tickLabels: {
            fontSize: 10,
            fill: theme.colors.onSurfaceVariant,
            angle: -45,
          },
        }}
      />

      {/* Average line */}
      {showAverage && averageLineData.length > 0 && (
        <VictoryLine
          data={averageLineData}
          style={{
            data: {
              stroke: theme.colors.outline,
              strokeWidth: 2,
              strokeDasharray: '5,5',
            },
          }}
        />
      )}

      {/* Main chart */}
      {chartType === 'area' ? (
        <VictoryArea
          data={chartData}
          style={{
            data: {
              fill: theme.colors.primaryContainer,
              fillOpacity: 0.3,
              stroke: theme.colors.primary,
              strokeWidth: 2,
            },
          }}
          animate={{
            duration: 1000,
            onLoad: { duration: 500 },
          }}
        />
      ) : (
        <VictoryLine
          data={chartData}
          style={{
            data: {
              stroke: theme.colors.primary,
              strokeWidth: 3,
            },
          }}
          animate={{
            duration: 1000,
            onLoad: { duration: 500 },
          }}
        />
      )}

      {/* Data points */}
      {showDataPoints && (
        <VictoryScatter
          data={chartData}
          size={6}
          style={{
            data: {
              fill: ({ datum }) => 
                selectedPoint?.date === data[datum.x - 1]?.date 
                  ? theme.colors.secondary 
                  : theme.colors.primary,
            },
          }}
          labelComponent={<VictoryTooltip />}
          events={[
            {
              target: 'data',
              eventHandlers: {
                onPress: () => {
                  return [
                    {
                      target: 'data',
                      mutation: (props) => {
                        handlePointPress(props.datum);
                        return null;
                      },
                    },
                  ];
                },
              },
            },
          ]}
        />
      )}
    </VictoryChart>
  );

  return (
    <ExpressiveCard style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Productivity Trends</Text>
        <View style={styles.chartTypeToggle}>
          <TouchableOpacity
            style={[
              styles.toggleButton,
              chartType === 'line' && styles.activeToggleButton,
            ]}
            onPress={() => {/* Chart type toggle handled by parent */}}
          >
            <Text
              style={[
                styles.toggleButtonText,
                chartType === 'line' && styles.activeToggleButtonText,
              ]}
            >
              Line
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.toggleButton,
              chartType === 'area' && styles.activeToggleButton,
            ]}
            onPress={() => {/* Chart type toggle handled by parent */}}
          >
            <Text
              style={[
                styles.toggleButtonText,
                chartType === 'area' && styles.activeToggleButtonText,
              ]}
            >
              Area
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Selected Point Info */}
      {selectedPoint && (
        <View style={styles.selectedPointInfo}>
          <Text style={styles.selectedPointDate}>
            {formatDate(selectedPoint.date)}
          </Text>
          <Text style={styles.selectedPointStats}>
            Rating: {selectedPoint.rating.toFixed(1)}⭐ • {selectedPoint.sessions} sessions • {formatDuration(selectedPoint.totalTime)}
          </Text>
        </View>
      )}

      {/* Chart */}
      <View style={styles.chartContainer}>
        {renderChart()}
      </View>

      {/* Statistics */}
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{stats.average.toFixed(1)}⭐</Text>
          <Text style={styles.statLabel}>Average</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{stats.highest.toFixed(1)}⭐</Text>
          <Text style={styles.statLabel}>Highest</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{stats.lowest.toFixed(1)}⭐</Text>
          <Text style={styles.statLabel}>Lowest</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{getTrendEmoji(stats.trend)}</Text>
          <Text style={styles.statLabel}>Trend</Text>
        </View>
      </View>
    </ExpressiveCard>
  );
};

export default ProductivityTrends;
