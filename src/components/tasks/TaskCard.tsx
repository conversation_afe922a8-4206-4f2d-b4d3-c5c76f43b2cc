import React, { useCallback, useRef, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import {
  PanGestureHandler,
  LongPressGestureHandler,
  State,
  TapGestureHandler,
} from 'react-native-gesture-handler';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../../constants/expressiveTheme';
import { Task } from '../../stores/tasksStore';

const { width: screenWidth } = Dimensions.get('window');

interface TaskCardProps {
  task: Task;
  onPress: (task: Task) => void;
  onDragStart: (task: Task, offset: { x: number; y: number }) => void;
  isDragging: boolean;
  index: number;
}

export const TaskCard: React.FC<TaskCardProps> = ({
  task,
  onPress,
  onDragStart,
  isDragging,
  index,
}) => {
  const theme = useDynamicTheme();
  
  // Animation refs
  const translateX = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(0)).current;
  const scale = useRef(new Animated.Value(1)).current;
  const opacity = useRef(new Animated.Value(1)).current;
  const elevation = useRef(new Animated.Value(1)).current;

  // Gesture refs
  const panRef = useRef(null);
  const longPressRef = useRef(null);
  const tapRef = useRef(null);

  const styles = StyleSheet.create({
    container: {
      marginBottom: 8,
    },
    dragContainer: {
      position: 'absolute',
      zIndex: 1000,
      width: '100%',
    },
    card: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 12,
      shadowColor: theme.colors.shadow,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    draggedCard: {
      shadowOffset: {
        width: 0,
        height: 8,
      },
      shadowOpacity: 0.3,
      shadowRadius: 12,
      elevation: 8,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: 8,
    },
    title: {
      ...ExpressiveTypography.titleSmall,
      color: theme.colors.onSurface,
      flex: 1,
      marginRight: 8,
    },
    priorityBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      minWidth: 60,
      alignItems: 'center',
    },
    priorityText: {
      ...ExpressiveTypography.labelSmall,
      fontWeight: '600',
      textTransform: 'uppercase',
    },
    description: {
      ...ExpressiveTypography.bodySmall,
      color: theme.colors.onSurfaceVariant,
      marginBottom: 8,
      lineHeight: 18,
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    dueDate: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onSurfaceVariant,
    },
    overdueDueDate: {
      color: theme.colors.error,
      fontWeight: '600',
    },
    assignedTo: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.primary,
      backgroundColor: theme.colors.primaryContainer,
      paddingHorizontal: 8,
      paddingVertical: 2,
      borderRadius: 8,
    },
    placeholder: {
      height: 80,
      backgroundColor: 'transparent',
    },
  });

  // Get priority colors
  const getPriorityColors = useCallback((priority: Task['priority']) => {
    switch (priority) {
      case 'high':
        return {
          backgroundColor: theme.colors.errorContainer,
          color: theme.colors.onErrorContainer,
        };
      case 'medium':
        return {
          backgroundColor: theme.colors.tertiaryContainer,
          color: theme.colors.onTertiaryContainer,
        };
      case 'low':
        return {
          backgroundColor: theme.colors.secondaryContainer,
          color: theme.colors.onSecondaryContainer,
        };
      default:
        return {
          backgroundColor: theme.colors.surfaceVariant,
          color: theme.colors.onSurfaceVariant,
        };
    }
  }, [theme]);

  // Check if task is overdue
  const isOverdue = useMemo(() => {
    if (!task.due_date) return false;
    return new Date(task.due_date) < new Date() && task.status !== 'done';
  }, [task.due_date, task.status]);

  // Format due date
  const formatDueDate = useCallback((dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Due today';
    if (diffDays === 1) return 'Due tomorrow';
    if (diffDays === -1) return 'Due yesterday';
    if (diffDays < 0) return `Overdue by ${Math.abs(diffDays)} days`;
    if (diffDays <= 7) return `Due in ${diffDays} days`;
    
    return date.toLocaleDateString();
  }, []);

  // Handle tap gesture
  const handleTapStateChange = useCallback((event: any) => {
    if (event.nativeEvent.state === State.ACTIVE && !isDragging) {
      onPress(task);
    }
  }, [onPress, task, isDragging]);

  // Handle long press gesture
  const handleLongPressStateChange = useCallback((event: any) => {
    if (event.nativeEvent.state === State.ACTIVE) {
      // Start drag preparation animation
      Animated.parallel([
        Animated.spring(scale, {
          toValue: 1.05,
          useNativeDriver: true,
        }),
        Animated.spring(elevation, {
          toValue: 8,
          useNativeDriver: false,
        }),
      ]).start();
    }
  }, [scale, elevation]);

  // Handle pan gesture
  const handlePanGestureEvent = useCallback((event: any) => {
    const { translationX, translationY } = event.nativeEvent;
    
    translateX.setValue(translationX);
    translateY.setValue(translationY);
  }, [translateX, translateY]);

  const handlePanStateChange = useCallback((event: any) => {
    const { state, translationX, translationY } = event.nativeEvent;
    
    switch (state) {
      case State.BEGAN:
        // Start drag
        onDragStart(task, { x: translationX, y: translationY });
        
        // Animate to dragging state
        Animated.parallel([
          Animated.spring(scale, {
            toValue: 1.1,
            useNativeDriver: true,
          }),
          Animated.spring(opacity, {
            toValue: 0.9,
            useNativeDriver: true,
          }),
        ]).start();
        break;
        
      case State.END:
      case State.CANCELLED:
      case State.FAILED:
        // Reset animations
        Animated.parallel([
          Animated.spring(translateX, {
            toValue: 0,
            useNativeDriver: true,
          }),
          Animated.spring(translateY, {
            toValue: 0,
            useNativeDriver: true,
          }),
          Animated.spring(scale, {
            toValue: 1,
            useNativeDriver: true,
          }),
          Animated.spring(opacity, {
            toValue: 1,
            useNativeDriver: true,
          }),
          Animated.spring(elevation, {
            toValue: 1,
            useNativeDriver: false,
          }),
        ]).start();
        break;
    }
  }, [onDragStart, task, translateX, translateY, scale, opacity, elevation]);

  const priorityColors = getPriorityColors(task.priority);

  // If this card is being dragged, show placeholder
  if (isDragging) {
    return <View style={styles.placeholder} />;
  }

  return (
    <View style={styles.container}>
      <TapGestureHandler
        ref={tapRef}
        onHandlerStateChange={handleTapStateChange}
        shouldCancelWhenOutside
      >
        <LongPressGestureHandler
          ref={longPressRef}
          onHandlerStateChange={handleLongPressStateChange}
          minDurationMs={500}
          simultaneousHandlers={[panRef]}
        >
          <PanGestureHandler
            ref={panRef}
            onGestureEvent={handlePanGestureEvent}
            onHandlerStateChange={handlePanStateChange}
            simultaneousHandlers={[longPressRef]}
            minDist={10}
          >
            <Animated.View
              style={[
                styles.card,
                isDragging && styles.draggedCard,
                {
                  transform: [
                    { translateX },
                    { translateY },
                    { scale },
                  ],
                  opacity,
                  elevation,
                },
              ]}
            >
              {/* Header */}
              <View style={styles.header}>
                <Text style={styles.title} numberOfLines={2}>
                  {task.title}
                </Text>
                <View style={[styles.priorityBadge, priorityColors]}>
                  <Text style={[styles.priorityText, { color: priorityColors.color }]}>
                    {task.priority}
                  </Text>
                </View>
              </View>

              {/* Description */}
              {task.description && (
                <Text style={styles.description} numberOfLines={3}>
                  {task.description}
                </Text>
              )}

              {/* Footer */}
              <View style={styles.footer}>
                {task.due_date && (
                  <Text style={[styles.dueDate, isOverdue && styles.overdueDueDate]}>
                    {formatDueDate(task.due_date)}
                  </Text>
                )}
                
                {task.assigned_to && (
                  <Text style={styles.assignedTo}>
                    Assigned
                  </Text>
                )}
              </View>
            </Animated.View>
          </PanGestureHandler>
        </LongPressGestureHandler>
      </TapGestureHandler>
    </View>
  );
};

export default TaskCard;
