import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  TextInput,
  Alert,
  Animated,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypographyVariants } from '../../constants/expressiveTheme';
import { MaterialIcons } from '@expo/vector-icons';
import { useTasksStore } from '../../stores/tasksStore';
import { useAuthStore } from '../../stores/authStore';
import { TodoBoard } from './TodoBoard';

interface TasksDropdownProps {
  groupId?: string;
  initialSelectedSubject?: string | null;
}

export const TasksDropdown: React.FC<TasksDropdownProps> = ({
  groupId,
  initialSelectedSubject,
}) => {
  const theme = useDynamicTheme();
  const { user } = useAuthStore();
  const { tasks, fetchTasks, addTask } = useTasksStore();
  
  const [showDropdown, setShowDropdown] = useState(false);
  const [showTasksModal, setShowTasksModal] = useState(false);
  const [showAddTaskModal, setShowAddTaskModal] = useState(false);
  const [newTaskTitle, setNewTaskTitle] = useState('');
  const [newTaskDescription, setNewTaskDescription] = useState('');
  const [selectedPriority, setSelectedPriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [selectedSubject, setSelectedSubject] = useState<string>(initialSelectedSubject || '');

  // Animation values
  const fadeAnim = new Animated.Value(0);
  const scaleAnim = new Animated.Value(0.95);

  useEffect(() => {
    if (user) {
      fetchTasks(user.id, groupId);
    }
  }, [user, groupId, fetchTasks]);

  useEffect(() => {
    if (showDropdown || showTasksModal) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.95,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [showDropdown, showTasksModal]);

  const handleAddTask = async () => {
    if (!newTaskTitle.trim()) {
      Alert.alert('Error', 'Please enter a task title');
      return;
    }

    if (!user) {
      Alert.alert('Error', 'You must be logged in to add tasks');
      return;
    }

    try {
      await addTask({
        title: newTaskTitle.trim(),
        description: newTaskDescription.trim(),
        priority: selectedPriority,
        subject: selectedSubject,
        user_id: user.id,
        group_id: groupId,
        status: 'todo',
        due_date: null,
      });

      setNewTaskTitle('');
      setNewTaskDescription('');
      setSelectedPriority('medium');
      setShowAddTaskModal(false);
      
      Alert.alert('Success', 'Task added successfully!');
    } catch (error) {
      console.error('Error adding task:', error);
      Alert.alert('Error', 'Failed to add task. Please try again.');
    }
  };

  const styles = StyleSheet.create({
    container: {
      position: 'relative',
      zIndex: 1000,
      marginHorizontal: 20,
      marginVertical: 10,
    },
    dropdownButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.primaryContainer,
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderRadius: 25,
      elevation: 4,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 4,
    },
    dropdownButtonText: {
      ...ExpressiveTypographyVariants.labelLarge,
      color: theme.colors.onPrimaryContainer,
      marginLeft: 8,
      fontWeight: '600',
    },
    dropdownMenu: {
      position: 'absolute',
      top: 60,
      left: 0,
      right: 0,
      backgroundColor: theme.colors.surface,
      borderRadius: 16,
      elevation: 8,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      maxHeight: 200,
      zIndex: 1001,
    },
    menuItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
    },
    menuItemText: {
      ...ExpressiveTypographyVariants.bodyMedium,
      color: theme.colors.onSurface,
      marginLeft: 12,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      backgroundColor: theme.colors.surface,
      borderRadius: 24,
      padding: 24,
      margin: 20,
      maxHeight: '80%',
      width: '90%',
      elevation: 12,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 6 },
      shadowOpacity: 0.4,
      shadowRadius: 12,
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
    },
    modalTitle: {
      ...ExpressiveTypographyVariants.headlineSmall,
      color: theme.colors.onSurface,
      fontWeight: '600',
    },
    closeButton: {
      padding: 8,
    },
    input: {
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 12,
      paddingHorizontal: 16,
      paddingVertical: 12,
      marginBottom: 16,
      color: theme.colors.onSurfaceVariant,
      ...ExpressiveTypographyVariants.bodyLarge,
    },
    textArea: {
      height: 100,
      textAlignVertical: 'top',
    },
    priorityContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 20,
    },
    priorityButton: {
      flex: 1,
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 12,
      marginHorizontal: 4,
      alignItems: 'center',
      backgroundColor: theme.colors.surfaceVariant,
    },
    priorityButtonActive: {
      backgroundColor: theme.colors.primary,
    },
    priorityButtonText: {
      ...ExpressiveTypographyVariants.labelMedium,
      color: theme.colors.onSurfaceVariant,
    },
    priorityButtonTextActive: {
      color: theme.colors.onPrimary,
      fontWeight: '600',
    },
    actionButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: 12,
    },
    button: {
      flex: 1,
      paddingVertical: 14,
      borderRadius: 12,
      alignItems: 'center',
    },
    primaryButton: {
      backgroundColor: theme.colors.primary,
    },
    secondaryButton: {
      backgroundColor: theme.colors.surfaceVariant,
    },
    buttonText: {
      ...ExpressiveTypographyVariants.labelLarge,
      fontWeight: '600',
    },
    primaryButtonText: {
      color: theme.colors.onPrimary,
    },
    secondaryButtonText: {
      color: theme.colors.onSurfaceVariant,
    },
  });

  return (
    <View style={styles.container}>
      {/* Dropdown Button */}
      <TouchableOpacity
        style={styles.dropdownButton}
        onPress={() => setShowDropdown(!showDropdown)}
        activeOpacity={0.8}
      >
        <MaterialIcons
          name="assignment"
          size={20}
          color={theme.colors.onPrimaryContainer}
        />
        <Text style={styles.dropdownButtonText}>Tasks</Text>
        <MaterialIcons
          name={showDropdown ? "keyboard-arrow-up" : "keyboard-arrow-down"}
          size={20}
          color={theme.colors.onPrimaryContainer}
        />
      </TouchableOpacity>

      {/* Dropdown Menu */}
      {showDropdown && (
        <Animated.View
          style={[
            styles.dropdownMenu,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => {
              setShowTasksModal(true);
              setShowDropdown(false);
            }}
          >
            <MaterialIcons
              name="visibility"
              size={20}
              color={theme.colors.primary}
            />
            <Text style={styles.menuItemText}>View Tasks</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => {
              setShowAddTaskModal(true);
              setShowDropdown(false);
            }}
          >
            <MaterialIcons
              name="add"
              size={20}
              color={theme.colors.primary}
            />
            <Text style={styles.menuItemText}>Add Task</Text>
          </TouchableOpacity>
        </Animated.View>
      )}

      {/* Tasks Modal */}
      <Modal
        visible={showTasksModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowTasksModal(false)}
      >
        <View style={styles.modalOverlay}>
          <Animated.View
            style={[
              styles.modalContent,
              {
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }],
              },
            ]}
          >
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Tasks</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowTasksModal(false)}
              >
                <MaterialIcons
                  name="close"
                  size={24}
                  color={theme.colors.onSurface}
                />
              </TouchableOpacity>
            </View>

            <ScrollView showsVerticalScrollIndicator={false}>
              <TodoBoard compact={true} groupId={groupId} />
            </ScrollView>
          </Animated.View>
        </View>
      </Modal>

      {/* Add Task Modal */}
      <Modal
        visible={showAddTaskModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowAddTaskModal(false)}
      >
        <View style={styles.modalOverlay}>
          <Animated.View
            style={[
              styles.modalContent,
              {
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }],
              },
            ]}
          >
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Add New Task</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowAddTaskModal(false)}
              >
                <MaterialIcons
                  name="close"
                  size={24}
                  color={theme.colors.onSurface}
                />
              </TouchableOpacity>
            </View>

            <TextInput
              style={styles.input}
              placeholder="Task title"
              placeholderTextColor={theme.colors.onSurfaceVariant}
              value={newTaskTitle}
              onChangeText={setNewTaskTitle}
            />

            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Description (optional)"
              placeholderTextColor={theme.colors.onSurfaceVariant}
              value={newTaskDescription}
              onChangeText={setNewTaskDescription}
              multiline
            />

            <Text style={[styles.modalTitle, { fontSize: 16, marginBottom: 12 }]}>
              Priority
            </Text>
            <View style={styles.priorityContainer}>
              {(['low', 'medium', 'high'] as const).map((priority) => (
                <TouchableOpacity
                  key={priority}
                  style={[
                    styles.priorityButton,
                    selectedPriority === priority && styles.priorityButtonActive,
                  ]}
                  onPress={() => setSelectedPriority(priority)}
                >
                  <Text
                    style={[
                      styles.priorityButtonText,
                      selectedPriority === priority && styles.priorityButtonTextActive,
                    ]}
                  >
                    {priority.charAt(0).toUpperCase() + priority.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={[styles.button, styles.secondaryButton]}
                onPress={() => setShowAddTaskModal(false)}
              >
                <Text style={[styles.buttonText, styles.secondaryButtonText]}>
                  Cancel
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.button, styles.primaryButton]}
                onPress={handleAddTask}
              >
                <Text style={[styles.buttonText, styles.primaryButtonText]}>
                  Add Task
                </Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </View>
      </Modal>
    </View>
  );
};
