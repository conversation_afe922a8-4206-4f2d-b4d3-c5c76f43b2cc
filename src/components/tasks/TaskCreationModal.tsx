import React, { useState, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Animated,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../../constants/expressiveTheme';
import { useTasksStore, Task } from '../../stores/tasksStore';
import ExpressiveButton from '../buttons/ExpressiveButton';
import ExpressiveCard from '../surfaces/ExpressiveCard';
import ExpressiveInput from '../inputs/ExpressiveInput';

interface TaskCreationModalProps {
  visible: boolean;
  onClose: () => void;
  initialColumnId?: string;
  groupId?: string | undefined;
  onTaskCreated?: (task: Task) => void;
}

export const TaskCreationModal: React.FC<TaskCreationModalProps> = ({
  visible,
  onClose,
  initialColumnId = 'todo',
  groupId,
  onTaskCreated,
}) => {
  const theme = useDynamicTheme();
  const { createTask, columns, isLoading } = useTasksStore();

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState<Task['priority']>('medium');
  const [status, setStatus] = useState<Task['status']>('todo');
  const [columnId, setColumnId] = useState(initialColumnId);
  const [dueDate, setDueDate] = useState<Date | null>(null);
  const [assignedTo, setAssignedTo] = useState<string>('');

  // Validation state
  const [errors, setErrors] = useState<{
    title?: string;
    description?: string;
  }>({});

  // Animation
  const slideAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  const styles = StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-end',
    },
    modalContainer: {
      backgroundColor: theme.colors.surface,
      borderTopLeftRadius: 24,
      borderTopRightRadius: 24,
      maxHeight: '90%',
      minHeight: '60%',
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 20,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
    },
    title: {
      ...ExpressiveTypography.headlineSmall,
      color: theme.colors.onSurface,
    },
    closeButton: {
      padding: 8,
    },
    closeButtonText: {
      ...ExpressiveTypography.labelLarge,
      color: theme.colors.primary,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    section: {
      marginBottom: 24,
    },
    sectionTitle: {
      ...ExpressiveTypography.titleSmall,
      color: theme.colors.onSurface,
      marginBottom: 12,
    },
    inputContainer: {
      marginBottom: 16,
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.outline,
      borderRadius: 12,
      padding: 16,
      backgroundColor: theme.colors.surface,
      color: theme.colors.onSurface,
      ...ExpressiveTypography.bodyMedium,
    },
    focusedInput: {
      borderColor: theme.colors.primary,
      borderWidth: 2,
    },
    textArea: {
      height: 100,
      textAlignVertical: 'top',
    },
    errorText: {
      ...ExpressiveTypography.bodySmall,
      color: theme.colors.error,
      marginTop: 4,
    },
    optionGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
    },
    optionButton: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 20,
      borderWidth: 1,
      borderColor: theme.colors.outline,
      backgroundColor: theme.colors.surface,
    },
    selectedOptionButton: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    optionText: {
      ...ExpressiveTypography.labelMedium,
      color: theme.colors.onSurface,
    },
    selectedOptionText: {
      color: theme.colors.onPrimary,
    },
    dateButton: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 16,
      borderWidth: 1,
      borderColor: theme.colors.outline,
      borderRadius: 12,
      backgroundColor: theme.colors.surface,
    },
    dateButtonText: {
      ...ExpressiveTypography.bodyMedium,
      color: theme.colors.onSurface,
    },
    placeholderText: {
      color: theme.colors.onSurfaceVariant,
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      padding: 20,
      borderTopWidth: 1,
      borderTopColor: theme.colors.outline,
      gap: 12,
    },
    footerButton: {
      flex: 1,
    },
  });

  // Reset form
  const resetForm = useCallback(() => {
    setTitle('');
    setDescription('');
    setPriority('medium');
    setStatus('todo');
    setColumnId(initialColumnId);
    setDueDate(null);
    setAssignedTo('');
    setErrors({});
  }, [initialColumnId]);

  // Validate form
  const validateForm = useCallback(() => {
    const newErrors: typeof errors = {};

    if (!title.trim()) {
      newErrors.title = 'Title is required';
    } else if (title.trim().length < 3) {
      newErrors.title = 'Title must be at least 3 characters';
    }

    if (description && description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [title, description]);

  // Handle create task
  const handleCreateTask = useCallback(async () => {
    if (!validateForm()) {
      return;
    }

    try {
      const taskData: Omit<Task, 'id' | 'user_id' | 'created_at' | 'updated_at'> = {
        title: title.trim(),
        description: description.trim() || null,
        priority,
        status,
        column_id: columnId,
        due_date: dueDate?.toISOString() || null,
        assigned_to: assignedTo || null,
        assigned_to_photo_url: null,
        group_id: groupId || null,
      };

      const newTask = await createTask(taskData);
      
      if (newTask) {
        if (onTaskCreated) {
          onTaskCreated(newTask);
        }
        resetForm();
        onClose();
      } else {
        Alert.alert('Error', 'Failed to create task. Please try again.');
      }
    } catch (error) {
      console.error('Error creating task:', error);
      Alert.alert('Error', 'Failed to create task. Please try again.');
    }
  }, [
    validateForm,
    title,
    description,
    priority,
    status,
    columnId,
    dueDate,
    assignedTo,
    groupId,
    createTask,
    onTaskCreated,
    resetForm,
    onClose,
  ]);

  // Handle close
  const handleClose = useCallback(() => {
    resetForm();
    onClose();
  }, [resetForm, onClose]);

  // Animation effects
  React.useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, slideAnim, opacityAnim]);

  // Priority options
  const priorityOptions: { value: Task['priority']; label: string; color: string }[] = [
    { value: 'low', label: 'Low', color: theme.colors.secondaryContainer },
    { value: 'medium', label: 'Medium', color: theme.colors.tertiaryContainer },
    { value: 'high', label: 'High', color: theme.colors.errorContainer },
  ];

  // Status options
  const statusOptions: { value: Task['status']; label: string }[] = [
    { value: 'todo', label: 'To Do' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'done', label: 'Done' },
  ];

  // Column options
  const columnOptions = columns.map(column => ({
    value: column.id,
    label: column.title,
  }));

  if (!visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={handleClose}
    >
      <Animated.View style={[styles.overlay, { opacity: opacityAnim }]}>
        <TouchableOpacity
          style={{ flex: 1 }}
          activeOpacity={1}
          onPress={handleClose}
        />
        
        <Animated.View
          style={[
            styles.modalContainer,
            {
              transform: [
                {
                  translateY: slideAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [500, 0],
                  }),
                },
              ],
            },
          ]}
        >
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={{ flex: 1 }}
          >
            {/* Header */}
            <View style={styles.header}>
              <Text style={styles.title}>Create Task</Text>
              <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
                <Text style={styles.closeButtonText}>Cancel</Text>
              </TouchableOpacity>
            </View>

            {/* Content */}
            <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
              {/* Title */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Title *</Text>
                <View style={styles.inputContainer}>
                  <TextInput
                    style={[styles.input, errors.title && { borderColor: theme.colors.error }]}
                    value={title}
                    onChangeText={setTitle}
                    placeholder="Enter task title"
                    placeholderTextColor={theme.colors.onSurfaceVariant}
                    maxLength={100}
                  />
                  {errors.title && <Text style={styles.errorText}>{errors.title}</Text>}
                </View>
              </View>

              {/* Description */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Description</Text>
                <View style={styles.inputContainer}>
                  <TextInput
                    style={[
                      styles.input,
                      styles.textArea,
                      errors.description && { borderColor: theme.colors.error }
                    ]}
                    value={description}
                    onChangeText={setDescription}
                    placeholder="Enter task description (optional)"
                    placeholderTextColor={theme.colors.onSurfaceVariant}
                    multiline
                    maxLength={500}
                  />
                  {errors.description && <Text style={styles.errorText}>{errors.description}</Text>}
                </View>
              </View>

              {/* Priority */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Priority</Text>
                <View style={styles.optionGrid}>
                  {priorityOptions.map((option) => (
                    <TouchableOpacity
                      key={option.value}
                      style={[
                        styles.optionButton,
                        priority === option.value && styles.selectedOptionButton,
                        { backgroundColor: priority === option.value ? option.color : theme.colors.surface }
                      ]}
                      onPress={() => setPriority(option.value)}
                    >
                      <Text
                        style={[
                          styles.optionText,
                          priority === option.value && styles.selectedOptionText,
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Status */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Status</Text>
                <View style={styles.optionGrid}>
                  {statusOptions.map((option) => (
                    <TouchableOpacity
                      key={option.value}
                      style={[
                        styles.optionButton,
                        status === option.value && styles.selectedOptionButton,
                      ]}
                      onPress={() => {
                        setStatus(option.value);
                        setColumnId(option.value === 'todo' ? 'todo' : option.value === 'in_progress' ? 'in_progress' : 'done');
                      }}
                    >
                      <Text
                        style={[
                          styles.optionText,
                          status === option.value && styles.selectedOptionText,
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Column */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Column</Text>
                <View style={styles.optionGrid}>
                  {columnOptions.map((option) => (
                    <TouchableOpacity
                      key={option.value}
                      style={[
                        styles.optionButton,
                        columnId === option.value && styles.selectedOptionButton,
                      ]}
                      onPress={() => setColumnId(option.value)}
                    >
                      <Text
                        style={[
                          styles.optionText,
                          columnId === option.value && styles.selectedOptionText,
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Due Date */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Due Date</Text>
                <TouchableOpacity
                  style={styles.dateButton}
                  onPress={() => {
                    // TODO: Implement date picker
                    Alert.alert('Date Picker', 'Date picker will be implemented in a future update');
                  }}
                >
                  <Text style={[styles.dateButtonText, !dueDate && styles.placeholderText]}>
                    {dueDate ? dueDate.toLocaleDateString() : 'Select due date (optional)'}
                  </Text>
                  <Text style={styles.dateButtonText}>📅</Text>
                </TouchableOpacity>
              </View>

              {/* Assigned To */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Assigned To</Text>
                <View style={styles.inputContainer}>
                  <TextInput
                    style={styles.input}
                    value={assignedTo}
                    onChangeText={setAssignedTo}
                    placeholder="Enter assignee email (optional)"
                    placeholderTextColor={theme.colors.onSurfaceVariant}
                    keyboardType="email-address"
                    autoCapitalize="none"
                  />
                </View>
              </View>
            </ScrollView>

            {/* Footer */}
            <View style={styles.footer}>
              <ExpressiveButton
                title="Cancel"
                variant="outlined"
                onPress={handleClose}
                style={styles.footerButton}
                disabled={isLoading}
              />
              <ExpressiveButton
                title={isLoading ? "Creating..." : "Create Task"}
                variant="filled"
                onPress={handleCreateTask}
                style={styles.footerButton}
                disabled={isLoading || !title.trim()}
                loading={isLoading}
              />
            </View>
          </KeyboardAvoidingView>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

export default TaskCreationModal;
