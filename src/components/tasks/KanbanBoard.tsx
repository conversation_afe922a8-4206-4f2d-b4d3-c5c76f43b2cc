import React, { useCallback, useMemo } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Dimensions,
  RefreshControl,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../../constants/expressiveTheme';
import { useTasksStore, Task, TaskColumn } from '../../stores/tasksStore';
import KanbanColumn from './KanbanColumn';
import TaskCard from './TaskCard';
import ExpressiveButton from '../buttons/ExpressiveButton';
import { getCurrentUser } from '../../services/supabase/client';

const { width: screenWidth } = Dimensions.get('window');

interface KanbanBoardProps {
  groupId?: string | undefined;
  onTaskPress?: (task: Task) => void;
  onCreateTask?: (columnId: string) => void;
  refreshing?: boolean;
  onRefresh?: () => void;
}

export const KanbanBoard: React.FC<KanbanBoardProps> = ({
  groupId,
  onTaskPress,
  onCreateTask,
  refreshing = false,
  onRefresh,
}) => {
  const theme = useDynamicTheme();
  const {
    tasks,
    columns,
    isLoading,
    error,
    getTasksByColumn,
    loadTasks,
    updateTask,
    dragState,
    startDrag,
    updateDrag,
    endDrag,
    cancelDrag,
  } = useTasksStore();

  // Calculate column width for horizontal scrolling
  const columnWidth = Math.max(280, screenWidth * 0.8);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.surface,
    },
    header: {
      padding: 16,
      backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
    },
    title: {
      ...ExpressiveTypography.headlineMedium,
      color: theme.colors.onSurface,
      marginBottom: 4,
    },
    subtitle: {
      ...ExpressiveTypography.bodyMedium,
      color: theme.colors.onSurfaceVariant,
    },
    scrollContainer: {
      flex: 1,
    },
    columnsContainer: {
      flexDirection: 'row',
      paddingHorizontal: 8,
      paddingVertical: 16,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32,
    },
    errorText: {
      ...ExpressiveTypography.bodyLarge,
      color: theme.colors.error,
      textAlign: 'center',
      marginBottom: 16,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32,
    },
    emptyText: {
      ...ExpressiveTypography.bodyLarge,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginBottom: 16,
    },
    dragOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.1)',
      zIndex: 1000,
    },
  });

  // Handle task drag start
  const handleTaskDragStart = useCallback((task: Task, sourceColumn: string, offset: { x: number; y: number }) => {
    startDrag(task, sourceColumn, offset);
  }, [startDrag]);

  // Handle task drag update
  const handleTaskDragUpdate = useCallback((targetColumn: string | null, offset: { x: number; y: number }) => {
    updateDrag(targetColumn, offset);
  }, [updateDrag]);

  // Handle task drag end
  const handleTaskDragEnd = useCallback(async () => {
    const success = await endDrag();
    if (!success) {
      // Handle drag failure if needed
      console.warn('Failed to update task position');
    }
  }, [endDrag]);

  // Handle task drag cancel
  const handleTaskDragCancel = useCallback(() => {
    cancelDrag();
  }, [cancelDrag]);

  // Handle task press
  const handleTaskPress = useCallback((task: Task) => {
    if (onTaskPress) {
      onTaskPress(task);
    }
  }, [onTaskPress]);

  // Handle create task
  const handleCreateTask = useCallback((columnId: string) => {
    if (onCreateTask) {
      onCreateTask(columnId);
    }
  }, [onCreateTask]);

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    if (onRefresh) {
      onRefresh();
    } else {
      await loadTasks(groupId);
    }
  }, [onRefresh, loadTasks, groupId]);

  // Get task statistics
  const taskStats = useMemo(() => {
    const stats = {
      total: tasks.length,
      todo: 0,
      inProgress: 0,
      done: 0,
    };

    tasks.forEach(task => {
      switch (task.status) {
        case 'todo':
          stats.todo++;
          break;
        case 'in_progress':
          stats.inProgress++;
          break;
        case 'done':
          stats.done++;
          break;
      }
    });

    return stats;
  }, [tasks]);

  // Render error state
  if (error) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <ExpressiveButton
            title="Retry"
            variant="filled"
            onPress={handleRefresh}
          />
        </View>
      </View>
    );
  }

  // Render empty state
  if (!isLoading && tasks.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Task Board</Text>
          <Text style={styles.subtitle}>No tasks yet</Text>
        </View>
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            Create your first task to get started with organizing your work.
          </Text>
          <ExpressiveButton
            title="Create Task"
            variant="filled"
            onPress={() => handleCreateTask('todo')}
          />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Task Board</Text>
        <Text style={styles.subtitle}>
          {taskStats.total} tasks • {taskStats.todo} to do • {taskStats.inProgress} in progress • {taskStats.done} done
        </Text>
      </View>

      {/* Kanban Columns */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.scrollContainer}
        contentContainerStyle={styles.columnsContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing || isLoading}
            onRefresh={handleRefresh}
            tintColor={theme.colors.primary}
            colors={[theme.colors.primary]}
          />
        }
      >
        {columns.map((column) => (
          <KanbanColumn
            key={column.id}
            column={column}
            tasks={getTasksByColumn(column.id)}
            width={columnWidth}
            onTaskPress={handleTaskPress}
            onCreateTask={handleCreateTask}
            onTaskDragStart={handleTaskDragStart}
            onTaskDragUpdate={handleTaskDragUpdate}
            onTaskDragEnd={handleTaskDragEnd}
            onTaskDragCancel={handleTaskDragCancel}
            isDragActive={dragState.isDragging}
            draggedTaskId={dragState.draggedTask?.id}
            isDropTarget={dragState.targetColumn === column.id}
          />
        ))}
      </ScrollView>

      {/* Drag Overlay */}
      {dragState.isDragging && (
        <View style={styles.dragOverlay} pointerEvents="none" />
      )}
    </View>
  );
};

export default KanbanBoard;
