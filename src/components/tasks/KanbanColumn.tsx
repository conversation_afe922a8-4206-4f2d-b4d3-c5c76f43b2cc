import React, { useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Animated,
} from 'react-native';
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../../constants/expressiveTheme';
import { Task, TaskColumn } from '../../stores/tasksStore';
import TaskCard from './TaskCard';
import ExpressiveButton from '../buttons/ExpressiveButton';

interface KanbanColumnProps {
  column: TaskColumn;
  tasks: Task[];
  width: number;
  onTaskPress: (task: Task) => void;
  onCreateTask: (columnId: string) => void;
  onTaskDragStart: (task: Task, sourceColumn: string, offset: { x: number; y: number }) => void;
  onTaskDragUpdate: (targetColumn: string | null, offset: { x: number; y: number }) => void;
  onTaskDragEnd: () => void;
  onTaskDragCancel: () => void;
  isDragActive: boolean;
  draggedTaskId?: string;
  isDropTarget: boolean;
}

export const KanbanColumn: React.FC<KanbanColumnProps> = ({
  column,
  tasks,
  width,
  onTaskPress,
  onCreateTask,
  onTaskDragStart,
  onTaskDragUpdate,
  onTaskDragEnd,
  onTaskDragCancel,
  isDragActive,
  draggedTaskId,
  isDropTarget,
}) => {
  const theme = useDynamicTheme();
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  const styles = StyleSheet.create({
    container: {
      width,
      marginHorizontal: 8,
      backgroundColor: theme.colors.surfaceContainer,
      borderRadius: 16,
      overflow: 'hidden',
    },
    dropTargetContainer: {
      backgroundColor: theme.colors.primaryContainer,
      borderWidth: 2,
      borderColor: theme.colors.primary,
      borderStyle: 'dashed',
    },
    header: {
      padding: 16,
      backgroundColor: column.color || theme.colors.primary,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    headerTitle: {
      ...ExpressiveTypography.titleMedium,
      color: theme.colors.onPrimary,
      fontWeight: '600',
    },
    taskCount: {
      backgroundColor: theme.colors.onPrimary,
      color: column.color || theme.colors.primary,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      fontSize: 12,
      fontWeight: '600',
      minWidth: 24,
      textAlign: 'center',
    },
    scrollContainer: {
      flex: 1,
      maxHeight: 600, // Limit height for better UX
    },
    tasksContainer: {
      padding: 12,
      gap: 8,
    },
    emptyContainer: {
      padding: 24,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: 120,
    },
    emptyText: {
      ...ExpressiveTypography.bodyMedium,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginBottom: 12,
    },
    createButton: {
      marginTop: 8,
      marginHorizontal: 12,
      marginBottom: 12,
    },
    dragPlaceholder: {
      height: 80,
      backgroundColor: theme.colors.outline,
      borderRadius: 12,
      borderWidth: 2,
      borderColor: theme.colors.primary,
      borderStyle: 'dashed',
      justifyContent: 'center',
      alignItems: 'center',
      opacity: 0.5,
    },
    dragPlaceholderText: {
      ...ExpressiveTypography.bodySmall,
      color: theme.colors.onSurfaceVariant,
    },
  });

  // Handle create task
  const handleCreateTask = useCallback(() => {
    onCreateTask(column.id);
  }, [onCreateTask, column.id]);

  // Handle task card press
  const handleTaskPress = useCallback((task: Task) => {
    if (!isDragActive) {
      onTaskPress(task);
    }
  }, [onTaskPress, isDragActive]);

  // Handle task drag start
  const handleTaskDragStart = useCallback((task: Task, offset: { x: number; y: number }) => {
    onTaskDragStart(task, column.id, offset);
  }, [onTaskDragStart, column.id]);

  // Handle pan gesture for drop zone
  const handlePanGestureEvent = useCallback((event: any) => {
    const { translationX, translationY } = event.nativeEvent;
    onTaskDragUpdate(column.id, { x: translationX, y: translationY });
  }, [onTaskDragUpdate, column.id]);

  const handlePanStateChange = useCallback((event: any) => {
    const { state } = event.nativeEvent;
    
    switch (state) {
      case State.ACTIVE:
        // Scale up when drag enters
        Animated.spring(scaleAnim, {
          toValue: 1.05,
          useNativeDriver: true,
        }).start();
        break;
        
      case State.END:
        // Handle drop
        onTaskDragEnd();
        // Scale back to normal
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
        }).start();
        break;
        
      case State.CANCELLED:
      case State.FAILED:
        // Cancel drag
        onTaskDragCancel();
        // Scale back to normal
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
        }).start();
        break;
    }
  }, [onTaskDragEnd, onTaskDragCancel, scaleAnim]);

  // Filter out dragged task from display
  const visibleTasks = tasks.filter(task => task.id !== draggedTaskId);

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>
        {column.title === 'To Do' 
          ? 'No tasks to do'
          : column.title === 'In Progress'
          ? 'No tasks in progress'
          : 'No completed tasks'
        }
      </Text>
      {column.status === 'todo' && (
        <ExpressiveButton
          title="Add Task"
          variant="outlined"
          size="small"
          onPress={handleCreateTask}
        />
      )}
    </View>
  );

  // Render drag placeholder
  const renderDragPlaceholder = () => (
    <View style={styles.dragPlaceholder}>
      <Text style={styles.dragPlaceholderText}>Drop task here</Text>
    </View>
  );

  return (
    <PanGestureHandler
      onGestureEvent={handlePanGestureEvent}
      onHandlerStateChange={handlePanStateChange}
      enabled={isDragActive}
    >
      <Animated.View
        style={[
          styles.container,
          isDropTarget && styles.dropTargetContainer,
          {
            transform: [{ scale: scaleAnim }],
            opacity: opacityAnim,
          },
        ]}
      >
        {/* Column Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>{column.title}</Text>
          <Text style={styles.taskCount}>{tasks.length}</Text>
        </View>

        {/* Tasks List */}
        <ScrollView
          style={styles.scrollContainer}
          contentContainerStyle={styles.tasksContainer}
          showsVerticalScrollIndicator={false}
          nestedScrollEnabled
        >
          {visibleTasks.length === 0 ? (
            renderEmptyState()
          ) : (
            <>
              {visibleTasks.map((task, index) => (
                <TaskCard
                  key={task.id}
                  task={task}
                  onPress={handleTaskPress}
                  onDragStart={handleTaskDragStart}
                  isDragging={draggedTaskId === task.id}
                  index={index}
                />
              ))}
              
              {/* Show drop placeholder when dragging over this column */}
              {isDropTarget && isDragActive && renderDragPlaceholder()}
            </>
          )}
        </ScrollView>

        {/* Create Task Button */}
        <View style={styles.createButton}>
          <ExpressiveButton
            title={`Add ${column.title === 'To Do' ? 'Task' : column.title === 'In Progress' ? 'Task' : 'Task'}`}
            variant="text"
            size="small"
            onPress={handleCreateTask}
          />
        </View>
      </Animated.View>
    </PanGestureHandler>
  );
};

export default KanbanColumn;
