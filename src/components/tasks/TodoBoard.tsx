import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypographyVariants } from '../../constants/expressiveTheme';
import { MaterialIcons } from '@expo/vector-icons';
import { useTasksStore } from '../../stores/tasksStore';
import { useAuthStore } from '../../stores/authStore';

interface TodoBoardProps {
  compact?: boolean;
  groupId?: string;
}

interface Task {
  id: string;
  title: string;
  description?: string;
  status: 'todo' | 'in_progress' | 'done';
  priority: 'low' | 'medium' | 'high';
  subject?: string;
  due_date?: string | null;
  created_at: string;
  updated_at: string;
  user_id: string;
  group_id?: string;
}

export const TodoBoard: React.FC<TodoBoardProps> = ({
  compact = false,
  groupId,
}) => {
  const theme = useDynamicTheme();
  const { user } = useAuthStore();
  const { tasks, fetchTasks, updateTask, deleteTask } = useTasksStore();
  
  const [filteredTasks, setFilteredTasks] = useState<Task[]>([]);

  useEffect(() => {
    if (user) {
      fetchTasks(user.id, groupId);
    }
  }, [user, groupId, fetchTasks]);

  useEffect(() => {
    // Filter tasks based on groupId
    const filtered = tasks.filter(task => {
      if (groupId) {
        return task.group_id === groupId;
      }
      return !task.group_id; // Personal tasks
    });
    setFilteredTasks(filtered);
  }, [tasks, groupId]);

  const getTasksByStatus = (status: 'todo' | 'in_progress' | 'done') => {
    return filteredTasks.filter(task => task.status === status);
  };

  const getPriorityColor = (priority: 'low' | 'medium' | 'high') => {
    switch (priority) {
      case 'high':
        return theme.colors.error;
      case 'medium':
        return theme.colors.tertiary;
      case 'low':
        return theme.colors.outline;
      default:
        return theme.colors.outline;
    }
  };

  const getPriorityIcon = (priority: 'low' | 'medium' | 'high') => {
    switch (priority) {
      case 'high':
        return 'priority-high';
      case 'medium':
        return 'remove';
      case 'low':
        return 'keyboard-arrow-down';
      default:
        return 'remove';
    }
  };

  const handleStatusChange = async (taskId: string, newStatus: 'todo' | 'in_progress' | 'done') => {
    try {
      await updateTask(taskId, { status: newStatus });
    } catch (error) {
      console.error('Error updating task status:', error);
      Alert.alert('Error', 'Failed to update task status');
    }
  };

  const handleDeleteTask = async (taskId: string) => {
    Alert.alert(
      'Delete Task',
      'Are you sure you want to delete this task?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteTask(taskId);
            } catch (error) {
              console.error('Error deleting task:', error);
              Alert.alert('Error', 'Failed to delete task');
            }
          },
        },
      ]
    );
  };

  const renderTask = (task: Task) => (
    <View key={task.id} style={styles.taskCard}>
      <View style={styles.taskHeader}>
        <View style={styles.taskTitleRow}>
          <Text style={styles.taskTitle} numberOfLines={compact ? 1 : 2}>
            {task.title}
          </Text>
          <View style={[styles.priorityIndicator, { backgroundColor: getPriorityColor(task.priority) }]}>
            <MaterialIcons
              name={getPriorityIcon(task.priority)}
              size={12}
              color={theme.colors.surface}
            />
          </View>
        </View>
        
        {!compact && task.description && (
          <Text style={styles.taskDescription} numberOfLines={2}>
            {task.description}
          </Text>
        )}
        
        {task.subject && (
          <Text style={styles.taskSubject}>
            📚 {task.subject}
          </Text>
        )}
      </View>

      <View style={styles.taskActions}>
        {task.status !== 'todo' && (
          <TouchableOpacity
            style={[styles.actionButton, styles.todoButton]}
            onPress={() => handleStatusChange(task.id, 'todo')}
          >
            <MaterialIcons name="undo" size={16} color={theme.colors.onSurfaceVariant} />
          </TouchableOpacity>
        )}
        
        {task.status !== 'in_progress' && (
          <TouchableOpacity
            style={[styles.actionButton, styles.progressButton]}
            onPress={() => handleStatusChange(task.id, 'in_progress')}
          >
            <MaterialIcons name="play-arrow" size={16} color={theme.colors.onPrimary} />
          </TouchableOpacity>
        )}
        
        {task.status !== 'done' && (
          <TouchableOpacity
            style={[styles.actionButton, styles.doneButton]}
            onPress={() => handleStatusChange(task.id, 'done')}
          >
            <MaterialIcons name="check" size={16} color={theme.colors.onSecondary} />
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => handleDeleteTask(task.id)}
        >
          <MaterialIcons name="delete" size={16} color={theme.colors.onError} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderColumn = (title: string, status: 'todo' | 'in_progress' | 'done', icon: string) => {
    const columnTasks = getTasksByStatus(status);
    
    return (
      <View style={[styles.column, compact && styles.compactColumn]}>
        <View style={styles.columnHeader}>
          <MaterialIcons name={icon} size={20} color={theme.colors.primary} />
          <Text style={styles.columnTitle}>{title}</Text>
          <View style={styles.taskCount}>
            <Text style={styles.taskCountText}>{columnTasks.length}</Text>
          </View>
        </View>
        
        <ScrollView
          style={styles.columnContent}
          showsVerticalScrollIndicator={false}
          nestedScrollEnabled
        >
          {columnTasks.length === 0 ? (
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateText}>No tasks</Text>
            </View>
          ) : (
            columnTasks.map(renderTask)
          )}
        </ScrollView>
      </View>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    board: {
      flexDirection: compact ? 'column' : 'row',
      gap: compact ? 12 : 16,
      padding: compact ? 8 : 16,
    },
    column: {
      flex: 1,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 16,
      padding: 12,
      minHeight: compact ? 200 : 400,
    },
    compactColumn: {
      minHeight: 150,
    },
    columnHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
      gap: 8,
    },
    columnTitle: {
      ...ExpressiveTypographyVariants.titleMedium,
      color: theme.colors.onSurfaceVariant,
      fontWeight: '600',
      flex: 1,
    },
    taskCount: {
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
      paddingHorizontal: 8,
      paddingVertical: 2,
      minWidth: 24,
      alignItems: 'center',
    },
    taskCountText: {
      ...ExpressiveTypographyVariants.labelSmall,
      color: theme.colors.onPrimary,
      fontWeight: '600',
    },
    columnContent: {
      flex: 1,
    },
    taskCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 12,
      marginBottom: 8,
      elevation: 2,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
    },
    taskHeader: {
      marginBottom: 8,
    },
    taskTitleRow: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      justifyContent: 'space-between',
      marginBottom: 4,
    },
    taskTitle: {
      ...ExpressiveTypographyVariants.bodyMedium,
      color: theme.colors.onSurface,
      fontWeight: '600',
      flex: 1,
      marginRight: 8,
    },
    priorityIndicator: {
      width: 20,
      height: 20,
      borderRadius: 10,
      alignItems: 'center',
      justifyContent: 'center',
    },
    taskDescription: {
      ...ExpressiveTypographyVariants.bodySmall,
      color: theme.colors.onSurfaceVariant,
      marginBottom: 4,
    },
    taskSubject: {
      ...ExpressiveTypographyVariants.labelSmall,
      color: theme.colors.primary,
      fontWeight: '500',
    },
    taskActions: {
      flexDirection: 'row',
      gap: 6,
      justifyContent: 'flex-end',
    },
    actionButton: {
      width: 28,
      height: 28,
      borderRadius: 14,
      alignItems: 'center',
      justifyContent: 'center',
    },
    todoButton: {
      backgroundColor: theme.colors.surfaceVariant,
    },
    progressButton: {
      backgroundColor: theme.colors.primary,
    },
    doneButton: {
      backgroundColor: theme.colors.secondary,
    },
    deleteButton: {
      backgroundColor: theme.colors.errorContainer,
    },
    emptyState: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 24,
    },
    emptyStateText: {
      ...ExpressiveTypographyVariants.bodyMedium,
      color: theme.colors.onSurfaceVariant,
      opacity: 0.6,
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.board}>
        {renderColumn('To Do', 'todo', 'radio-button-unchecked')}
        {renderColumn('In Progress', 'in_progress', 'play-circle-outline')}
        {renderColumn('Done', 'done', 'check-circle')}
      </View>
    </View>
  );
};
