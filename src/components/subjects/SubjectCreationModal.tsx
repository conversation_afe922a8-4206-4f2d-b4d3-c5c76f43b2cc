import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Modal,
  Pressable,
  Dimensions,
  Vibration,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Surface,
  IconButton,
  Portal,
  HelperText,
} from 'react-native-paper';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
  Easing,
} from 'react-native-reanimated';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { useSubjectStore } from '../../stores/subjectStore';
import { 
  COLOR_PALETTES, 
  ColorUtils, 
  DEFAULT_SUBJECT_COLORS,
  SUBJECT_COLOR_RECOMMENDATIONS 
} from '../../constants/colorPalette';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface SubjectCreationModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess?: (subject: any) => void;
  initialName?: string;
  initialColor?: string;
}

const SubjectCreationModal: React.FC<SubjectCreationModalProps> = ({
  visible,
  onClose,
  onSuccess,
  initialName = '',
  initialColor = '',
}) => {
  const theme = useDynamicTheme();
  const { subjects, createSubject } = useSubjectStore();

  // Form state
  const [subjectName, setSubjectName] = useState(initialName);
  const [selectedColor, setSelectedColor] = useState(initialColor || DEFAULT_SUBJECT_COLORS[0]);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{ name?: string; color?: string }>({});

  // Animation values
  const modalScale = useSharedValue(0);
  const modalOpacity = useSharedValue(0);
  const contentTranslateY = useSharedValue(50);
  const colorGridOpacity = useSharedValue(0);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (visible) {
      setSubjectName(initialName);
      setSelectedColor(initialColor || getRecommendedColor(initialName));
      setErrors({});
      setIsLoading(false);
      
      // Animate in
      modalScale.value = withSpring(1, { damping: 15 });
      modalOpacity.value = withTiming(1, { duration: 300 });
      contentTranslateY.value = withSpring(0, { damping: 15 });
      colorGridOpacity.value = withTiming(1, { duration: 500, easing: Easing.out(Easing.ease) });
    } else {
      // Animate out
      modalScale.value = withTiming(0, { duration: 200 });
      modalOpacity.value = withTiming(0, { duration: 200 });
      contentTranslateY.value = withTiming(50, { duration: 200 });
      colorGridOpacity.value = withTiming(0, { duration: 200 });
    }
  }, [visible, initialName, initialColor]);

  // Get recommended color based on subject name
  const getRecommendedColor = (name: string): string => {
    if (!name) return DEFAULT_SUBJECT_COLORS[0];
    
    const recommendedColors = ColorUtils.getSubjectColors(name);
    const usedColors = subjects.map(s => s.color);
    const availableColors = recommendedColors.filter(color => !usedColors.includes(color));
    
    return availableColors.length > 0 
      ? availableColors[0] 
      : recommendedColors[0];
  };

  // Update recommended color when name changes
  useEffect(() => {
    if (subjectName && !initialColor) {
      const recommendedColor = getRecommendedColor(subjectName);
      setSelectedColor(recommendedColor);
    }
  }, [subjectName, initialColor]);

  // Validation
  const validateForm = (): boolean => {
    const newErrors: { name?: string; color?: string } = {};

    // Name validation
    if (!subjectName.trim()) {
      newErrors.name = 'Subject name is required';
    } else if (subjectName.trim().length < 2) {
      newErrors.name = 'Subject name must be at least 2 characters';
    } else if (subjectName.trim().length > 50) {
      newErrors.name = 'Subject name must be less than 50 characters';
    } else {
      // Check for duplicate names
      const existingSubject = subjects.find(
        s => s.name.toLowerCase() === subjectName.trim().toLowerCase()
      );
      if (existingSubject) {
        newErrors.name = 'A subject with this name already exists';
      }
    }

    // Color validation
    if (!selectedColor) {
      newErrors.color = 'Please select a color';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      Vibration.vibrate(100);
      return;
    }

    setIsLoading(true);

    try {
      const newSubject = await createSubject(subjectName.trim(), selectedColor);
      
      if (newSubject) {
        // Success haptic feedback
        Vibration.vibrate([0, 100, 50, 100]);
        
        onSuccess?.(newSubject);
        onClose();
        
        // Reset form
        setSubjectName('');
        setSelectedColor(DEFAULT_SUBJECT_COLORS[0]);
        setErrors({});
      } else {
        setErrors({ name: 'Failed to create subject. Please try again.' });
        Vibration.vibrate(100);
      }
    } catch (error) {
      console.error('Error creating subject:', error);
      setErrors({ name: 'An error occurred. Please try again.' });
      Vibration.vibrate(100);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle color selection
  const handleColorSelect = (color: string) => {
    setSelectedColor(color);
    setErrors(prev => ({ ...prev, color: undefined }));
    Vibration.vibrate(50);
  };

  // Get available colors (excluding already used ones)
  const getAvailableColors = () => {
    const usedColors = subjects.map(s => s.color);
    return DEFAULT_SUBJECT_COLORS.map(color => ({
      color,
      isUsed: usedColors.includes(color),
    }));
  };

  // Animated styles
  const modalAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: modalScale.value }],
    opacity: modalOpacity.value,
  }));

  const contentAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: contentTranslateY.value }],
  }));

  const colorGridAnimatedStyle = useAnimatedStyle(() => ({
    opacity: colorGridOpacity.value,
  }));

  // Color option component
  const ColorOption = ({ color, isUsed }: { color: string; isUsed: boolean }) => {
    const colorScale = useSharedValue(1);
    const isSelected = selectedColor === color;

    const colorAnimatedStyle = useAnimatedStyle(() => ({
      transform: [{ scale: colorScale.value }],
    }));

    const handlePress = () => {
      if (!isUsed) {
        colorScale.value = withSpring(0.9, { damping: 15 }, () => {
          colorScale.value = withSpring(1, { damping: 15 });
        });
        runOnJS(handleColorSelect)(color);
      }
    };

    return (
      <Animated.View style={colorAnimatedStyle}>
        <Pressable
          onPress={handlePress}
          disabled={isUsed}
          style={[
            styles.colorOption,
            {
              backgroundColor: color,
              opacity: isUsed ? 0.3 : 1,
              borderWidth: isSelected ? 3 : 0,
              borderColor: theme.colors.onSurface,
            },
          ]}
        >
          {isSelected && (
            <IconButton
              icon="check"
              size={20}
              iconColor={ColorUtils.getContrastRatio(color, '#FFFFFF') >= 4.5 ? '#FFFFFF' : '#000000'}
              style={{ margin: 0 }}
            />
          )}
          {isUsed && !isSelected && (
            <IconButton
              icon="close"
              size={16}
              iconColor={ColorUtils.getContrastRatio(color, '#FFFFFF') >= 4.5 ? '#FFFFFF' : '#000000'}
              style={{ margin: 0 }}
            />
          )}
        </Pressable>
      </Animated.View>
    );
  };

  const styles = StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    modal: {
      width: Math.min(screenWidth - 40, 400),
      maxHeight: screenHeight * 0.8,
      borderRadius: 24,
      backgroundColor: theme.colors.surface,
      elevation: 24,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 12 },
      shadowOpacity: 0.3,
      shadowRadius: 24,
    },
    header: {
      padding: 24,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
      alignItems: 'center',
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: theme.colors.onSurface,
    },
    content: {
      padding: 24,
    },
    inputContainer: {
      marginBottom: 24,
    },
    input: {
      backgroundColor: theme.colors.surfaceVariant,
    },
    colorSection: {
      marginBottom: 24,
    },
    colorSectionTitle: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.onSurface,
      marginBottom: 16,
    },
    colorGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
      justifyContent: 'center',
    },
    colorOption: {
      width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    previewSection: {
      marginBottom: 24,
    },
    previewTitle: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.onSurface,
      marginBottom: 12,
    },
    preview: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      borderRadius: 12,
      backgroundColor: `${selectedColor}20`,
      borderWidth: 1,
      borderColor: selectedColor,
    },
    previewColorIndicator: {
      width: 20,
      height: 20,
      borderRadius: 10,
      backgroundColor: selectedColor,
      marginRight: 12,
    },
    previewText: {
      fontSize: 16,
      fontWeight: '500',
      color: selectedColor,
    },
    actions: {
      flexDirection: 'row',
      padding: 24,
      gap: 12,
      borderTopWidth: 1,
      borderTopColor: theme.colors.outline,
    },
    actionButton: {
      flex: 1,
    },
    closeButton: {
      position: 'absolute',
      top: 8,
      right: 8,
      zIndex: 1,
    },
  });

  return (
    <Portal>
      <Modal
        visible={visible}
        transparent
        animationType="none"
        onRequestClose={onClose}
      >
        <KeyboardAvoidingView
          style={styles.overlay}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <Animated.View style={[styles.modal, modalAnimatedStyle]}>
            {/* Close button */}
            <IconButton
              icon="close"
              size={24}
              iconColor={theme.colors.onSurfaceVariant}
              style={styles.closeButton}
              onPress={onClose}
            />

            {/* Header */}
            <View style={styles.header}>
              <Text style={styles.headerTitle}>Create New Subject</Text>
            </View>

            {/* Content */}
            <Animated.View style={[styles.content, contentAnimatedStyle]}>
              {/* Subject name input */}
              <View style={styles.inputContainer}>
                <TextInput
                  label="Subject Name"
                  value={subjectName}
                  onChangeText={(text) => {
                    setSubjectName(text);
                    setErrors(prev => ({ ...prev, name: undefined }));
                  }}
                  mode="outlined"
                  style={styles.input}
                  placeholder="e.g., Mathematics, Physics, History"
                  error={!!errors.name}
                  disabled={isLoading}
                />
                <HelperText type="error" visible={!!errors.name}>
                  {errors.name}
                </HelperText>
              </View>

              {/* Color selection */}
              <View style={styles.colorSection}>
                <Text style={styles.colorSectionTitle}>Choose Color</Text>
                <Animated.View style={[styles.colorGrid, colorGridAnimatedStyle]}>
                  {getAvailableColors().map(({ color, isUsed }) => (
                    <ColorOption key={color} color={color} isUsed={isUsed} />
                  ))}
                </Animated.View>
                <HelperText type="error" visible={!!errors.color}>
                  {errors.color}
                </HelperText>
              </View>

              {/* Preview */}
              {subjectName.trim() && (
                <View style={styles.previewSection}>
                  <Text style={styles.previewTitle}>Preview</Text>
                  <Surface style={styles.preview} elevation={1}>
                    <View style={styles.previewColorIndicator} />
                    <Text style={styles.previewText}>
                      {subjectName.trim()}
                    </Text>
                  </Surface>
                </View>
              )}
            </Animated.View>

            {/* Actions */}
            <View style={styles.actions}>
              <Button
                mode="outlined"
                onPress={onClose}
                style={styles.actionButton}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                mode="contained"
                onPress={handleSubmit}
                style={styles.actionButton}
                loading={isLoading}
                disabled={isLoading || !subjectName.trim()}
              >
                Create Subject
              </Button>
            </View>
          </Animated.View>
        </KeyboardAvoidingView>
      </Modal>
    </Portal>
  );
};

export default SubjectCreationModal;
