import React, { useEffect } from 'react';
import {
  View,
  StyleSheet,
  Pressable,
  Dimensions,
  Vibration,
} from 'react-native';
import {
  Text,
  Surface,
  IconButton,
  ProgressBar,
  Chip,
} from 'react-native-paper';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
  Easing,
} from 'react-native-reanimated';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { Subject } from '../../stores/subjectStore';
import { formatDuration } from '../../utils/timerUtils';
import { ColorUtils } from '../../constants/colorPalette';

const { width: screenWidth } = Dimensions.get('window');

interface SubjectCardProps {
  subject: Subject;
  isSelected?: boolean;
  onPress?: (subject: Subject) => void;
  onLongPress?: (subject: Subject) => void;
  onEdit?: (subject: Subject) => void;
  onDelete?: (subject: Subject) => void;
  showStats?: boolean;
  showProgress?: boolean;
  compact?: boolean;
  style?: any;
}

const SubjectCard: React.FC<SubjectCardProps> = ({
  subject,
  isSelected = false,
  onPress,
  onLongPress,
  onEdit,
  onDelete,
  showStats = true,
  showProgress = false,
  compact = false,
  style,
}) => {
  const theme = useDynamicTheme();

  // Animation values
  const scale = useSharedValue(1);
  const elevation = useSharedValue(2);
  const borderWidth = useSharedValue(0);
  const glowOpacity = useSharedValue(0);
  const progressWidth = useSharedValue(0);

  // Selection animation
  useEffect(() => {
    if (isSelected) {
      borderWidth.value = withSpring(2, { damping: 15 });
      elevation.value = withSpring(8, { damping: 15 });
      glowOpacity.value = withTiming(0.3, { duration: 300 });
    } else {
      borderWidth.value = withSpring(0, { damping: 15 });
      elevation.value = withSpring(2, { damping: 15 });
      glowOpacity.value = withTiming(0, { duration: 300 });
    }
  }, [isSelected]);

  // Progress animation
  useEffect(() => {
    if (showProgress && subject.totalTime) {
      const dailyGoal = 3600; // 1 hour default goal
      const progress = Math.min((subject.todayTime || 0) / dailyGoal, 1);
      progressWidth.value = withTiming(progress, {
        duration: 1000,
        easing: Easing.out(Easing.ease),
      });
    }
  }, [subject.todayTime, showProgress]);

  // Haptic feedback
  const triggerHaptic = (type: 'light' | 'medium' = 'light') => {
    Vibration.vibrate(type === 'light' ? 50 : 100);
  };

  // Press handlers
  const handlePressIn = () => {
    scale.value = withSpring(0.95, { damping: 15 });
    runOnJS(triggerHaptic)('light');
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15 });
  };

  const handlePress = () => {
    onPress?.(subject);
  };

  const handleLongPress = () => {
    runOnJS(triggerHaptic)('medium');
    onLongPress?.(subject);
  };

  // Get contrast color for text
  const getContrastColor = (backgroundColor: string): string => {
    const contrastRatio = ColorUtils.getContrastRatio(backgroundColor, '#FFFFFF');
    return contrastRatio >= 4.5 ? '#FFFFFF' : '#000000';
  };

  // Get color variations
  const colorVariations = ColorUtils.generateVariations(subject.color);
  const textColor = getContrastColor(subject.color);

  // Calculate stats
  const todayTime = subject.todayTime || 0;
  const totalTime = subject.totalTime || 0;
  const sessionsToday = subject.sessionsToday || 0;
  const averageSession = subject.averageSessionLength || 0;

  // Animated styles
  const cardAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    elevation: elevation.value,
    shadowOpacity: interpolate(elevation.value, [2, 8], [0.1, 0.3]),
    shadowRadius: interpolate(elevation.value, [2, 8], [4, 12]),
    borderWidth: borderWidth.value,
    borderColor: subject.color,
  }));

  const glowAnimatedStyle = useAnimatedStyle(() => ({
    opacity: glowOpacity.value,
  }));

  const progressAnimatedStyle = useAnimatedStyle(() => ({
    width: `${progressWidth.value * 100}%`,
  }));

  const styles = StyleSheet.create({
    container: {
      marginVertical: compact ? 4 : 8,
      marginHorizontal: compact ? 8 : 16,
    },
    card: {
      borderRadius: compact ? 12 : 16,
      overflow: 'hidden',
      backgroundColor: theme.colors.surface,
    },
    glowOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: subject.color,
      borderRadius: compact ? 12 : 16,
    },
    content: {
      padding: compact ? 12 : 16,
      position: 'relative',
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: compact ? 8 : 12,
    },
    colorIndicator: {
      width: compact ? 20 : 24,
      height: compact ? 20 : 24,
      borderRadius: compact ? 10 : 12,
      backgroundColor: subject.color,
      marginRight: compact ? 8 : 12,
      elevation: 2,
    },
    subjectInfo: {
      flex: 1,
    },
    subjectName: {
      fontSize: compact ? 16 : 18,
      fontWeight: '600',
      color: theme.colors.onSurface,
      marginBottom: 2,
    },
    subjectMeta: {
      fontSize: compact ? 12 : 14,
      color: theme.colors.onSurfaceVariant,
    },
    actions: {
      flexDirection: 'row',
      gap: 4,
    },
    actionButton: {
      width: compact ? 32 : 36,
      height: compact ? 32 : 36,
    },
    statsContainer: {
      marginTop: compact ? 8 : 12,
      gap: compact ? 6 : 8,
    },
    statsRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    statItem: {
      flex: 1,
      alignItems: 'center',
    },
    statValue: {
      fontSize: compact ? 14 : 16,
      fontWeight: '600',
      color: theme.colors.onSurface,
    },
    statLabel: {
      fontSize: compact ? 10 : 12,
      color: theme.colors.onSurfaceVariant,
      marginTop: 2,
    },
    statSeconds: {
      fontSize: compact ? 8 : 10,
      color: theme.colors.onSurfaceVariant,
      marginTop: 1,
      fontFamily: 'monospace',
      opacity: 0.7,
    },
    progressContainer: {
      marginTop: compact ? 8 : 12,
    },
    progressLabel: {
      fontSize: compact ? 12 : 14,
      color: theme.colors.onSurfaceVariant,
      marginBottom: 4,
    },
    progressTrack: {
      height: compact ? 4 : 6,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: compact ? 2 : 3,
      overflow: 'hidden',
    },
    progressFill: {
      height: '100%',
      backgroundColor: subject.color,
      borderRadius: compact ? 2 : 3,
    },
    tagsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 4,
      marginTop: compact ? 6 : 8,
    },
    tag: {
      backgroundColor: `${subject.color}20`,
    },
    tagText: {
      fontSize: compact ? 10 : 12,
      color: subject.color,
    },
    selectedOverlay: {
      position: 'absolute',
      top: 8,
      right: 8,
      backgroundColor: subject.color,
      borderRadius: 12,
      padding: 4,
    },
    emptyStats: {
      alignItems: 'center',
      padding: compact ? 8 : 12,
    },
    emptyText: {
      fontSize: compact ? 12 : 14,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      fontStyle: 'italic',
    },
  });

  return (
    <View style={[styles.container, style]}>
      <Pressable
        onPress={handlePress}
        onLongPress={handleLongPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
      >
        <Animated.View style={[styles.card, cardAnimatedStyle]}>
          {/* Glow overlay for selection */}
          <Animated.View style={[styles.glowOverlay, glowAnimatedStyle]} />
          
          <View style={styles.content}>
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.colorIndicator} />
              
              <View style={styles.subjectInfo}>
                <Text style={styles.subjectName} numberOfLines={1}>
                  {subject.name}
                </Text>
                {subject.lastStudied && (
                  <Text style={styles.subjectMeta}>
                    Last studied: {new Date(subject.lastStudied).toLocaleDateString()}
                  </Text>
                )}
              </View>

              {/* Action buttons */}
              {(onEdit || onDelete) && (
                <View style={styles.actions}>
                  {onEdit && (
                    <IconButton
                      icon="pencil"
                      size={compact ? 16 : 20}
                      iconColor={theme.colors.onSurfaceVariant}
                      style={styles.actionButton}
                      onPress={() => onEdit(subject)}
                    />
                  )}
                  {onDelete && (
                    <IconButton
                      icon="delete"
                      size={compact ? 16 : 20}
                      iconColor={theme.colors.error}
                      style={styles.actionButton}
                      onPress={() => onDelete(subject)}
                    />
                  )}
                </View>
              )}
            </View>

            {/* Stats */}
            {showStats && (
              <View style={styles.statsContainer}>
                {totalTime > 0 ? (
                  <>
                    <View style={styles.statsRow}>
                      <View style={styles.statItem}>
                        <Text style={styles.statValue}>
                          {formatDuration(todayTime)}
                        </Text>
                        <Text style={styles.statLabel}>Today</Text>
                        <Text style={styles.statSeconds}>
                          {todayTime}s
                        </Text>
                      </View>
                      
                      <View style={styles.statItem}>
                        <Text style={styles.statValue}>
                          {formatDuration(totalTime)}
                        </Text>
                        <Text style={styles.statLabel}>Total</Text>
                      </View>
                      
                      <View style={styles.statItem}>
                        <Text style={styles.statValue}>
                          {sessionsToday}
                        </Text>
                        <Text style={styles.statLabel}>Sessions</Text>
                      </View>
                    </View>

                    {averageSession > 0 && (
                      <View style={styles.statsRow}>
                        <View style={styles.statItem}>
                          <Text style={styles.statValue}>
                            {formatDuration(averageSession)}
                          </Text>
                          <Text style={styles.statLabel}>Avg Session</Text>
                        </View>
                      </View>
                    )}
                  </>
                ) : (
                  <View style={styles.emptyStats}>
                    <Text style={styles.emptyText}>
                      No study sessions yet
                    </Text>
                  </View>
                )}
              </View>
            )}

            {/* Progress bar */}
            {showProgress && todayTime > 0 && (
              <View style={styles.progressContainer}>
                <Text style={styles.progressLabel}>
                  Daily Progress ({Math.round((todayTime / 3600) * 100)}%)
                </Text>
                <View style={styles.progressTrack}>
                  <Animated.View style={[styles.progressFill, progressAnimatedStyle]} />
                </View>
              </View>
            )}

            {/* Tags */}
            {!compact && (
              <View style={styles.tagsContainer}>
                <Chip
                  mode="outlined"
                  style={styles.tag}
                  textStyle={styles.tagText}
                  compact
                >
                  {subject.color.toUpperCase()}
                </Chip>
              </View>
            )}
          </View>

          {/* Selection indicator */}
          {isSelected && (
            <View style={styles.selectedOverlay}>
              <IconButton
                icon="check"
                size={16}
                iconColor="white"
                style={{ margin: 0 }}
              />
            </View>
          )}
        </Animated.View>
      </Pressable>
    </View>
  );
};

export default SubjectCard;
