import React, { useRef, useEffect } from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  StyleSheet,
  Animated,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypographyVariants } from '../../constants/expressiveTheme';
import { getElevationStyle, ColoredElevation } from '../../constants/surfaceElevation';
import { ExpressiveEasing, ExpressiveDurations } from '../../constants/expressiveMotion';

// ===========================================
// 🔘 EXPRESSIVE BUTTON COMPONENT
// ===========================================

export type ButtonVariant = 'filled' | 'outlined' | 'text' | 'elevated' | 'tonal';
export type ButtonSize = 'small' | 'medium' | 'large';

interface ExpressiveButtonProps {
  children?: React.ReactNode;
  title?: string;
  variant?: ButtonVariant;
  size?: ButtonSize;
  colorVariant?: keyof typeof ColoredElevation;
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  onPress?: () => void;
  onLongPress?: () => void;
  style?: ViewStyle;
  textStyle?: TextStyle;
  fullWidth?: boolean;
  borderRadius?: number;
}

export const ExpressiveButton: React.FC<ExpressiveButtonProps> = ({
  children,
  title,
  variant = 'filled',
  size = 'medium',
  colorVariant = 'primary',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  onPress,
  onLongPress,
  style,
  textStyle,
  fullWidth = false,
  borderRadius = 20,
}) => {
  const theme = useDynamicTheme();
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const elevationAnim = useRef(new Animated.Value(0)).current;

  // Button press animations
  const handlePressIn = () => {
    if (disabled || loading) return;
    
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 0.96,
        duration: ExpressiveDurations.micro,
        easing: ExpressiveEasing.standard,
        useNativeDriver: true,
      }),
      Animated.timing(elevationAnim, {
        toValue: variant === 'elevated' ? 2 : 0,
        duration: ExpressiveDurations.micro,
        easing: ExpressiveEasing.standard,
        useNativeDriver: false,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    if (disabled || loading) return;
    
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: ExpressiveDurations.quick,
        easing: ExpressiveEasing.bounce,
        useNativeDriver: true,
      }),
      Animated.timing(elevationAnim, {
        toValue: variant === 'elevated' ? 1 : 0,
        duration: ExpressiveDurations.quick,
        easing: ExpressiveEasing.standard,
        useNativeDriver: false,
      }),
    ]).start();
  };

  // Initialize elevation for elevated variant
  useEffect(() => {
    if (variant === 'elevated') {
      elevationAnim.setValue(1);
    }
  }, [variant, elevationAnim]);

  // Get button colors based on variant
  const getButtonColors = () => {
    const colors = theme.colors;
    
    switch (variant) {
      case 'filled':
        return {
          backgroundColor: disabled ? colors.surfaceDisabled : colors.primary,
          textColor: disabled ? colors.onSurfaceDisabled : colors.onPrimary,
          borderColor: 'transparent',
        };
      
      case 'outlined':
        return {
          backgroundColor: 'transparent',
          textColor: disabled ? colors.onSurfaceDisabled : colors.primary,
          borderColor: disabled ? colors.outline : colors.outline,
        };
      
      case 'text':
        return {
          backgroundColor: 'transparent',
          textColor: disabled ? colors.onSurfaceDisabled : colors.primary,
          borderColor: 'transparent',
        };
      
      case 'elevated':
        return {
          backgroundColor: disabled ? colors.surfaceDisabled : colors.surface,
          textColor: disabled ? colors.onSurfaceDisabled : colors.primary,
          borderColor: 'transparent',
        };
      
      case 'tonal':
        return {
          backgroundColor: disabled ? colors.surfaceDisabled : colors.secondaryContainer,
          textColor: disabled ? colors.onSurfaceDisabled : colors.onSecondaryContainer,
          borderColor: 'transparent',
        };
      
      default:
        return {
          backgroundColor: colors.primary,
          textColor: colors.onPrimary,
          borderColor: 'transparent',
        };
    }
  };

  // Get button size styles
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          paddingHorizontal: 16,
          paddingVertical: 8,
          minHeight: 32,
          typography: ExpressiveTypographyVariants.labelSmall,
        };
      
      case 'large':
        return {
          paddingHorizontal: 32,
          paddingVertical: 16,
          minHeight: 56,
          typography: ExpressiveTypographyVariants.labelLarge,
        };
      
      default: // medium
        return {
          paddingHorizontal: 24,
          paddingVertical: 12,
          minHeight: 44,
          typography: ExpressiveTypographyVariants.labelMedium,
        };
    }
  };

  const buttonColors = getButtonColors();
  const sizeStyles = getSizeStyles();

  const styles = StyleSheet.create({
    container: {
      borderRadius,
      backgroundColor: buttonColors.backgroundColor,
      borderWidth: variant === 'outlined' ? 1 : 0,
      borderColor: buttonColors.borderColor,
      paddingHorizontal: sizeStyles.paddingHorizontal,
      paddingVertical: sizeStyles.paddingVertical,
      minHeight: sizeStyles.minHeight,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      opacity: disabled ? 0.6 : 1,
      alignSelf: fullWidth ? 'stretch' : 'flex-start',
      ...getElevationStyle(variant === 'elevated' ? 1 : 0, colorVariant),
    },
    content: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: icon && title ? 8 : 0,
    },
    text: {
      ...sizeStyles.typography,
      color: buttonColors.textColor,
      fontWeight: '600',
      textAlign: 'center',
    },
    iconContainer: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    loadingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
  });

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator 
            size="small" 
            color={buttonColors.textColor} 
          />
          {title && (
            <Text style={[styles.text, textStyle]}>
              {title}
            </Text>
          )}
        </View>
      );
    }

    if (children) {
      return children;
    }

    return (
      <View style={styles.content}>
        {icon && iconPosition === 'left' && (
          <View style={styles.iconContainer}>
            {icon}
          </View>
        )}
        
        {title && (
          <Text style={[styles.text, textStyle]}>
            {title}
          </Text>
        )}
        
        {icon && iconPosition === 'right' && (
          <View style={styles.iconContainer}>
            {icon}
          </View>
        )}
      </View>
    );
  };

  return (
    <Animated.View
      style={[
        { transform: [{ scale: scaleAnim }] },
        variant === 'elevated' && {
          ...getElevationStyle(1, colorVariant),
          shadowOffset: {
            width: 0,
            height: elevationAnim,
          },
          shadowRadius: elevationAnim.interpolate({
            inputRange: [0, 2],
            outputRange: [0, 8],
          }),
        },
      ]}
    >
      <TouchableOpacity
        style={[styles.container, style]}
        onPress={onPress}
        onLongPress={onLongPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled || loading}
        activeOpacity={0.8}
      >
        {renderContent()}
      </TouchableOpacity>
    </Animated.View>
  );
};

export default ExpressiveButton;
