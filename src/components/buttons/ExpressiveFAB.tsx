import React, { useRef, useEffect } from 'react';
import {
  TouchableOpacity,
  View,
  Text,
  StyleSheet,
  Animated,
  ViewStyle,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypographyVariants } from '../../constants/expressiveTheme';
import { getElevationStyle, ColoredElevation } from '../../constants/surfaceElevation';
import { ExpressiveEasing, ExpressiveDurations } from '../../constants/expressiveMotion';

// ===========================================
// 🎯 EXPRESSIVE FLOATING ACTION BUTTON
// ===========================================

export type FABSize = 'small' | 'medium' | 'large';
export type FABVariant = 'primary' | 'secondary' | 'tertiary' | 'surface';

interface ExpressiveFABProps {
  icon: React.ReactNode;
  label?: string;
  size?: FABSize;
  variant?: FABVariant;
  colorVariant?: keyof typeof ColoredElevation;
  extended?: boolean;
  disabled?: boolean;
  loading?: boolean;
  onPress?: () => void;
  onLongPress?: () => void;
  style?: ViewStyle;
  position?: 'bottomRight' | 'bottomLeft' | 'bottomCenter' | 'topRight' | 'topLeft' | 'center';
}

export const ExpressiveFAB: React.FC<ExpressiveFABProps> = ({
  icon,
  label,
  size = 'medium',
  variant = 'primary',
  colorVariant = 'primary',
  extended = false,
  disabled = false,
  loading = false,
  onPress,
  onLongPress,
  style,
  position = 'bottomRight',
}) => {
  const theme = useDynamicTheme();
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const elevationAnim = useRef(new Animated.Value(6)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const extendAnim = useRef(new Animated.Value(extended ? 1 : 0)).current;

  // Handle press animations
  const handlePressIn = () => {
    if (disabled || loading) return;
    
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 0.92,
        duration: ExpressiveDurations.micro,
        easing: ExpressiveEasing.standard,
        useNativeDriver: true,
      }),
      Animated.timing(elevationAnim, {
        toValue: 12,
        duration: ExpressiveDurations.micro,
        easing: ExpressiveEasing.standard,
        useNativeDriver: false,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    if (disabled || loading) return;
    
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: ExpressiveDurations.short,
        easing: ExpressiveEasing.bounce,
        useNativeDriver: true,
      }),
      Animated.timing(elevationAnim, {
        toValue: 6,
        duration: ExpressiveDurations.short,
        easing: ExpressiveEasing.standard,
        useNativeDriver: false,
      }),
    ]).start();
  };

  // Animate extension
  useEffect(() => {
    Animated.timing(extendAnim, {
      toValue: extended ? 1 : 0,
      duration: ExpressiveDurations.medium,
      easing: ExpressiveEasing.decelerate,
      useNativeDriver: false,
    }).start();
  }, [extended, extendAnim]);

  // Loading rotation animation
  useEffect(() => {
    if (loading) {
      const rotate = () => {
        rotateAnim.setValue(0);
        Animated.timing(rotateAnim, {
          toValue: 1,
          duration: ExpressiveDurations.loading,
          easing: ExpressiveEasing.linear,
          useNativeDriver: true,
        }).start(() => {
          if (loading) rotate();
        });
      };
      rotate();
    } else {
      rotateAnim.setValue(0);
    }
  }, [loading, rotateAnim]);

  // Get FAB colors based on variant
  const getFABColors = () => {
    const colors = theme.colors;
    
    switch (variant) {
      case 'primary':
        return {
          backgroundColor: disabled ? colors.surfaceDisabled : colors.primaryContainer,
          iconColor: disabled ? colors.onSurfaceDisabled : colors.onPrimaryContainer,
          textColor: disabled ? colors.onSurfaceDisabled : colors.onPrimaryContainer,
        };
      
      case 'secondary':
        return {
          backgroundColor: disabled ? colors.surfaceDisabled : colors.secondaryContainer,
          iconColor: disabled ? colors.onSurfaceDisabled : colors.onSecondaryContainer,
          textColor: disabled ? colors.onSurfaceDisabled : colors.onSecondaryContainer,
        };
      
      case 'tertiary':
        return {
          backgroundColor: disabled ? colors.surfaceDisabled : colors.tertiaryContainer,
          iconColor: disabled ? colors.onSurfaceDisabled : colors.onTertiaryContainer,
          textColor: disabled ? colors.onSurfaceDisabled : colors.onTertiaryContainer,
        };
      
      case 'surface':
        return {
          backgroundColor: disabled ? colors.surfaceDisabled : colors.surface,
          iconColor: disabled ? colors.onSurfaceDisabled : colors.primary,
          textColor: disabled ? colors.onSurfaceDisabled : colors.primary,
        };
      
      default:
        return {
          backgroundColor: colors.primaryContainer,
          iconColor: colors.onPrimaryContainer,
          textColor: colors.onPrimaryContainer,
        };
    }
  };

  // Get FAB size styles
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          width: 40,
          height: 40,
          borderRadius: 12,
          iconSize: 18,
        };
      
      case 'large':
        return {
          width: 96,
          height: 96,
          borderRadius: 28,
          iconSize: 36,
        };
      
      default: // medium
        return {
          width: 56,
          height: 56,
          borderRadius: 16,
          iconSize: 24,
        };
    }
  };

  // Get position styles
  const getPositionStyles = () => {
    const margin = 16;
    
    switch (position) {
      case 'bottomLeft':
        return { position: 'absolute', bottom: margin, left: margin };
      case 'bottomCenter':
        return { position: 'absolute', bottom: margin, alignSelf: 'center' };
      case 'bottomRight':
        return { position: 'absolute', bottom: margin, right: margin };
      case 'topLeft':
        return { position: 'absolute', top: margin, left: margin };
      case 'topRight':
        return { position: 'absolute', top: margin, right: margin };
      case 'center':
        return { position: 'absolute', top: '50%', left: '50%', transform: [{ translateX: -28 }, { translateY: -28 }] };
      default:
        return {};
    }
  };

  const fabColors = getFABColors();
  const sizeStyles = getSizeStyles();
  const positionStyles = getPositionStyles();

  const styles = StyleSheet.create({
    container: {
      ...positionStyles,
      zIndex: 1000,
    },
    fab: {
      backgroundColor: fabColors.backgroundColor,
      borderRadius: sizeStyles.borderRadius,
      alignItems: 'center',
      justifyContent: 'center',
      opacity: disabled ? 0.6 : 1,
      ...getElevationStyle(6, colorVariant),
    },
    staticFab: {
      width: sizeStyles.width,
      height: sizeStyles.height,
    },
    extendedFab: {
      height: sizeStyles.height,
      paddingHorizontal: 16,
      flexDirection: 'row',
      alignItems: 'center',
    },
    iconContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      width: sizeStyles.iconSize,
      height: sizeStyles.iconSize,
    },
    label: {
      ...ExpressiveTypographyVariants.labelMedium,
      color: fabColors.textColor,
      marginLeft: 8,
      fontWeight: '600',
    },
  });

  const fabWidth = extendAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [sizeStyles.width, sizeStyles.width + (label ? label.length * 8 + 40 : 0)],
  });

  const labelOpacity = extendAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0, 0, 1],
  });

  const iconRotation = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <View style={[styles.container, style]}>
      <Animated.View
        style={[
          { transform: [{ scale: scaleAnim }] },
          {
            shadowOffset: {
              width: 0,
              height: elevationAnim,
            },
            shadowRadius: elevationAnim.interpolate({
              inputRange: [6, 12],
              outputRange: [12, 24],
            }),
            shadowOpacity: elevationAnim.interpolate({
              inputRange: [6, 12],
              outputRange: [0.3, 0.4],
            }),
          },
        ]}
      >
        <TouchableOpacity
          style={[
            styles.fab,
            extended ? styles.extendedFab : styles.staticFab,
            extended && { width: fabWidth },
          ]}
          onPress={onPress}
          onLongPress={onLongPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          disabled={disabled || loading}
          activeOpacity={0.8}
        >
          <Animated.View
            style={[
              styles.iconContainer,
              loading && { transform: [{ rotate: iconRotation }] },
            ]}
          >
            {icon}
          </Animated.View>
          
          {extended && label && (
            <Animated.Text
              style={[styles.label, { opacity: labelOpacity }]}
              numberOfLines={1}
            >
              {label}
            </Animated.Text>
          )}
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
};

export default ExpressiveFAB;
