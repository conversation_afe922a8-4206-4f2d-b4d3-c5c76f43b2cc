import React, { useState } from 'react';
import { 
  View, 
  Text, 
  ScrollView, 
  StyleSheet, 
  TouchableOpacity, 
  TextInput,
  Alert 
} from 'react-native';
import { useDynamicTheme, useMaterialYou } from '../contexts/DynamicThemeProvider';
import { FallbackSeedColors, type GenerationStyle } from '../constants/dynamicColors';

/**
 * Dynamic Color Demo Component
 * Demonstrates the Material You Dynamic Color System
 */
export const DynamicColorDemo: React.FC = () => {
  const theme = useDynamicTheme();
  const materialYou = useMaterialYou();
  const [customSeedColor, setCustomSeedColor] = useState('#6750A4');

  const generationStyles: GenerationStyle[] = [
    'TONAL_SPOT',
    'VIBRANT', 
    'EXPRESSIVE',
    'SPRITZ',
    'RAINBOW',
    'FRUIT_SALAD',
    'CONTENT',
    'MONOCHROMATIC'
  ];

  const handleSeedColorChange = (color: string) => {
    // Validate hex color format
    const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    if (hexRegex.test(color)) {
      materialYou.setSeedColor(color);
      setCustomSeedColor(color);
    } else {
      Alert.alert('Invalid Color', 'Please enter a valid hex color (e.g., #6750A4)');
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      padding: 16,
    },
    section: {
      marginBottom: 24,
      padding: 16,
      backgroundColor: theme.colors.surfaceContainer,
      borderRadius: 12,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.onSurface,
      marginBottom: 12,
    },
    infoText: {
      fontSize: 14,
      color: theme.colors.onSurfaceVariant,
      marginBottom: 8,
    },
    colorPalette: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      marginTop: 8,
    },
    colorSwatch: {
      width: 40,
      height: 40,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.outline,
    },
    controlButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 8,
      marginRight: 8,
      marginBottom: 8,
    },
    controlButtonText: {
      color: theme.colors.onPrimary,
      fontWeight: '500',
    },
    secondaryButton: {
      backgroundColor: theme.colors.secondaryContainer,
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 8,
      marginRight: 8,
      marginBottom: 8,
    },
    secondaryButtonText: {
      color: theme.colors.onSecondaryContainer,
      fontWeight: '500',
    },
    input: {
      backgroundColor: theme.colors.surfaceVariant,
      color: theme.colors.onSurfaceVariant,
      padding: 12,
      borderRadius: 8,
      fontSize: 16,
      marginBottom: 12,
    },
    themeToggleContainer: {
      flexDirection: 'row',
      gap: 8,
    },
    seedColorContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      marginTop: 8,
    },
    seedColorButton: {
      width: 50,
      height: 50,
      borderRadius: 25,
      borderWidth: 2,
      borderColor: theme.colors.outline,
      justifyContent: 'center',
      alignItems: 'center',
    },
    selectedSeedColor: {
      borderColor: theme.colors.primary,
      borderWidth: 3,
    },
    styleButton: {
      backgroundColor: theme.colors.tertiaryContainer,
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      marginRight: 8,
      marginBottom: 8,
    },
    styleButtonText: {
      color: theme.colors.onTertiaryContainer,
      fontSize: 12,
      fontWeight: '500',
    },
    activeStyleButton: {
      backgroundColor: theme.colors.tertiary,
    },
    activeStyleButtonText: {
      color: theme.colors.onTertiary,
    },
  });

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.primary }]}>
          Dynamic Color System
        </Text>
        <Text style={styles.infoText}>
          Material You Integration with Android 12+ Support
        </Text>
        <Text style={styles.infoText}>
          Material You Supported: {materialYou.isSupported ? '✅' : '❌'}
        </Text>
        <Text style={styles.infoText}>
          Dynamic Colors Enabled: {materialYou.isEnabled ? '✅' : '❌'}
        </Text>
        <Text style={styles.infoText}>
          Current Seed Color: {materialYou.getCurrentSeedColor()}
        </Text>
      </View>

      {/* Theme Controls */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Theme Controls</Text>
        <View style={styles.themeToggleContainer}>
          <TouchableOpacity 
            style={styles.controlButton}
            onPress={() => theme.setColorScheme('light')}
          >
            <Text style={styles.controlButtonText}>Light</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.controlButton}
            onPress={() => theme.setColorScheme('dark')}
          >
            <Text style={styles.controlButtonText}>Dark</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.controlButton}
            onPress={() => theme.setColorScheme('auto')}
          >
            <Text style={styles.controlButtonText}>Auto</Text>
          </TouchableOpacity>
        </View>
        
        <TouchableOpacity 
          style={styles.secondaryButton}
          onPress={() => theme.setDynamicColorsEnabled(!materialYou.isEnabled)}
        >
          <Text style={styles.secondaryButtonText}>
            {materialYou.isEnabled ? 'Disable' : 'Enable'} Dynamic Colors
          </Text>
        </TouchableOpacity>
      </View>

      {/* Seed Color Selection */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Seed Color</Text>
        <Text style={styles.infoText}>
          Choose a seed color to generate the Material You palette
        </Text>
        
        <TextInput
          style={styles.input}
          value={customSeedColor}
          onChangeText={setCustomSeedColor}
          onSubmitEditing={() => handleSeedColorChange(customSeedColor)}
          placeholder="Enter hex color (e.g., #6750A4)"
          placeholderTextColor={theme.colors.onSurfaceVariant}
        />
        
        <View style={styles.seedColorContainer}>
          {Object.entries(FallbackSeedColors).map(([name, color]) => (
            <TouchableOpacity
              key={name}
              style={[
                styles.seedColorButton,
                { backgroundColor: color },
                materialYou.getCurrentSeedColor() === color && styles.selectedSeedColor
              ]}
              onPress={() => handleSeedColorChange(color)}
            />
          ))}
        </View>
      </View>

      {/* Generation Styles */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Generation Styles</Text>
        <Text style={styles.infoText}>
          Different styles create different color harmonies
        </Text>
        
        <View style={styles.colorPalette}>
          {generationStyles.map((style) => (
            <TouchableOpacity
              key={style}
              style={[
                styles.styleButton,
                theme.config.generationStyle === style && styles.activeStyleButton
              ]}
              onPress={() => materialYou.setGenerationStyle(style)}
            >
              <Text style={[
                styles.styleButtonText,
                theme.config.generationStyle === style && styles.activeStyleButtonText
              ]}>
                {style.replace('_', ' ')}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Material You Palette Preview */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Material You Palette</Text>
        
        <Text style={[styles.infoText, { marginTop: 8 }]}>Accent 1 (Primary)</Text>
        <View style={styles.colorPalette}>
          {materialYou.palette.system_accent1.map((color, index) => (
            <View
              key={index}
              style={[styles.colorSwatch, { backgroundColor: color }]}
            />
          ))}
        </View>
        
        <Text style={[styles.infoText, { marginTop: 8 }]}>Accent 2 (Secondary)</Text>
        <View style={styles.colorPalette}>
          {materialYou.palette.system_accent2.map((color, index) => (
            <View
              key={index}
              style={[styles.colorSwatch, { backgroundColor: color }]}
            />
          ))}
        </View>
        
        <Text style={[styles.infoText, { marginTop: 8 }]}>Accent 3 (Tertiary)</Text>
        <View style={styles.colorPalette}>
          {materialYou.palette.system_accent3.map((color, index) => (
            <View
              key={index}
              style={[styles.colorSwatch, { backgroundColor: color }]}
            />
          ))}
        </View>
        
        <Text style={[styles.infoText, { marginTop: 8 }]}>Neutral 1 (Surface)</Text>
        <View style={styles.colorPalette}>
          {materialYou.palette.system_neutral1.map((color, index) => (
            <View
              key={index}
              style={[styles.colorSwatch, { backgroundColor: color }]}
            />
          ))}
        </View>
      </View>

      {/* Current Theme Colors */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Current Theme Colors</Text>
        
        <View style={styles.colorPalette}>
          <View style={[styles.colorSwatch, { backgroundColor: theme.colors.primary }]} />
          <View style={[styles.colorSwatch, { backgroundColor: theme.colors.secondary }]} />
          <View style={[styles.colorSwatch, { backgroundColor: theme.colors.tertiary }]} />
          <View style={[styles.colorSwatch, { backgroundColor: theme.colors.surface }]} />
          <View style={[styles.colorSwatch, { backgroundColor: theme.colors.background }]} />
        </View>
        
        <Text style={styles.infoText}>
          Primary • Secondary • Tertiary • Surface • Background
        </Text>
      </View>

      {/* Reset Button */}
      <View style={styles.section}>
        <TouchableOpacity 
          style={styles.secondaryButton}
          onPress={theme.resetToDefault}
        >
          <Text style={styles.secondaryButtonText}>Reset to Default</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

export default DynamicColorDemo;
