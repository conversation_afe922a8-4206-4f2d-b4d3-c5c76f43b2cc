import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from 'react-native';
import { useDynamicTheme } from '../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../constants/expressiveTheme';
import ExpressiveSurface from './surfaces/ExpressiveSurface';
import ExpressiveCard from './surfaces/ExpressiveCard';
import {
  ElevationLevel,
  SurfaceContainer,
  ColoredElevation,
  getElevationStyle,
  getSurfaceContainerColor,
} from '../constants/surfaceElevation';

/**
 * Surface Elevation Demo Component
 * Demonstrates the Material 3 Expressive Surface Elevation System
 */
export const SurfaceElevationDemo: React.FC = () => {
  const theme = useDynamicTheme();
  const [selectedElevation, setSelectedElevation] = useState<ElevationLevel>(2);
  const [selectedColorVariant, setSelectedColorVariant] = useState<keyof typeof ColoredElevation>('primary');
  const animatedElevation = useRef(new Animated.Value(2)).current;

  const elevationLevels: ElevationLevel[] = [0, 1, 2, 3, 4, 5];
  const colorVariants: (keyof typeof ColoredElevation)[] = ['primary', 'secondary', 'tertiary', 'success', 'warning', 'error'];
  const surfaceContainers: SurfaceContainer[] = ['surface', 'surfaceContainer', 'surfaceContainerLow', 'surfaceContainerHigh', 'surfaceContainerHighest'];

  const handleElevationChange = (level: ElevationLevel) => {
    setSelectedElevation(level);
    Animated.timing(animatedElevation, {
      toValue: level,
      duration: 300,
      useNativeDriver: false,
    }).start();
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      padding: 16,
    },
    section: {
      marginBottom: 32,
    },
    sectionTitle: {
      ...ExpressiveTypography.headlineMedium,
      color: theme.colors.primary,
      marginBottom: 16,
    },
    subsectionTitle: {
      ...ExpressiveTypography.titleMedium,
      color: theme.colors.onSurface,
      marginBottom: 12,
    },
    description: {
      ...ExpressiveTypography.bodyMedium,
      color: theme.colors.onSurfaceVariant,
      marginBottom: 16,
      lineHeight: 20,
    },
    elevationGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
      marginBottom: 24,
    },
    elevationSample: {
      width: 80,
      height: 80,
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 12,
    },
    elevationLabel: {
      ...ExpressiveTypography.labelMedium,
      color: theme.colors.onSurface,
      fontWeight: '600',
    },
    controlsRow: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      marginBottom: 16,
    },
    controlButton: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      backgroundColor: theme.colors.secondaryContainer,
    },
    activeControlButton: {
      backgroundColor: theme.colors.primary,
    },
    controlButtonText: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onSecondaryContainer,
      fontSize: 10,
    },
    activeControlButtonText: {
      color: theme.colors.onPrimary,
    },
    animatedSample: {
      width: 120,
      height: 120,
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 16,
      alignSelf: 'center',
      marginVertical: 16,
    },
    cardGrid: {
      gap: 16,
    },
    demoCard: {
      marginBottom: 12,
    },
    surfaceContainerRow: {
      flexDirection: 'row',
      gap: 8,
      marginBottom: 16,
    },
    surfaceContainerSample: {
      flex: 1,
      height: 60,
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 8,
    },
    surfaceContainerLabel: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onSurface,
      textAlign: 'center',
      fontSize: 9,
    },
  });

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Surface Elevation System</Text>
        <Text style={styles.description}>
          Material 3 Expressive elevation with enhanced shadows, colored variants, and surface containers.
        </Text>
      </View>

      {/* Elevation Levels */}
      <View style={styles.section}>
        <Text style={styles.subsectionTitle}>Elevation Levels</Text>
        <View style={styles.elevationGrid}>
          {elevationLevels.map((level) => (
            <ExpressiveSurface
              key={level}
              elevation={level}
              style={[
                styles.elevationSample,
                { backgroundColor: getSurfaceContainerColor('surfaceContainer', theme.isDark) }
              ]}
            >
              <Text style={styles.elevationLabel}>Level {level}</Text>
            </ExpressiveSurface>
          ))}
        </View>
      </View>

      {/* Colored Elevation */}
      <View style={styles.section}>
        <Text style={styles.subsectionTitle}>Colored Elevation</Text>
        <Text style={styles.description}>
          Select a color variant to see colored shadows:
        </Text>
        
        <View style={styles.controlsRow}>
          {colorVariants.map((variant) => (
            <TouchableOpacity
              key={variant}
              style={[
                styles.controlButton,
                selectedColorVariant === variant && styles.activeControlButton,
              ]}
              onPress={() => setSelectedColorVariant(variant)}
            >
              <Text
                style={[
                  styles.controlButtonText,
                  selectedColorVariant === variant && styles.activeControlButtonText,
                ]}
              >
                {variant.toUpperCase()}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.elevationGrid}>
          {elevationLevels.slice(1).map((level) => (
            <ExpressiveSurface
              key={level}
              elevation={level}
              colorVariant={selectedColorVariant}
              style={[
                styles.elevationSample,
                { backgroundColor: getSurfaceContainerColor('surfaceContainer', theme.isDark) }
              ]}
            >
              <Text style={styles.elevationLabel}>Level {level}</Text>
            </ExpressiveSurface>
          ))}
        </View>
      </View>

      {/* Animated Elevation */}
      <View style={styles.section}>
        <Text style={styles.subsectionTitle}>Animated Elevation</Text>
        <Text style={styles.description}>
          Tap elevation levels to see smooth transitions:
        </Text>
        
        <View style={styles.controlsRow}>
          {elevationLevels.map((level) => (
            <TouchableOpacity
              key={level}
              style={[
                styles.controlButton,
                selectedElevation === level && styles.activeControlButton,
              ]}
              onPress={() => handleElevationChange(level)}
            >
              <Text
                style={[
                  styles.controlButtonText,
                  selectedElevation === level && styles.activeControlButtonText,
                ]}
              >
                {level}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <ExpressiveSurface
          animatedElevation={animatedElevation}
          colorVariant={selectedColorVariant}
          style={[
            styles.animatedSample,
            { backgroundColor: getSurfaceContainerColor('surfaceContainer', theme.isDark) }
          ]}
        >
          <Text style={styles.elevationLabel}>
            Level {selectedElevation}
          </Text>
        </ExpressiveSurface>
      </View>

      {/* Surface Containers */}
      <View style={styles.section}>
        <Text style={styles.subsectionTitle}>Surface Containers</Text>
        <Text style={styles.description}>
          Different surface container background colors:
        </Text>
        
        {surfaceContainers.map((container) => (
          <View key={container} style={styles.surfaceContainerRow}>
            <ExpressiveSurface
              elevation={1}
              container={container}
              style={styles.surfaceContainerSample}
            >
              <Text style={styles.surfaceContainerLabel}>
                {container.replace(/([A-Z])/g, ' $1').toLowerCase()}
              </Text>
            </ExpressiveSurface>
          </View>
        ))}
      </View>

      {/* Card Examples */}
      <View style={styles.section}>
        <Text style={styles.subsectionTitle}>Expressive Cards</Text>
        <View style={styles.cardGrid}>
          <ExpressiveCard
            title="Elevated Card"
            subtitle="Default elevation with primary color"
            description="This card uses the standard elevated variant with level 2 elevation."
            elevation={2}
            colorVariant="primary"
            style={styles.demoCard}
          />
          
          <ExpressiveCard
            title="Filled Card"
            subtitle="Filled variant with no elevation"
            description="This card uses the filled variant with a solid background."
            variant="filled"
            style={styles.demoCard}
          />
          
          <ExpressiveCard
            title="Outlined Card"
            subtitle="Outlined variant with border"
            description="This card uses the outlined variant with a border and no elevation."
            variant="outlined"
            style={styles.demoCard}
          />
          
          <ExpressiveCard
            title="Interactive Card"
            subtitle="Tap to see animation"
            description="This card has press animations and colored elevation."
            elevation={3}
            colorVariant="tertiary"
            onPress={() => console.log('Card pressed!')}
            style={styles.demoCard}
          />
        </View>
      </View>
    </ScrollView>
  );
};

export default SurfaceElevationDemo;
