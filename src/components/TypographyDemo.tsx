import React from 'react';
import { View, Text, ScrollView, StyleSheet } from 'react-native';
import {
  ExpressiveTypography,
  ExpressiveTypographyVariants,
  EmotionalTypography,
  expressiveLightTheme,
} from '../constants/expressiveTheme';

/**
 * Typography Demo Component
 * Demonstrates the Material 3 Expressive Typography System
 */
export const TypographyDemo: React.FC = () => {
  const theme = expressiveLightTheme;

  return (
    <ScrollView style={styles.container}>
      <View style={styles.section}>
        <Text style={[ExpressiveTypography.displayLarge, { color: theme.colors.primary }]}>
          Typography Demo
        </Text>
        <Text style={[ExpressiveTypography.bodyMedium, { color: theme.colors.onSurface }]}>
          Material 3 Expressive Typography System
        </Text>
      </View>

      {/* Display Styles */}
      <View style={styles.section}>
        <Text style={[ExpressiveTypography.headlineMedium, { color: theme.colors.primary }]}>
          Display Styles
        </Text>
        <Text style={[ExpressiveTypography.displayLarge, { color: theme.colors.onSurface }]}>
          Display Large
        </Text>
        <Text style={[ExpressiveTypography.displayMedium, { color: theme.colors.onSurface }]}>
          Display Medium
        </Text>
        <Text style={[ExpressiveTypography.displaySmall, { color: theme.colors.onSurface }]}>
          Display Small
        </Text>
      </View>

      {/* Headline Styles */}
      <View style={styles.section}>
        <Text style={[ExpressiveTypography.headlineMedium, { color: theme.colors.primary }]}>
          Headline Styles
        </Text>
        <Text style={[ExpressiveTypography.headlineLarge, { color: theme.colors.onSurface }]}>
          Headline Large
        </Text>
        <Text style={[ExpressiveTypography.headlineMedium, { color: theme.colors.onSurface }]}>
          Headline Medium
        </Text>
        <Text style={[ExpressiveTypography.headlineSmall, { color: theme.colors.onSurface }]}>
          Headline Small
        </Text>
      </View>

      {/* Body Styles */}
      <View style={styles.section}>
        <Text style={[ExpressiveTypography.headlineMedium, { color: theme.colors.primary }]}>
          Body Styles
        </Text>
        <Text style={[ExpressiveTypography.bodyLarge, { color: theme.colors.onSurface }]}>
          Body Large - This is the main body text style for longer content and paragraphs.
        </Text>
        <Text style={[ExpressiveTypography.bodyMedium, { color: theme.colors.onSurface }]}>
          Body Medium - This is used for secondary body text and descriptions.
        </Text>
        <Text style={[ExpressiveTypography.bodySmall, { color: theme.colors.onSurface }]}>
          Body Small - This is used for captions and small text elements.
        </Text>
      </View>

      {/* Typography Variants */}
      <View style={styles.section}>
        <Text style={[ExpressiveTypography.headlineMedium, { color: theme.colors.primary }]}>
          Typography Variants
        </Text>
        <Text style={[ExpressiveTypographyVariants.timerDisplay, { color: theme.colors.primary }]}>
          25:00
        </Text>
        <Text style={[ExpressiveTypographyVariants.subjectLabel, { color: theme.colors.secondary }]}>
          MATHEMATICS
        </Text>
        <Text style={[ExpressiveTypographyVariants.statisticValue, { color: theme.colors.onSurface }]}>
          2h 45m
        </Text>
        <Text style={[ExpressiveTypographyVariants.statisticLabel, { color: theme.colors.onSurfaceVariant }]}>
          STUDY TIME TODAY
        </Text>
      </View>

      {/* Emotional Typography */}
      <View style={styles.section}>
        <Text style={[ExpressiveTypography.headlineMedium, { color: theme.colors.primary }]}>
          Emotional Typography
        </Text>
        <Text style={[EmotionalTypography.focus, { color: theme.expressive.emotions.focus }]}>
          Focus Mode Active
        </Text>
        <Text style={[EmotionalTypography.energy, { color: theme.expressive.emotions.energy }]}>
          Let's Get Started!
        </Text>
        <Text style={[EmotionalTypography.calm, { color: theme.expressive.emotions.calm }]}>
          Take a peaceful break
        </Text>
        <Text style={[EmotionalTypography.success, { color: theme.expressive.emotions.success }]}>
          Session Completed!
        </Text>
        <Text style={[EmotionalTypography.alert, { color: theme.expressive.emotions.alert }]}>
          Attention Required
        </Text>
        <Text style={[EmotionalTypography.creativity, { color: theme.expressive.emotions.creativity }]}>
          Creative Thinking Time
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFBFE', // Light theme background
    padding: 16,
  },
  section: {
    marginBottom: 32,
  },
});

export default TypographyDemo;
