import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  ScrollView,
  Alert,
  Animated,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypographyVariants } from '../../constants/expressiveTheme';
import { MaterialIcons } from '@expo/vector-icons';
import { useAuthStore } from '../../stores/authStore';
import DateTimePicker from '@react-native-community/datetimepicker';

interface Exam {
  id: string;
  name: string;
  date: string;
  time: string;
  userId: string;
}

interface TimeLeft {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

export const ExamCountdown: React.FC = () => {
  const theme = useDynamicTheme();
  const { user } = useAuthStore();
  
  const [exams, setExams] = useState<Exam[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [showAddExam, setShowAddExam] = useState(false);
  const [timeLeft, setTimeLeft] = useState<Record<string, TimeLeft>>({});
  const [closestExam, setClosestExam] = useState<{id: string, name: string, days: number} | null>(null);
  const [loading, setLoading] = useState(true);
  
  // New exam form state
  const [newExam, setNewExam] = useState({
    name: '',
    date: new Date(),
    time: '09:00',
  });
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [editingExam, setEditingExam] = useState<string | null>(null);

  // Animation values
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    loadExams();
    
    // Start pulse animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.05,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Fade in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      updateCountdowns();
    }, 1000);

    return () => clearInterval(interval);
  }, [exams]);

  const loadExams = async () => {
    try {
      // TODO: Load from Supabase
      // For now, use mock data or localStorage
      setExams([]);
      setLoading(false);
    } catch (error) {
      console.error('Error loading exams:', error);
      setLoading(false);
    }
  };

  const updateCountdowns = () => {
    const now = new Date();
    const newTimeLeft: Record<string, TimeLeft> = {};
    let closest: {id: string, name: string, days: number} | null = null;
    let minDays = Infinity;

    exams.forEach(exam => {
      const examDateTime = new Date(`${exam.date}T${exam.time}`);
      const timeDiff = examDateTime.getTime() - now.getTime();

      if (timeDiff > 0) {
        const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

        newTimeLeft[exam.id] = { days, hours, minutes, seconds };

        if (days < minDays) {
          minDays = days;
          closest = { id: exam.id, name: exam.name, days };
        }
      }
    });

    setTimeLeft(newTimeLeft);
    setClosestExam(closest);
  };

  const handleAddExam = async () => {
    if (!newExam.name.trim()) {
      Alert.alert('Error', 'Please enter an exam name');
      return;
    }

    if (!user) {
      Alert.alert('Error', 'You must be logged in to add exams');
      return;
    }

    try {
      const examToAdd: Exam = {
        id: Date.now().toString(),
        name: newExam.name.trim(),
        date: newExam.date.toISOString().split('T')[0],
        time: newExam.time,
        userId: user.id,
      };

      setExams(prev => [...prev, examToAdd]);
      setNewExam({ name: '', date: new Date(), time: '09:00' });
      setShowAddExam(false);
      
      Alert.alert('Success', 'Exam added successfully!');
    } catch (error) {
      console.error('Error adding exam:', error);
      Alert.alert('Error', 'Failed to add exam');
    }
  };

  const handleDeleteExam = (examId: string) => {
    Alert.alert(
      'Delete Exam',
      'Are you sure you want to delete this exam?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setExams(prev => prev.filter(exam => exam.id !== examId));
          },
        },
      ]
    );
  };

  const formatTimeLeft = (time: TimeLeft): string => {
    if (time.days > 0) {
      return `${time.days}d ${time.hours}h`;
    } else if (time.hours > 0) {
      return `${time.hours}h ${time.minutes}m`;
    } else {
      return `${time.minutes}m ${time.seconds}s`;
    }
  };

  const styles = StyleSheet.create({
    container: {
      alignItems: 'center',
      marginVertical: 10,
    },
    countdownCard: {
      backgroundColor: theme.colors.primaryContainer,
      borderRadius: 20,
      paddingHorizontal: 20,
      paddingVertical: 12,
      alignItems: 'center',
      elevation: 4,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      minWidth: 200,
    },
    countdownText: {
      ...ExpressiveTypographyVariants.labelMedium,
      color: theme.colors.onPrimaryContainer,
      textAlign: 'center',
    },
    examName: {
      ...ExpressiveTypographyVariants.titleSmall,
      color: theme.colors.onPrimaryContainer,
      fontWeight: '600',
      marginBottom: 4,
    },
    timeDisplay: {
      ...ExpressiveTypographyVariants.headlineSmall,
      color: theme.colors.onPrimaryContainer,
      fontWeight: '700',
    },
    noExamsText: {
      ...ExpressiveTypographyVariants.bodyMedium,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      backgroundColor: theme.colors.surface,
      borderRadius: 24,
      padding: 24,
      margin: 20,
      maxHeight: '80%',
      width: '90%',
      elevation: 12,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 6 },
      shadowOpacity: 0.4,
      shadowRadius: 12,
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
    },
    modalTitle: {
      ...ExpressiveTypographyVariants.headlineSmall,
      color: theme.colors.onSurface,
      fontWeight: '600',
    },
    closeButton: {
      padding: 8,
    },
    examsList: {
      maxHeight: 300,
    },
    examItem: {
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    examInfo: {
      flex: 1,
    },
    examItemName: {
      ...ExpressiveTypographyVariants.titleSmall,
      color: theme.colors.onSurfaceVariant,
      fontWeight: '600',
    },
    examDateTime: {
      ...ExpressiveTypographyVariants.bodySmall,
      color: theme.colors.onSurfaceVariant,
      opacity: 0.8,
    },
    examCountdown: {
      ...ExpressiveTypographyVariants.labelLarge,
      color: theme.colors.primary,
      fontWeight: '600',
    },
    deleteButton: {
      padding: 8,
      marginLeft: 8,
    },
    addButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
      paddingVertical: 12,
      paddingHorizontal: 20,
      alignItems: 'center',
      marginTop: 16,
    },
    addButtonText: {
      ...ExpressiveTypographyVariants.labelLarge,
      color: theme.colors.onPrimary,
      fontWeight: '600',
    },
    input: {
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 12,
      paddingHorizontal: 16,
      paddingVertical: 12,
      marginBottom: 16,
      color: theme.colors.onSurfaceVariant,
      ...ExpressiveTypographyVariants.bodyLarge,
    },
    dateTimeRow: {
      flexDirection: 'row',
      gap: 12,
      marginBottom: 16,
    },
    dateTimeButton: {
      flex: 1,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 12,
      paddingVertical: 12,
      paddingHorizontal: 16,
      alignItems: 'center',
    },
    dateTimeButtonText: {
      ...ExpressiveTypographyVariants.bodyMedium,
      color: theme.colors.onSurfaceVariant,
    },
    actionButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: 12,
      marginTop: 20,
    },
    button: {
      flex: 1,
      paddingVertical: 14,
      borderRadius: 12,
      alignItems: 'center',
    },
    primaryButton: {
      backgroundColor: theme.colors.primary,
    },
    secondaryButton: {
      backgroundColor: theme.colors.surfaceVariant,
    },
    buttonText: {
      ...ExpressiveTypographyVariants.labelLarge,
      fontWeight: '600',
    },
    primaryButtonText: {
      color: theme.colors.onPrimary,
    },
    secondaryButtonText: {
      color: theme.colors.onSurfaceVariant,
    },
  });

  if (loading) {
    return (
      <View style={styles.container}>
        <Text style={styles.noExamsText}>Loading...</Text>
      </View>
    );
  }

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      <TouchableOpacity
        style={styles.countdownCard}
        onPress={() => setShowModal(true)}
        activeOpacity={0.8}
      >
        <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
          {closestExam && timeLeft[closestExam.id] ? (
            <>
              <Text style={styles.examName} numberOfLines={1}>
                {closestExam.name}
              </Text>
              <Text style={styles.timeDisplay}>
                {formatTimeLeft(timeLeft[closestExam.id])}
              </Text>
              <Text style={styles.countdownText}>until exam</Text>
            </>
          ) : (
            <>
              <MaterialIcons
                name="event"
                size={24}
                color={theme.colors.onPrimaryContainer}
              />
              <Text style={styles.countdownText}>No upcoming exams</Text>
            </>
          )}
        </Animated.View>
      </TouchableOpacity>

      {/* Exams Modal */}
      <Modal
        visible={showModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Exam Schedule</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowModal(false)}
              >
                <MaterialIcons
                  name="close"
                  size={24}
                  color={theme.colors.onSurface}
                />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.examsList} showsVerticalScrollIndicator={false}>
              {exams.length === 0 ? (
                <Text style={styles.noExamsText}>
                  No exams scheduled. Add your first exam below!
                </Text>
              ) : (
                exams.map(exam => (
                  <View key={exam.id} style={styles.examItem}>
                    <View style={styles.examInfo}>
                      <Text style={styles.examItemName}>{exam.name}</Text>
                      <Text style={styles.examDateTime}>
                        {new Date(exam.date).toLocaleDateString()} at {exam.time}
                      </Text>
                      {timeLeft[exam.id] && (
                        <Text style={styles.examCountdown}>
                          {formatTimeLeft(timeLeft[exam.id])} remaining
                        </Text>
                      )}
                    </View>
                    <TouchableOpacity
                      style={styles.deleteButton}
                      onPress={() => handleDeleteExam(exam.id)}
                    >
                      <MaterialIcons
                        name="delete"
                        size={20}
                        color={theme.colors.error}
                      />
                    </TouchableOpacity>
                  </View>
                ))
              )}
            </ScrollView>

            <TouchableOpacity
              style={styles.addButton}
              onPress={() => setShowAddExam(true)}
            >
              <Text style={styles.addButtonText}>Add New Exam</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Add Exam Modal */}
      <Modal
        visible={showAddExam}
        transparent
        animationType="fade"
        onRequestClose={() => setShowAddExam(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Add New Exam</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowAddExam(false)}
              >
                <MaterialIcons
                  name="close"
                  size={24}
                  color={theme.colors.onSurface}
                />
              </TouchableOpacity>
            </View>

            <TextInput
              style={styles.input}
              placeholder="Exam name"
              placeholderTextColor={theme.colors.onSurfaceVariant}
              value={newExam.name}
              onChangeText={(text) => setNewExam(prev => ({ ...prev, name: text }))}
            />

            <View style={styles.dateTimeRow}>
              <TouchableOpacity
                style={styles.dateTimeButton}
                onPress={() => setShowDatePicker(true)}
              >
                <Text style={styles.dateTimeButtonText}>
                  📅 {newExam.date.toLocaleDateString()}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.dateTimeButton}
                onPress={() => setShowTimePicker(true)}
              >
                <Text style={styles.dateTimeButtonText}>
                  🕐 {newExam.time}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={[styles.button, styles.secondaryButton]}
                onPress={() => setShowAddExam(false)}
              >
                <Text style={[styles.buttonText, styles.secondaryButtonText]}>
                  Cancel
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.button, styles.primaryButton]}
                onPress={handleAddExam}
              >
                <Text style={[styles.buttonText, styles.primaryButtonText]}>
                  Add Exam
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Date Picker */}
      {showDatePicker && (
        <DateTimePicker
          value={newExam.date}
          mode="date"
          display="default"
          onChange={(event, selectedDate) => {
            setShowDatePicker(false);
            if (selectedDate) {
              setNewExam(prev => ({ ...prev, date: selectedDate }));
            }
          }}
        />
      )}

      {/* Time Picker */}
      {showTimePicker && (
        <DateTimePicker
          value={new Date(`2000-01-01T${newExam.time}`)}
          mode="time"
          display="default"
          onChange={(event, selectedTime) => {
            setShowTimePicker(false);
            if (selectedTime) {
              const timeString = selectedTime.toTimeString().slice(0, 5);
              setNewExam(prev => ({ ...prev, time: timeString }));
            }
          }}
        />
      )}
    </Animated.View>
  );
};
