import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  ScrollView,
  Alert,
  Animated,
  Linking,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypographyVariants } from '../../constants/expressiveTheme';
import { MaterialIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface PlaylistItem {
  id: string;
  name: string;
  url: string;
  description?: string;
}

interface SpotifyBarProps {
  isCollapsed?: boolean;
  onToggleCollapse?: (collapsed: boolean) => void;
}

const DEFAULT_STUDY_PLAYLISTS: PlaylistItem[] = [
  {
    id: '1',
    name: 'Deep Focus',
    url: 'https://open.spotify.com/playlist/37i9dQZF1DWZeKCadgRdKQ',
    description: 'Instrumental music for deep concentration',
  },
  {
    id: '2',
    name: 'Peaceful Piano',
    url: 'https://open.spotify.com/playlist/37i9dQZF1DX4sWSpwAYIy1',
    description: 'Relaxing piano melodies',
  },
  {
    id: '3',
    name: 'Chill Lofi Study Beats',
    url: 'https://open.spotify.com/playlist/37i9dQZF1DWWQRwui0ExPn',
    description: 'Lo-fi hip hop beats for studying',
  },
  {
    id: '4',
    name: 'Classical Essentials',
    url: 'https://open.spotify.com/playlist/37i9dQZF1DWWEJlAGA9gs0',
    description: 'Essential classical music',
  },
];

export const SpotifyBar: React.FC<SpotifyBarProps> = ({
  isCollapsed = false,
  onToggleCollapse,
}) => {
  const theme = useDynamicTheme();
  
  const [playlists, setPlaylists] = useState<PlaylistItem[]>(DEFAULT_STUDY_PLAYLISTS);
  const [currentPlaylist, setCurrentPlaylist] = useState<string | null>(null);
  const [showPlayDialog, setShowPlayDialog] = useState(false);
  const [showAddPlaylistDialog, setShowAddPlaylistDialog] = useState(false);
  const [newPlaylistName, setNewPlaylistName] = useState('');
  const [newPlaylistUrl, setNewPlaylistUrl] = useState('');
  const [newPlaylistDescription, setNewPlaylistDescription] = useState('');
  const [expanded, setExpanded] = useState(!isCollapsed);

  // Animation values
  const fadeAnim = new Animated.Value(1);
  const scaleAnim = new Animated.Value(1);

  useEffect(() => {
    loadPlaylists();
    loadCurrentPlaylist();
  }, []);

  useEffect(() => {
    setExpanded(!isCollapsed);
  }, [isCollapsed]);

  const loadPlaylists = async () => {
    try {
      const savedPlaylists = await AsyncStorage.getItem('spotify-playlists');
      if (savedPlaylists) {
        setPlaylists(JSON.parse(savedPlaylists));
      }
    } catch (error) {
      console.error('Error loading playlists:', error);
    }
  };

  const loadCurrentPlaylist = async () => {
    try {
      const savedPlaylist = await AsyncStorage.getItem('spotify-current-playlist');
      if (savedPlaylist) {
        setCurrentPlaylist(savedPlaylist);
      }
    } catch (error) {
      console.error('Error loading current playlist:', error);
    }
  };

  const savePlaylists = async (newPlaylists: PlaylistItem[]) => {
    try {
      await AsyncStorage.setItem('spotify-playlists', JSON.stringify(newPlaylists));
      setPlaylists(newPlaylists);
    } catch (error) {
      console.error('Error saving playlists:', error);
    }
  };

  const saveCurrentPlaylist = async (playlistId: string | null) => {
    try {
      if (playlistId) {
        await AsyncStorage.setItem('spotify-current-playlist', playlistId);
      } else {
        await AsyncStorage.removeItem('spotify-current-playlist');
      }
      setCurrentPlaylist(playlistId);
    } catch (error) {
      console.error('Error saving current playlist:', error);
    }
  };

  const handlePlayPlaylist = async (playlist: PlaylistItem) => {
    try {
      const supported = await Linking.canOpenURL(playlist.url);
      if (supported) {
        await Linking.openURL(playlist.url);
        await saveCurrentPlaylist(playlist.id);
        setShowPlayDialog(false);
        
        // Animate selection
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.1,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }),
        ]).start();
      } else {
        Alert.alert(
          'Spotify Not Available',
          'Please install Spotify to play music, or copy the playlist URL to open in your browser.',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Copy URL',
              onPress: () => {
                // In a real app, you'd use Clipboard API
                Alert.alert('URL Copied', 'Playlist URL copied to clipboard');
              },
            },
          ]
        );
      }
    } catch (error) {
      console.error('Error opening Spotify:', error);
      Alert.alert('Error', 'Failed to open Spotify');
    }
  };

  const handleAddPlaylist = async () => {
    if (!newPlaylistName.trim() || !newPlaylistUrl.trim()) {
      Alert.alert('Error', 'Please enter both name and URL');
      return;
    }

    const newPlaylist: PlaylistItem = {
      id: Date.now().toString(),
      name: newPlaylistName.trim(),
      url: newPlaylistUrl.trim(),
      description: newPlaylistDescription.trim() || undefined,
    };

    const updatedPlaylists = [...playlists, newPlaylist];
    await savePlaylists(updatedPlaylists);

    setNewPlaylistName('');
    setNewPlaylistUrl('');
    setNewPlaylistDescription('');
    setShowAddPlaylistDialog(false);

    Alert.alert('Success', 'Playlist added successfully!');
  };

  const handleDeletePlaylist = async (playlistId: string) => {
    Alert.alert(
      'Delete Playlist',
      'Are you sure you want to remove this playlist?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const updatedPlaylists = playlists.filter(p => p.id !== playlistId);
            await savePlaylists(updatedPlaylists);
            
            if (currentPlaylist === playlistId) {
              await saveCurrentPlaylist(null);
            }
          },
        },
      ]
    );
  };

  const toggleExpanded = () => {
    const newExpanded = !expanded;
    setExpanded(newExpanded);
    onToggleCollapse?.(!newExpanded);
  };

  const getCurrentPlaylistName = () => {
    const playlist = playlists.find(p => p.id === currentPlaylist);
    return playlist?.name || 'No playlist selected';
  };

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 16,
      padding: 16,
      marginHorizontal: 20,
      marginVertical: 10,
      elevation: 4,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 4,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: expanded ? 16 : 0,
    },
    headerLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    title: {
      ...ExpressiveTypographyVariants.titleMedium,
      color: theme.colors.onSurfaceVariant,
      fontWeight: '600',
      marginLeft: 8,
    },
    currentPlaylist: {
      ...ExpressiveTypographyVariants.bodySmall,
      color: theme.colors.onSurfaceVariant,
      opacity: 0.8,
      marginLeft: 8,
      flex: 1,
    },
    toggleButton: {
      padding: 8,
    },
    content: {
      gap: 12,
    },
    playButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
      paddingVertical: 14,
      paddingHorizontal: 20,
      alignItems: 'center',
      flexDirection: 'row',
      justifyContent: 'center',
      gap: 8,
    },
    playButtonText: {
      ...ExpressiveTypographyVariants.labelLarge,
      color: theme.colors.onPrimary,
      fontWeight: '600',
    },
    stopButton: {
      backgroundColor: theme.colors.errorContainer,
      borderRadius: 12,
      paddingVertical: 12,
      paddingHorizontal: 16,
      alignItems: 'center',
      flexDirection: 'row',
      justifyContent: 'center',
      gap: 8,
    },
    stopButtonText: {
      ...ExpressiveTypographyVariants.labelMedium,
      color: theme.colors.onErrorContainer,
      fontWeight: '600',
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      backgroundColor: theme.colors.surface,
      borderRadius: 24,
      padding: 24,
      margin: 20,
      maxHeight: '80%',
      width: '90%',
      elevation: 12,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 6 },
      shadowOpacity: 0.4,
      shadowRadius: 12,
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
    },
    modalTitle: {
      ...ExpressiveTypographyVariants.headlineSmall,
      color: theme.colors.onSurface,
      fontWeight: '600',
    },
    closeButton: {
      padding: 8,
    },
    playlistsList: {
      maxHeight: 300,
    },
    playlistItem: {
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      flexDirection: 'row',
      alignItems: 'center',
    },
    playlistInfo: {
      flex: 1,
    },
    playlistName: {
      ...ExpressiveTypographyVariants.titleSmall,
      color: theme.colors.onSurfaceVariant,
      fontWeight: '600',
    },
    playlistDescription: {
      ...ExpressiveTypographyVariants.bodySmall,
      color: theme.colors.onSurfaceVariant,
      opacity: 0.8,
      marginTop: 2,
    },
    playlistActions: {
      flexDirection: 'row',
      gap: 8,
    },
    actionButton: {
      padding: 8,
    },
    addButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
      paddingVertical: 12,
      paddingHorizontal: 20,
      alignItems: 'center',
      marginTop: 16,
    },
    addButtonText: {
      ...ExpressiveTypographyVariants.labelLarge,
      color: theme.colors.onPrimary,
      fontWeight: '600',
    },
    input: {
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 12,
      paddingHorizontal: 16,
      paddingVertical: 12,
      marginBottom: 16,
      color: theme.colors.onSurfaceVariant,
      ...ExpressiveTypographyVariants.bodyLarge,
    },
    textArea: {
      height: 80,
      textAlignVertical: 'top',
    },
    actionButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: 12,
      marginTop: 20,
    },
    button: {
      flex: 1,
      paddingVertical: 14,
      borderRadius: 12,
      alignItems: 'center',
    },
    primaryButton: {
      backgroundColor: theme.colors.primary,
    },
    secondaryButton: {
      backgroundColor: theme.colors.surfaceVariant,
    },
    buttonText: {
      ...ExpressiveTypographyVariants.labelLarge,
      fontWeight: '600',
    },
    primaryButtonText: {
      color: theme.colors.onPrimary,
    },
    secondaryButtonText: {
      color: theme.colors.onSurfaceVariant,
    },
  });

  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        },
      ]}
    >
      <TouchableOpacity style={styles.header} onPress={toggleExpanded}>
        <View style={styles.headerLeft}>
          <MaterialIcons
            name="music-note"
            size={24}
            color={theme.colors.primary}
          />
          <Text style={styles.title}>Music</Text>
          {!expanded && (
            <Text style={styles.currentPlaylist} numberOfLines={1}>
              {getCurrentPlaylistName()}
            </Text>
          )}
        </View>
        <TouchableOpacity style={styles.toggleButton} onPress={toggleExpanded}>
          <MaterialIcons
            name={expanded ? "keyboard-arrow-up" : "keyboard-arrow-down"}
            size={24}
            color={theme.colors.onSurfaceVariant}
          />
        </TouchableOpacity>
      </TouchableOpacity>

      {expanded && (
        <View style={styles.content}>
          {currentPlaylist ? (
            <View style={{ gap: 8 }}>
              <Text style={styles.currentPlaylist}>
                Now playing: {getCurrentPlaylistName()}
              </Text>
              <TouchableOpacity
                style={styles.stopButton}
                onPress={() => saveCurrentPlaylist(null)}
              >
                <MaterialIcons
                  name="stop"
                  size={20}
                  color={theme.colors.onErrorContainer}
                />
                <Text style={styles.stopButtonText}>Stop</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <TouchableOpacity
              style={styles.playButton}
              onPress={() => setShowPlayDialog(true)}
            >
              <MaterialIcons
                name="play-arrow"
                size={24}
                color={theme.colors.onPrimary}
              />
              <Text style={styles.playButtonText}>Select Music for Focus</Text>
            </TouchableOpacity>
          )}
        </View>
      )}

      {/* Playlist Selection Modal */}
      <Modal
        visible={showPlayDialog}
        transparent
        animationType="fade"
        onRequestClose={() => setShowPlayDialog(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Choose Playlist</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowPlayDialog(false)}
              >
                <MaterialIcons
                  name="close"
                  size={24}
                  color={theme.colors.onSurface}
                />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.playlistsList} showsVerticalScrollIndicator={false}>
              {playlists.map(playlist => (
                <TouchableOpacity
                  key={playlist.id}
                  style={styles.playlistItem}
                  onPress={() => handlePlayPlaylist(playlist)}
                >
                  <View style={styles.playlistInfo}>
                    <Text style={styles.playlistName}>{playlist.name}</Text>
                    {playlist.description && (
                      <Text style={styles.playlistDescription}>
                        {playlist.description}
                      </Text>
                    )}
                  </View>
                  <View style={styles.playlistActions}>
                    <TouchableOpacity
                      style={styles.actionButton}
                      onPress={() => handleDeletePlaylist(playlist.id)}
                    >
                      <MaterialIcons
                        name="delete"
                        size={20}
                        color={theme.colors.error}
                      />
                    </TouchableOpacity>
                  </View>
                </TouchableOpacity>
              ))}
            </ScrollView>

            <TouchableOpacity
              style={styles.addButton}
              onPress={() => {
                setShowPlayDialog(false);
                setShowAddPlaylistDialog(true);
              }}
            >
              <Text style={styles.addButtonText}>Add Custom Playlist</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Add Playlist Modal */}
      <Modal
        visible={showAddPlaylistDialog}
        transparent
        animationType="fade"
        onRequestClose={() => setShowAddPlaylistDialog(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Add Playlist</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowAddPlaylistDialog(false)}
              >
                <MaterialIcons
                  name="close"
                  size={24}
                  color={theme.colors.onSurface}
                />
              </TouchableOpacity>
            </View>

            <TextInput
              style={styles.input}
              placeholder="Playlist name"
              placeholderTextColor={theme.colors.onSurfaceVariant}
              value={newPlaylistName}
              onChangeText={setNewPlaylistName}
            />

            <TextInput
              style={styles.input}
              placeholder="Spotify URL"
              placeholderTextColor={theme.colors.onSurfaceVariant}
              value={newPlaylistUrl}
              onChangeText={setNewPlaylistUrl}
            />

            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Description (optional)"
              placeholderTextColor={theme.colors.onSurfaceVariant}
              value={newPlaylistDescription}
              onChangeText={setNewPlaylistDescription}
              multiline
            />

            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={[styles.button, styles.secondaryButton]}
                onPress={() => setShowAddPlaylistDialog(false)}
              >
                <Text style={[styles.buttonText, styles.secondaryButtonText]}>
                  Cancel
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.button, styles.primaryButton]}
                onPress={handleAddPlaylist}
              >
                <Text style={[styles.buttonText, styles.primaryButtonText]}>
                  Add Playlist
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </Animated.View>
  );
};
