import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import { useDynamicTheme } from '../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../constants/expressiveTheme';
import ExpressiveCard from './surfaces/ExpressiveCard';
import ExpressiveButton from './buttons/ExpressiveButton';
import ExpressiveFAB from './buttons/ExpressiveFAB';
import ExpressiveInput from './inputs/ExpressiveInput';
import ExpressiveBottomNavigation, { NavigationItem } from './navigation/ExpressiveBottomNavigation';

/**
 * Component Library Demo
 * Demonstrates the Material 3 Expressive Component Library
 */
export const ComponentLibraryDemo: React.FC = () => {
  const theme = useDynamicTheme();
  const [inputValue, setInputValue] = useState('');
  const [activeNavKey, setActiveNavKey] = useState('home');
  const [fabExtended, setFabExtended] = useState(false);

  // Navigation items
  const navigationItems: NavigationItem[] = [
    {
      key: 'home',
      label: 'Home',
      icon: <Text style={{ fontSize: 20 }}>🏠</Text>,
      activeIcon: <Text style={{ fontSize: 20 }}>🏡</Text>,
    },
    {
      key: 'timer',
      label: 'Timer',
      icon: <Text style={{ fontSize: 20 }}>⏱️</Text>,
      badge: 2,
    },
    {
      key: 'tasks',
      label: 'Tasks',
      icon: <Text style={{ fontSize: 20 }}>📋</Text>,
      badge: '5',
    },
    {
      key: 'analytics',
      label: 'Analytics',
      icon: <Text style={{ fontSize: 20 }}>📊</Text>,
    },
    {
      key: 'profile',
      label: 'Profile',
      icon: <Text style={{ fontSize: 20 }}>👤</Text>,
    },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      padding: 16,
      paddingBottom: 100, // Space for bottom navigation
    },
    section: {
      marginBottom: 32,
    },
    sectionTitle: {
      ...ExpressiveTypography.headlineMedium,
      color: theme.colors.primary,
      marginBottom: 16,
    },
    subsectionTitle: {
      ...ExpressiveTypography.titleMedium,
      color: theme.colors.onSurface,
      marginBottom: 12,
    },
    description: {
      ...ExpressiveTypography.bodyMedium,
      color: theme.colors.onSurfaceVariant,
      marginBottom: 16,
      lineHeight: 20,
    },
    buttonGrid: {
      gap: 12,
      marginBottom: 16,
    },
    buttonRow: {
      flexDirection: 'row',
      gap: 12,
      flexWrap: 'wrap',
    },
    inputContainer: {
      gap: 16,
      marginBottom: 16,
    },
    fabContainer: {
      flexDirection: 'row',
      gap: 16,
      flexWrap: 'wrap',
      alignItems: 'center',
      marginBottom: 16,
    },
    cardGrid: {
      gap: 16,
    },
  });

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Component Library</Text>
          <Text style={styles.description}>
            Material 3 Expressive components with beautiful animations and interactions.
          </Text>
        </View>

        {/* Buttons */}
        <View style={styles.section}>
          <Text style={styles.subsectionTitle}>Buttons</Text>
          
          <View style={styles.buttonGrid}>
            <View style={styles.buttonRow}>
              <ExpressiveButton
                title="Filled"
                variant="filled"
                onPress={() => Alert.alert('Filled Button', 'Pressed!')}
              />
              <ExpressiveButton
                title="Outlined"
                variant="outlined"
                onPress={() => Alert.alert('Outlined Button', 'Pressed!')}
              />
              <ExpressiveButton
                title="Text"
                variant="text"
                onPress={() => Alert.alert('Text Button', 'Pressed!')}
              />
            </View>
            
            <View style={styles.buttonRow}>
              <ExpressiveButton
                title="Elevated"
                variant="elevated"
                onPress={() => Alert.alert('Elevated Button', 'Pressed!')}
              />
              <ExpressiveButton
                title="Tonal"
                variant="tonal"
                onPress={() => Alert.alert('Tonal Button', 'Pressed!')}
              />
              <ExpressiveButton
                title="Disabled"
                variant="filled"
                disabled
              />
            </View>
            
            <View style={styles.buttonRow}>
              <ExpressiveButton
                title="Small"
                size="small"
                variant="filled"
                onPress={() => Alert.alert('Small Button', 'Pressed!')}
              />
              <ExpressiveButton
                title="Medium"
                size="medium"
                variant="filled"
                onPress={() => Alert.alert('Medium Button', 'Pressed!')}
              />
              <ExpressiveButton
                title="Large"
                size="large"
                variant="filled"
                onPress={() => Alert.alert('Large Button', 'Pressed!')}
              />
            </View>
            
            <View style={styles.buttonRow}>
              <ExpressiveButton
                title="With Icon"
                variant="filled"
                icon={<Text style={{ color: theme.colors.onPrimary }}>🚀</Text>}
                onPress={() => Alert.alert('Icon Button', 'Pressed!')}
              />
              <ExpressiveButton
                title="Loading"
                variant="filled"
                loading
              />
            </View>
          </View>
        </View>

        {/* Floating Action Buttons */}
        <View style={styles.section}>
          <Text style={styles.subsectionTitle}>Floating Action Buttons</Text>
          
          <View style={styles.fabContainer}>
            <ExpressiveFAB
              icon={<Text style={{ fontSize: 20 }}>➕</Text>}
              size="small"
              position="center"
              onPress={() => Alert.alert('Small FAB', 'Pressed!')}
            />
            <ExpressiveFAB
              icon={<Text style={{ fontSize: 24 }}>✏️</Text>}
              size="medium"
              position="center"
              onPress={() => Alert.alert('Medium FAB', 'Pressed!')}
            />
            <ExpressiveFAB
              icon={<Text style={{ fontSize: 28 }}>🎯</Text>}
              size="large"
              position="center"
              onPress={() => Alert.alert('Large FAB', 'Pressed!')}
            />
          </View>
          
          <ExpressiveButton
            title={fabExtended ? 'Collapse FAB' : 'Extend FAB'}
            variant="outlined"
            onPress={() => setFabExtended(!fabExtended)}
            style={{ marginBottom: 16 }}
          />
          
          <ExpressiveFAB
            icon={<Text style={{ fontSize: 24 }}>📝</Text>}
            label="Create Task"
            extended={fabExtended}
            position="center"
            onPress={() => Alert.alert('Extended FAB', 'Pressed!')}
          />
        </View>

        {/* Inputs */}
        <View style={styles.section}>
          <Text style={styles.subsectionTitle}>Input Fields</Text>
          
          <View style={styles.inputContainer}>
            <ExpressiveInput
              label="Email"
              placeholder="Enter your email"
              value={inputValue}
              onChangeText={setInputValue}
              leadingIcon={<Text>📧</Text>}
              helperText="We'll never share your email"
            />
            
            <ExpressiveInput
              label="Password"
              placeholder="Enter password"
              secureTextEntry
              required
              trailingIcon={<Text>👁️</Text>}
              variant="filled"
            />
            
            <ExpressiveInput
              label="Error Example"
              placeholder="This field has an error"
              errorText="This field is required"
              variant="outlined"
            />
            
            <ExpressiveInput
              label="Disabled Field"
              placeholder="This field is disabled"
              disabled
              value="Disabled value"
            />
          </View>
        </View>

        {/* Cards */}
        <View style={styles.section}>
          <Text style={styles.subsectionTitle}>Cards</Text>
          
          <View style={styles.cardGrid}>
            <ExpressiveCard
              title="Elevated Card"
              subtitle="With elevation and shadow"
              description="This card demonstrates the elevated variant with proper Material 3 elevation."
              variant="elevated"
              elevation={2}
              colorVariant="primary"
            />
            
            <ExpressiveCard
              title="Filled Card"
              subtitle="Solid background"
              description="This card uses the filled variant with a solid background color."
              variant="filled"
            />
            
            <ExpressiveCard
              title="Outlined Card"
              subtitle="With border outline"
              description="This card uses the outlined variant with a border and no elevation."
              variant="outlined"
            />
            
            <ExpressiveCard
              title="Interactive Card"
              subtitle="Tap to interact"
              description="This card has press animations and responds to touch."
              elevation={1}
              colorVariant="secondary"
              onPress={() => Alert.alert('Card Pressed', 'Interactive card was tapped!')}
            />
          </View>
        </View>
      </ScrollView>

      {/* Bottom Navigation */}
      <ExpressiveBottomNavigation
        items={navigationItems}
        activeKey={activeNavKey}
        onItemPress={setActiveNavKey}
        showLabels={true}
      />
    </View>
  );
};

export default ComponentLibraryDemo;
