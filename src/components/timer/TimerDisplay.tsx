import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import Svg, { Circle } from 'react-native-svg';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypographyVariants } from '../../constants/expressiveTheme';
type TimerMode = 'pomodoro' | 'stopwatch';
type PomodoroPhase = 'work' | 'shortBreak' | 'longBreak';
type TimerStatus = 'idle' | 'running' | 'paused';

// ===========================================
// 🕐 TIMER DISPLAY COMPONENT
// ===========================================

interface TimerDisplayProps {
  time: number; // seconds
  mode: TimerMode;
  phase: PomodoroPhase;
  status: TimerStatus;
  totalDuration?: number; // for progress calculation
  completedSessions?: number;
}

const { width: screenWidth } = Dimensions.get('window');
const CIRCLE_SIZE = Math.min(screenWidth * 0.7, 280);
const CIRCLE_RADIUS = (CIRCLE_SIZE - 20) / 2;
const CIRCLE_CIRCUMFERENCE = 2 * Math.PI * CIRCLE_RADIUS;

export const TimerDisplay: React.FC<TimerDisplayProps> = ({
  time,
  mode,
  phase,
  status,
  totalDuration = 0,
  completedSessions = 0,
}) => {
  const theme = useDynamicTheme();
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;

  // Format time display
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes
        .toString()
        .padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs
      .toString()
      .padStart(2, '0')}`;
  };

  // Get phase color
  const getPhaseColor = () => {
    switch (phase) {
      case 'work':
        return theme.colors.primary;
      case 'shortBreak':
        return theme.colors.secondary;
      case 'longBreak':
        return theme.colors.tertiary;
      default:
        return theme.colors.primary;
    }
  };

  // Get phase emoji
  const getPhaseEmoji = () => {
    switch (phase) {
      case 'work':
        return '🍅';
      case 'shortBreak':
        return '☕';
      case 'longBreak':
        return '🌟';
      default:
        return '🍅';
    }
  };

  // Get phase label
  const getPhaseLabel = () => {
    switch (phase) {
      case 'work':
        return 'Focus Time';
      case 'shortBreak':
        return 'Short Break';
      case 'longBreak':
        return 'Long Break';
      default:
        return 'Focus Time';
    }
  };

  // Calculate progress for circular progress bar
  const progress = totalDuration > 0 ? (totalDuration - time) / totalDuration : 0;
  const strokeDashoffset = CIRCLE_CIRCUMFERENCE * (1 - progress);

  // Pulse animation for running timer
  useEffect(() => {
    if (status === 'running') {
      const pulse = () => {
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.05,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]).start(() => {
          if (status === 'running') pulse();
        });
      };
      pulse();
    } else {
      pulseAnim.setValue(1);
    }
  }, [status, pulseAnim]);

  // Progress animation
  useEffect(() => {
    Animated.timing(progressAnim, {
      toValue: progress,
      duration: 500,
      useNativeDriver: false,
    }).start();
  }, [progress, progressAnim]);

  const styles = StyleSheet.create({
    container: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 32,
    },
    circleContainer: {
      width: CIRCLE_SIZE,
      height: CIRCLE_SIZE,
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
    },
    progressCircle: {
      position: 'absolute',
      top: 0,
      left: 0,
    },
    innerContent: {
      alignItems: 'center',
      justifyContent: 'center',
      position: 'absolute',
    },
    phaseEmoji: {
      fontSize: 32,
      marginBottom: 8,
    },
    timeText: {
      ...ExpressiveTypographyVariants.timerDisplay,
      color: theme.colors.onSurface,
      fontSize: Math.min(screenWidth * 0.12, 48),
      fontWeight: '300',
      letterSpacing: -1,
    },
    phaseLabel: {
      ...ExpressiveTypographyVariants.labelLarge,
      color: theme.colors.onSurfaceVariant,
      marginTop: 8,
      textAlign: 'center',
    },
    sessionInfo: {
      marginTop: 24,
      alignItems: 'center',
    },
    sessionCount: {
      ...ExpressiveTypographyVariants.titleMedium,
      color: theme.colors.primary,
      fontWeight: '600',
    },
    sessionLabel: {
      ...ExpressiveTypographyVariants.labelSmall,
      color: theme.colors.onSurfaceVariant,
      marginTop: 4,
      textTransform: 'uppercase',
    },
    modeIndicator: {
      position: 'absolute',
      top: -40,
      backgroundColor: theme.colors.primaryContainer,
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
    },
    modeText: {
      ...ExpressiveTypographyVariants.labelSmall,
      color: theme.colors.onPrimaryContainer,
      fontWeight: '600',
      textTransform: 'uppercase',
    },
    statusIndicator: {
      position: 'absolute',
      bottom: -20,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 6,
    },
    statusDot: {
      width: 6,
      height: 6,
      borderRadius: 3,
      backgroundColor: getPhaseColor(),
    },
    statusText: {
      ...ExpressiveTypographyVariants.labelSmall,
      color: theme.colors.onSurfaceVariant,
      fontSize: 10,
    },
  });

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.circleContainer,
          { transform: [{ scale: pulseAnim }] },
        ]}
      >
        {/* Mode Indicator */}
        <View style={styles.modeIndicator}>
          <Text style={styles.modeText}>
            {mode === 'pomodoro' ? 'Pomodoro' : 'Stopwatch'}
          </Text>
        </View>

        {/* Progress Circle */}
        {mode === 'pomodoro' && (
          <Svg
            width={CIRCLE_SIZE}
            height={CIRCLE_SIZE}
            style={styles.progressCircle}
          >
            {/* Background Circle */}
            <Circle
              cx={CIRCLE_SIZE / 2}
              cy={CIRCLE_SIZE / 2}
              r={CIRCLE_RADIUS}
              stroke={theme.colors.outline}
              strokeWidth={4}
              fill="transparent"
              opacity={0.3}
            />
            {/* Progress Circle */}
            <Circle
              cx={CIRCLE_SIZE / 2}
              cy={CIRCLE_SIZE / 2}
              r={CIRCLE_RADIUS}
              stroke={getPhaseColor()}
              strokeWidth={6}
              fill="transparent"
              strokeDasharray={CIRCLE_CIRCUMFERENCE}
              strokeDashoffset={strokeDashoffset}
              strokeLinecap="round"
              transform={`rotate(-90 ${CIRCLE_SIZE / 2} ${CIRCLE_SIZE / 2})`}
            />
          </Svg>
        )}

        {/* Inner Content */}
        <View style={styles.innerContent}>
          {mode === 'pomodoro' && (
            <Text style={styles.phaseEmoji}>{getPhaseEmoji()}</Text>
          )}
          
          <Text style={styles.timeText}>{formatTime(time)}</Text>
          
          {mode === 'pomodoro' && (
            <Text style={styles.phaseLabel}>{getPhaseLabel()}</Text>
          )}
        </View>

        {/* Status Indicator */}
        <View style={styles.statusIndicator}>
          <View style={styles.statusDot} />
          <Text style={styles.statusText}>
            {status === 'running' ? 'Active' : status === 'paused' ? 'Paused' : 'Ready'}
          </Text>
        </View>
      </Animated.View>

      {/* Session Information */}
      {mode === 'pomodoro' && (
        <View style={styles.sessionInfo}>
          <Text style={styles.sessionCount}>
            {completedSessions}
          </Text>
          <Text style={styles.sessionLabel}>
            Sessions Completed
          </Text>
        </View>
      )}
    </View>
  );
};

export default TimerDisplay;
