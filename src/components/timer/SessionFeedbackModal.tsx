import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Modal,
  Pressable,
  Dimensions,
  Vibration,
} from 'react-native';
import {
  Text,
  Button,
  TextInput,
  Surface,
  IconButton,
  Chip,
} from 'react-native-paper';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  runOnJS,
  Easing,
} from 'react-native-reanimated';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { formatDuration } from '../../utils/timerUtils';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface SessionData {
  subject: string;
  taskName?: string;
  duration: number;
  mode: 'pomodoro' | 'stopwatch';
  phase?: string;
}

interface SessionFeedbackModalProps {
  visible: boolean;
  sessionData: SessionData | null;
  onSubmit: (feedback: SessionFeedback) => void;
  onSkip: () => void;
  onClose: () => void;
}

export interface SessionFeedback {
  productivityRating: number;
  notes: string;
  tags: string[];
  difficulty: 'easy' | 'medium' | 'hard';
  focusLevel: number;
  distractions: string[];
}

const SessionFeedbackModal: React.FC<SessionFeedbackModalProps> = ({
  visible,
  sessionData,
  onSubmit,
  onSkip,
  onClose,
}) => {
  const theme = useDynamicTheme();
  
  // Form state
  const [productivityRating, setProductivityRating] = useState(3);
  const [focusLevel, setFocusLevel] = useState(3);
  const [difficulty, setDifficulty] = useState<'easy' | 'medium' | 'hard'>('medium');
  const [notes, setNotes] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedDistractions, setSelectedDistractions] = useState<string[]>([]);

  // Animation values
  const modalScale = useSharedValue(0);
  const modalOpacity = useSharedValue(0);
  const contentTranslateY = useSharedValue(50);

  // Predefined tags and distractions
  const availableTags = [
    'Focused', 'Productive', 'Challenging', 'Easy', 'Interesting',
    'Boring', 'Rushed', 'Relaxed', 'Motivated', 'Tired'
  ];

  const availableDistractions = [
    'Phone', 'Social Media', 'Noise', 'Hunger', 'Fatigue',
    'Other People', 'Thoughts', 'Internet', 'Music', 'None'
  ];

  // Reset form when modal opens
  useEffect(() => {
    if (visible) {
      setProductivityRating(3);
      setFocusLevel(3);
      setDifficulty('medium');
      setNotes('');
      setSelectedTags([]);
      setSelectedDistractions([]);
      
      // Animate in
      modalScale.value = withSpring(1, { damping: 15 });
      modalOpacity.value = withTiming(1, { duration: 300 });
      contentTranslateY.value = withSpring(0, { damping: 15 });
    } else {
      // Animate out
      modalScale.value = withTiming(0, { duration: 200 });
      modalOpacity.value = withTiming(0, { duration: 200 });
      contentTranslateY.value = withTiming(50, { duration: 200 });
    }
  }, [visible]);

  // Haptic feedback
  const triggerHaptic = () => {
    Vibration.vibrate(50);
  };

  // Handle rating selection
  const handleRatingSelect = (rating: number, setter: (value: number) => void) => {
    setter(rating);
    runOnJS(triggerHaptic)();
  };

  // Handle tag toggle
  const toggleTag = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
    runOnJS(triggerHaptic)();
  };

  // Handle distraction toggle
  const toggleDistraction = (distraction: string) => {
    setSelectedDistractions(prev => 
      prev.includes(distraction) 
        ? prev.filter(d => d !== distraction)
        : [...prev, distraction]
    );
    runOnJS(triggerHaptic)();
  };

  // Handle submit
  const handleSubmit = () => {
    const feedback: SessionFeedback = {
      productivityRating,
      notes,
      tags: selectedTags,
      difficulty,
      focusLevel,
      distractions: selectedDistractions,
    };
    
    onSubmit(feedback);
  };

  // Animated styles
  const modalAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: modalScale.value }],
    opacity: modalOpacity.value,
  }));

  const contentAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: contentTranslateY.value }],
  }));

  // Rating component
  const RatingSelector = ({ 
    value, 
    onChange, 
    label, 
    icon 
  }: { 
    value: number; 
    onChange: (rating: number) => void; 
    label: string; 
    icon: string;
  }) => (
    <View style={styles.ratingContainer}>
      <View style={styles.ratingHeader}>
        <IconButton icon={icon} size={20} iconColor={theme.colors.primary} />
        <Text style={styles.ratingLabel}>{label}</Text>
      </View>
      <View style={styles.ratingButtons}>
        {[1, 2, 3, 4, 5].map((rating) => (
          <Pressable
            key={rating}
            style={[
              styles.ratingButton,
              {
                backgroundColor: rating <= value 
                  ? theme.colors.primary 
                  : theme.colors.surfaceVariant,
              },
            ]}
            onPress={() => handleRatingSelect(rating, onChange)}
          >
            <Text
              style={[
                styles.ratingButtonText,
                {
                  color: rating <= value 
                    ? theme.colors.onPrimary 
                    : theme.colors.onSurfaceVariant,
                },
              ]}
            >
              {rating}
            </Text>
          </Pressable>
        ))}
      </View>
    </View>
  );

  if (!sessionData) return null;

  const styles = StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    modal: {
      width: Math.min(screenWidth - 40, 400),
      maxHeight: screenHeight * 0.9,
      borderRadius: 24,
      backgroundColor: theme.colors.surface,
      elevation: 24,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 12 },
      shadowOpacity: 0.3,
      shadowRadius: 24,
    },
    header: {
      padding: 24,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
      alignItems: 'center',
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: theme.colors.onSurface,
      marginBottom: 8,
    },
    sessionInfo: {
      alignItems: 'center',
    },
    sessionSubject: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.primary,
    },
    sessionDuration: {
      fontSize: 14,
      color: theme.colors.onSurfaceVariant,
      marginTop: 4,
    },
    content: {
      padding: 24,
      maxHeight: screenHeight * 0.6,
    },
    scrollContent: {
      gap: 24,
    },
    ratingContainer: {
      gap: 12,
    },
    ratingHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    ratingLabel: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.onSurface,
    },
    ratingButtons: {
      flexDirection: 'row',
      gap: 8,
      justifyContent: 'center',
    },
    ratingButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      elevation: 2,
    },
    ratingButtonText: {
      fontSize: 16,
      fontWeight: '600',
    },
    difficultyContainer: {
      gap: 12,
    },
    difficultyLabel: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.onSurface,
    },
    difficultyButtons: {
      flexDirection: 'row',
      gap: 8,
    },
    difficultyButton: {
      flex: 1,
    },
    tagsContainer: {
      gap: 12,
    },
    tagsLabel: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.onSurface,
    },
    tagsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
    },
    notesContainer: {
      gap: 12,
    },
    notesLabel: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.onSurface,
    },
    notesInput: {
      backgroundColor: theme.colors.surfaceVariant,
    },
    actions: {
      flexDirection: 'row',
      padding: 24,
      gap: 12,
      borderTopWidth: 1,
      borderTopColor: theme.colors.outline,
    },
    actionButton: {
      flex: 1,
    },
  });

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <Animated.View style={[styles.modal, modalAnimatedStyle]}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.headerTitle}>How was your session?</Text>
            <View style={styles.sessionInfo}>
              <Text style={styles.sessionSubject}>
                {sessionData.subject}
                {sessionData.taskName && ` - ${sessionData.taskName}`}
              </Text>
              <Text style={styles.sessionDuration}>
                {formatDuration(sessionData.duration)} • {sessionData.mode}
              </Text>
            </View>
          </View>

          {/* Content */}
          <Animated.ScrollView 
            style={styles.content}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            style={contentAnimatedStyle}
          >
            {/* Productivity Rating */}
            <RatingSelector
              value={productivityRating}
              onChange={setProductivityRating}
              label="Productivity"
              icon="chart-line"
            />

            {/* Focus Level */}
            <RatingSelector
              value={focusLevel}
              onChange={setFocusLevel}
              label="Focus Level"
              icon="eye"
            />

            {/* Difficulty */}
            <View style={styles.difficultyContainer}>
              <Text style={styles.difficultyLabel}>Difficulty</Text>
              <View style={styles.difficultyButtons}>
                {(['easy', 'medium', 'hard'] as const).map((level) => (
                  <Button
                    key={level}
                    mode={difficulty === level ? 'contained' : 'outlined'}
                    onPress={() => setDifficulty(level)}
                    style={styles.difficultyButton}
                  >
                    {level.charAt(0).toUpperCase() + level.slice(1)}
                  </Button>
                ))}
              </View>
            </View>

            {/* Tags */}
            <View style={styles.tagsContainer}>
              <Text style={styles.tagsLabel}>How did it feel?</Text>
              <View style={styles.tagsGrid}>
                {availableTags.map((tag) => (
                  <Chip
                    key={tag}
                    selected={selectedTags.includes(tag)}
                    onPress={() => toggleTag(tag)}
                    mode="outlined"
                  >
                    {tag}
                  </Chip>
                ))}
              </View>
            </View>

            {/* Distractions */}
            <View style={styles.tagsContainer}>
              <Text style={styles.tagsLabel}>Any distractions?</Text>
              <View style={styles.tagsGrid}>
                {availableDistractions.map((distraction) => (
                  <Chip
                    key={distraction}
                    selected={selectedDistractions.includes(distraction)}
                    onPress={() => toggleDistraction(distraction)}
                    mode="outlined"
                  >
                    {distraction}
                  </Chip>
                ))}
              </View>
            </View>

            {/* Notes */}
            <View style={styles.notesContainer}>
              <Text style={styles.notesLabel}>Additional notes</Text>
              <TextInput
                value={notes}
                onChangeText={setNotes}
                placeholder="What did you learn? Any insights?"
                multiline
                numberOfLines={3}
                style={styles.notesInput}
                mode="outlined"
              />
            </View>
          </Animated.ScrollView>

          {/* Actions */}
          <View style={styles.actions}>
            <Button
              mode="outlined"
              onPress={onSkip}
              style={styles.actionButton}
            >
              Skip
            </Button>
            <Button
              mode="contained"
              onPress={handleSubmit}
              style={styles.actionButton}
            >
              Save Feedback
            </Button>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

export default SessionFeedbackModal;
