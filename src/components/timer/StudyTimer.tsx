import React, { useEffect, useState, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  AppState,
  AppStateStatus,
  Alert,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypographyVariants } from '../../constants/expressiveTheme';
import { TimerDisplay } from './TimerDisplay';
import { TimerControls } from './TimerControls';
import { useTimerStore } from '../../stores/timerStore';
import { useAuthStore } from '../../stores/authStore';
import { useTimerNotifications } from '../../hooks/useTimerNotifications';
import { useBackgroundTimer } from '../../hooks/useBackgroundTimer';
import AsyncStorage from '@react-native-async-storage/async-storage';

type TimerStatus = 'idle' | 'running' | 'paused';
type TimerMode = 'pomodoro' | 'stopwatch';
type PhaseType = 'work' | 'shortBreak' | 'longBreak';

interface StudyTimerProps {
  mode: TimerMode;
  hideNotificationButton?: boolean;
  onStatusChange?: (status: TimerStatus) => void;
  groupSync?: {
    groupId: string;
    state: {
      isRunning: boolean;
      startTime: number | null;
      timeRemaining: number;
      currentPhase: PhaseType;
      completedSessions: number;
      mode: TimerMode;
    };
    setState: (state: any) => Promise<void>;
  };
}

interface SavedTimerState {
  status: TimerStatus;
  startTime: number | null;
  accumulatedPausedTime: number;
  pauseStartTime: number | null;
  currentPhase: PhaseType;
  completedSessions: number;
  mode: TimerMode;
  selectedSubject: string;
  saveTimestamp: number;
  taskName: string;
  taskType: string;
}

const SESSION_STORAGE_KEY = 'studyTimerState';

export const StudyTimer: React.FC<StudyTimerProps> = ({
  mode,
  hideNotificationButton = false,
  onStatusChange,
  groupSync,
}) => {
  const theme = useDynamicTheme();
  const { user } = useAuthStore();
  const {
    status,
    currentPhase,
    displayTime,
    elapsedTime,
    pomodoroCount,
    workDuration,
    shortBreakDuration,
    longBreakDuration,
    longBreakInterval,
    selectedSubject,
    currentTaskName,
    startTimer,
    pauseTimer,
    stopTimer,
    resetTimer,
    completeSession,
    setMode: setTimerMode,
  } = useTimerStore();

  // Local state
  const [timerStatus, setTimerStatus] = useState<TimerStatus>('idle');
  const [startTime, setStartTime] = useState<number | null>(null);
  const [accumulatedPausedTime, setAccumulatedPausedTime] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [showSettings, setShowSettings] = useState(false);
  const [showSessionComplete, setShowSessionComplete] = useState(false);
  const [sessionFeedback, setSessionFeedback] = useState('');
  const [productivityRating, setProductivityRating] = useState(0);

  // Refs
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const pauseStartTimeRef = useRef<number | null>(null);
  const sessionStartTimeRef = useRef<number>(0);
  const appStateRef = useRef<AppStateStatus>(AppState.currentState);

  // Hooks
  const { scheduleNotification, cancelNotifications } = useTimerNotifications();
  const { startBackgroundTimer, stopBackgroundTimer } = useBackgroundTimer();

  // Animation values
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const fadeAnim = useRef(new Animated.Value(1)).current;

  // Set timer mode on mount
  useEffect(() => {
    setTimerMode(mode);
  }, [mode, setTimerMode]);

  // Load saved state on mount
  useEffect(() => {
    loadSavedState();
  }, []);

  // Handle app state changes
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (appStateRef.current.match(/inactive|background/) && nextAppState === 'active') {
        // App has come to the foreground
        if (timerStatus === 'running') {
          syncTimeFromBackground();
        }
      } else if (nextAppState.match(/inactive|background/)) {
        // App is going to background
        saveStateToStorage();
        if (timerStatus === 'running') {
          startBackgroundTimer();
        }
      }
      appStateRef.current = nextAppState;
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [timerStatus]);

  // Timer interval effect
  useEffect(() => {
    if (timerStatus === 'running') {
      startPulseAnimation();
      intervalRef.current = setInterval(() => {
        updateTimer();
      }, 1000);
    } else {
      stopPulseAnimation();
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [timerStatus, startTime, accumulatedPausedTime]);

  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.05,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const stopPulseAnimation = () => {
    pulseAnim.stopAnimation();
    Animated.timing(pulseAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const loadSavedState = async () => {
    try {
      const savedState = await AsyncStorage.getItem(SESSION_STORAGE_KEY);
      if (savedState) {
        const parsedState: SavedTimerState = JSON.parse(savedState);
        const timeSinceSave = Date.now() - parsedState.saveTimestamp;
        
        // Only restore if saved within last 24 hours
        if (timeSinceSave < 24 * 60 * 60 * 1000) {
          setTimerStatus(parsedState.status);
          setStartTime(parsedState.startTime);
          setAccumulatedPausedTime(parsedState.accumulatedPausedTime);
          
          if (parsedState.status === 'running' && parsedState.startTime) {
            // Sync time if timer was running
            syncTimeFromBackground();
          }
        }
      }
    } catch (error) {
      console.error('Error loading saved timer state:', error);
    }
  };

  const saveStateToStorage = useCallback(async () => {
    try {
      const stateToSave: SavedTimerState = {
        status: timerStatus,
        startTime: startTime,
        accumulatedPausedTime: accumulatedPausedTime,
        pauseStartTime: pauseStartTimeRef.current,
        currentPhase: currentPhase,
        completedSessions: pomodoroCount,
        mode: mode,
        selectedSubject: selectedSubject || '',
        saveTimestamp: Date.now(),
        taskName: currentTaskName || '',
        taskType: 'study',
      };
      
      await AsyncStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(stateToSave));
    } catch (error) {
      console.error('Error saving timer state:', error);
    }
  }, [timerStatus, startTime, accumulatedPausedTime, currentPhase, pomodoroCount, mode, selectedSubject, currentTaskName]);

  const syncTimeFromBackground = () => {
    if (startTime && timerStatus === 'running') {
      const now = Date.now();
      const totalElapsed = Math.floor((now - startTime) / 1000) - accumulatedPausedTime;
      setCurrentTime(totalElapsed);
    }
  };

  const updateTimer = () => {
    if (!startTime) return;

    const now = Date.now();
    const totalElapsed = Math.floor((now - startTime) / 1000) - accumulatedPausedTime;
    setCurrentTime(totalElapsed);

    // Check for phase completion in pomodoro mode
    if (mode === 'pomodoro') {
      const phaseDuration = getPhaseDuration();
      if (totalElapsed >= phaseDuration) {
        handlePhaseComplete();
      }
    }
  };

  const getPhaseDuration = (): number => {
    switch (currentPhase) {
      case 'work':
        return workDuration * 60;
      case 'shortBreak':
        return shortBreakDuration * 60;
      case 'longBreak':
        return longBreakDuration * 60;
      default:
        return workDuration * 60;
    }
  };

  const handlePhaseComplete = () => {
    // Handle phase completion logic
    setTimerStatus('idle');
    setShowSessionComplete(true);
    
    // Schedule notification
    scheduleNotification(
      'Phase Complete!',
      `Your ${currentPhase} session is complete.`,
      1
    );
  };

  const handleStart = () => {
    if (timerStatus === 'idle') {
      const now = Date.now();
      setStartTime(now);
      sessionStartTimeRef.current = now;
      setAccumulatedPausedTime(0);
      setCurrentTime(0);
    } else if (timerStatus === 'paused' && pauseStartTimeRef.current) {
      const pauseDuration = Math.floor((Date.now() - pauseStartTimeRef.current) / 1000);
      setAccumulatedPausedTime(prev => prev + pauseDuration);
      pauseStartTimeRef.current = null;
    }
    
    setTimerStatus('running');
    onStatusChange?.('running');
    startTimer();
  };

  const handlePause = () => {
    setTimerStatus('paused');
    pauseStartTimeRef.current = Date.now();
    onStatusChange?.('paused');
    pauseTimer();
    saveStateToStorage();
  };

  const handleReset = () => {
    setTimerStatus('idle');
    setStartTime(null);
    setAccumulatedPausedTime(0);
    setCurrentTime(0);
    pauseStartTimeRef.current = null;
    onStatusChange?.('idle');
    resetTimer();
    cancelNotifications();
    stopBackgroundTimer();
    AsyncStorage.removeItem(SESSION_STORAGE_KEY);
  };

  const handleComplete = () => {
    // Complete session logic
    setShowSessionComplete(true);
    setTimerStatus('idle');
    completeSession(sessionFeedback, productivityRating);
  };

  const getDisplayTime = (): number => {
    if (mode === 'pomodoro') {
      const phaseDuration = getPhaseDuration();
      return Math.max(0, phaseDuration - currentTime);
    } else {
      return currentTime;
    }
  };

  const styles = StyleSheet.create({
    container: {
      alignItems: 'center',
      paddingVertical: 20,
    },
    timerWrapper: {
      alignItems: 'center',
      marginBottom: 40,
    },
    phaseIndicator: {
      ...ExpressiveTypographyVariants.labelLarge,
      color: theme.colors.primary,
      marginBottom: 16,
      textTransform: 'uppercase',
      letterSpacing: 1,
    },
    sessionCounter: {
      ...ExpressiveTypographyVariants.bodyMedium,
      color: theme.colors.onSurfaceVariant,
      marginTop: 16,
    },
  });

  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      <View style={styles.timerWrapper}>
        {mode === 'pomodoro' && (
          <Text style={styles.phaseIndicator}>
            {currentPhase === 'work' ? 'Focus Time' : 
             currentPhase === 'shortBreak' ? 'Short Break' : 'Long Break'}
          </Text>
        )}
        
        <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
          <TimerDisplay
            time={getDisplayTime()}
            mode={mode}
            phase={currentPhase}
            status={timerStatus}
            totalDuration={getPhaseDuration()}
            completedSessions={pomodoroCount}
          />
        </Animated.View>
        
        {mode === 'pomodoro' && (
          <Text style={styles.sessionCounter}>
            Session {pomodoroCount + 1}
          </Text>
        )}
      </View>

      <TimerControls
        status={timerStatus}
        mode={mode}
        onStart={handleStart}
        onPause={handlePause}
        onReset={handleReset}
        onComplete={handleComplete}
      />
    </Animated.View>
  );
};
