import React, { useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  Vibration,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { useTimerCore, TimerMode, TimerStatus, PomodoroPhase, TimerSettings, defaultTimerSettings } from './TimerCore';
import TimerDisplay from './TimerDisplay';
import TimerControls from './TimerControls';

// ===========================================
// 🍅 MAIN TIMER COMPONENT
// ===========================================

interface TimerProps {
  initialMode?: TimerMode;
  initialSettings?: Partial<TimerSettings>;
  onSessionComplete?: (completedSessions: number) => void;
  onPhaseChange?: (phase: PomodoroPhase) => void;
}

export const Timer: React.FC<TimerProps> = ({
  initialMode = 'pomodoro',
  initialSettings = {},
  onSessionComplete,
  onPhaseChange,
}) => {
  const theme = useDynamicTheme();
  const [currentMode, setCurrentMode] = useState<TimerMode>(initialMode);

  // Timer core logic
  const { state, setState, audioManager, notificationManager, startTimeRef, pausedTimeRef, getPhaseDuration } = useTimerCore(
    { ...defaultTimerSettings, ...initialSettings },
    {
      onTick: (time) => {
        // Optional: Add tick sound for last 10 seconds
        if (currentMode === 'pomodoro' && time <= 10 && time > 0 && state.settings.soundEnabled) {
          audioManager.playSound('tick', 0.3);
        }
      },
      onPhaseComplete: (currentPhase, nextPhase) => {
        // Vibration feedback
        if (state.settings.vibrationEnabled) {
          Vibration.vibrate([0, 500, 200, 500]);
        }
        
        onPhaseChange?.(nextPhase);
      },
      onSessionComplete: (completedSessions) => {
        onSessionComplete?.(completedSessions);
      },
      onTimerStart: () => {
        console.log('Timer started');
      },
      onTimerPause: () => {
        console.log('Timer paused');
      },
      onTimerReset: () => {
        console.log('Timer reset');
      },
    }
  );

  // Timer control handlers
  const handleStart = useCallback(() => {
    if (state.status === 'idle') {
      // Starting fresh
      startTimeRef.current = Date.now();
      pausedTimeRef.current = 0;
      
      const initialTime = currentMode === 'pomodoro' 
        ? getPhaseDuration(state.currentPhase, state.settings)
        : 0;
      
      setState(prev => ({
        ...prev,
        mode: currentMode,
        status: 'running',
        displayTime: initialTime,
        currentPhase: 'work',
        completedSessions: 0,
      }));
    } else if (state.status === 'paused') {
      // Resuming from pause
      const now = Date.now();
      const pauseDuration = now - (pausedTimeRef.current || now);
      startTimeRef.current = (startTimeRef.current || now) + pauseDuration;
      
      setState(prev => ({
        ...prev,
        status: 'running',
      }));
    }
  }, [state.status, state.currentPhase, state.settings, currentMode, setState, startTimeRef, pausedTimeRef, getPhaseDuration]);

  const handlePause = useCallback(() => {
    if (state.status === 'running') {
      pausedTimeRef.current = Date.now();
      setState(prev => ({
        ...prev,
        status: 'paused',
      }));
    }
  }, [state.status, setState, pausedTimeRef]);

  const handleReset = useCallback(() => {
    if (state.status !== 'idle') {
      Alert.alert(
        'Reset Timer',
        'Are you sure you want to reset the timer? All progress will be lost.',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Reset',
            style: 'destructive',
            onPress: () => {
              // Cancel any scheduled notifications
              notificationManager.cancelAllNotifications();
              
              // Reset timer state
              startTimeRef.current = null;
              pausedTimeRef.current = 0;
              
              const initialTime = currentMode === 'pomodoro' 
                ? getPhaseDuration('work', state.settings)
                : 0;
              
              setState(prev => ({
                ...prev,
                status: 'idle',
                displayTime: initialTime,
                currentPhase: 'work',
                completedSessions: 0,
              }));
            },
          },
        ]
      );
    }
  }, [state.status, state.settings, currentMode, setState, notificationManager, startTimeRef, pausedTimeRef, getPhaseDuration]);

  const handleModeToggle = useCallback(() => {
    if (state.status === 'idle') {
      const newMode: TimerMode = currentMode === 'pomodoro' ? 'stopwatch' : 'pomodoro';
      setCurrentMode(newMode);
      
      const initialTime = newMode === 'pomodoro' 
        ? getPhaseDuration('work', state.settings)
        : 0;
      
      setState(prev => ({
        ...prev,
        mode: newMode,
        displayTime: initialTime,
        currentPhase: 'work',
        completedSessions: 0,
      }));
    }
  }, [state.status, state.settings, currentMode, setState, getPhaseDuration]);

  // Get total duration for progress calculation
  const getTotalDuration = () => {
    if (currentMode === 'stopwatch') return 0;
    return getPhaseDuration(state.currentPhase, state.settings);
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      flexGrow: 1,
      justifyContent: 'center',
      paddingHorizontal: 24,
      paddingVertical: 32,
    },
    content: {
      alignItems: 'center',
      gap: 32,
    },
    displayContainer: {
      width: '100%',
      alignItems: 'center',
    },
    controlsContainer: {
      width: '100%',
      alignItems: 'center',
    },
  });

  return (
    <View style={styles.container}>
      <ScrollView 
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          {/* Timer Display */}
          <View style={styles.displayContainer}>
            <TimerDisplay
              time={state.displayTime}
              mode={currentMode}
              phase={state.currentPhase}
              status={state.status}
              totalDuration={getTotalDuration()}
              completedSessions={state.completedSessions}
            />
          </View>

          {/* Timer Controls */}
          <View style={styles.controlsContainer}>
            <TimerControls
              status={state.status}
              mode={currentMode}
              onStart={handleStart}
              onPause={handlePause}
              onReset={handleReset}
              onModeToggle={handleModeToggle}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default Timer;
