import React, { useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  Pressable,
  Dimensions,
} from 'react-native';
import { Text, Card, Surface } from 'react-native-paper';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withRepeat,
  withSequence,
  interpolate,
  Easing,
  runOnJS,
} from 'react-native-reanimated';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { useTimerStore, TimerPhase, TimerStatus } from '../../stores/timerStore';
import { formatTime, calculatePhaseProgress } from '../../utils/timerUtils';

const { width: screenWidth } = Dimensions.get('window');
const CARD_SIZE = screenWidth * 0.85;
const PROGRESS_RING_SIZE = CARD_SIZE * 0.7;

interface ExpressiveTimerCardProps {
  onPress?: () => void;
  showProgress?: boolean;
  compact?: boolean;
}

const ExpressiveTimerCard: React.FC<ExpressiveTimerCardProps> = ({
  onPress,
  showProgress = true,
  compact = false,
}) => {
  const theme = useDynamicTheme();
  const {
    status,
    mode,
    currentPhase,
    displayTime,
    elapsedTime,
    selectedSubject,
    selectedSubjectColor,
    workDuration,
    shortBreakDuration,
    longBreakDuration,
  } = useTimerStore();

  // Animation values
  const scale = useSharedValue(1);
  const pulseOpacity = useSharedValue(1);
  const progressRotation = useSharedValue(0);
  const cardElevation = useSharedValue(2);
  const glowIntensity = useSharedValue(0);

  // Get phase-specific colors
  const getPhaseColors = () => {
    // Use subject color if available, otherwise fall back to theme colors
    const subjectColor = selectedSubjectColor || theme.colors.primary;

    const baseColors = {
      work: {
        primary: subjectColor,
        surface: `${subjectColor}20`,
        accent: subjectColor,
      },
      shortBreak: {
        primary: theme.colors.secondary,
        surface: theme.colors.secondaryContainer,
        accent: theme.colors.onSecondaryContainer,
      },
      longBreak: {
        primary: theme.colors.tertiary,
        surface: theme.colors.tertiaryContainer,
        accent: theme.colors.onTertiaryContainer,
      },
      pause: {
        primary: theme.colors.outline,
        surface: theme.colors.surfaceVariant,
        accent: theme.colors.onSurfaceVariant,
      },
    };

    return baseColors[currentPhase] || baseColors.work;
  };

  const phaseColors = getPhaseColors();

  // Calculate progress
  const getTotalDuration = () => {
    switch (currentPhase) {
      case 'work':
        return workDuration * 60;
      case 'shortBreak':
        return shortBreakDuration * 60;
      case 'longBreak':
        return longBreakDuration * 60;
      default:
        return workDuration * 60;
    }
  };

  const progress = mode === 'pomodoro' 
    ? calculatePhaseProgress(elapsedTime, getTotalDuration()) / 100
    : 0;

  // Status-based animations
  useEffect(() => {
    switch (status) {
      case 'running':
        // Pulsing animation for running state
        pulseOpacity.value = withRepeat(
          withSequence(
            withTiming(0.7, { duration: 1000, easing: Easing.inOut(Easing.ease) }),
            withTiming(1, { duration: 1000, easing: Easing.inOut(Easing.ease) })
          ),
          -1,
          false
        );
        
        // Elevated state
        cardElevation.value = withTiming(8, { duration: 300 });
        glowIntensity.value = withTiming(0.3, { duration: 300 });
        break;
        
      case 'paused':
        // Gentle breathing animation for paused state
        pulseOpacity.value = withRepeat(
          withSequence(
            withTiming(0.8, { duration: 2000, easing: Easing.inOut(Easing.ease) }),
            withTiming(1, { duration: 2000, easing: Easing.inOut(Easing.ease) })
          ),
          -1,
          false
        );
        
        cardElevation.value = withTiming(4, { duration: 300 });
        glowIntensity.value = withTiming(0.1, { duration: 300 });
        break;
        
      case 'completed':
        // Celebration animation
        scale.value = withSequence(
          withTiming(1.1, { duration: 200 }),
          withTiming(1, { duration: 200 })
        );
        
        cardElevation.value = withTiming(12, { duration: 300 });
        glowIntensity.value = withTiming(0.5, { duration: 300 });
        
        // Reset after celebration
        setTimeout(() => {
          cardElevation.value = withTiming(2, { duration: 500 });
          glowIntensity.value = withTiming(0, { duration: 500 });
        }, 1000);
        break;
        
      default:
        // Idle state
        pulseOpacity.value = withTiming(1, { duration: 300 });
        cardElevation.value = withTiming(2, { duration: 300 });
        glowIntensity.value = withTiming(0, { duration: 300 });
        break;
    }
  }, [status]);

  // Progress animation
  useEffect(() => {
    if (mode === 'pomodoro' && showProgress) {
      progressRotation.value = withTiming(progress * 360, {
        duration: 500,
        easing: Easing.out(Easing.ease),
      });
    }
  }, [progress, mode, showProgress]);

  // Press animation
  const handlePressIn = () => {
    scale.value = withTiming(0.95, { duration: 100 });
  };

  const handlePressOut = () => {
    scale.value = withTiming(1, { duration: 100 });
  };

  // Animated styles
  const cardAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    elevation: cardElevation.value,
    shadowOpacity: interpolate(cardElevation.value, [2, 12], [0.1, 0.3]),
    shadowRadius: interpolate(cardElevation.value, [2, 12], [4, 16]),
  }));

  const pulseAnimatedStyle = useAnimatedStyle(() => ({
    opacity: pulseOpacity.value,
  }));

  const glowAnimatedStyle = useAnimatedStyle(() => ({
    opacity: glowIntensity.value,
  }));

  const progressAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${progressRotation.value}deg` }],
  }));

  // Get status text
  const getStatusText = () => {
    switch (status) {
      case 'running':
        return mode === 'pomodoro' ? `${currentPhase.toUpperCase()} TIME` : 'STUDYING';
      case 'paused':
        return 'PAUSED';
      case 'completed':
        return 'COMPLETED!';
      default:
        return 'READY TO START';
    }
  };

  const styles = StyleSheet.create({
    container: {
      alignItems: 'center',
      marginVertical: 16,
    },
    card: {
      width: compact ? CARD_SIZE * 0.8 : CARD_SIZE,
      height: compact ? CARD_SIZE * 0.6 : CARD_SIZE * 0.8,
      borderRadius: 24,
      overflow: 'hidden',
    },
    cardContent: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 24,
      position: 'relative',
    },
    glowOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: 24,
      backgroundColor: phaseColors.primary,
    },
    progressRing: {
      position: 'absolute',
      width: PROGRESS_RING_SIZE,
      height: PROGRESS_RING_SIZE,
      borderRadius: PROGRESS_RING_SIZE / 2,
      borderWidth: 4,
      borderColor: `${phaseColors.primary}20`,
    },
    progressIndicator: {
      position: 'absolute',
      width: PROGRESS_RING_SIZE,
      height: PROGRESS_RING_SIZE,
      borderRadius: PROGRESS_RING_SIZE / 2,
      borderWidth: 4,
      borderColor: 'transparent',
      borderTopColor: phaseColors.primary,
    },
    timeContainer: {
      alignItems: 'center',
      zIndex: 1,
    },
    timeText: {
      fontSize: compact ? 36 : 48,
      fontWeight: '300',
      color: phaseColors.accent,
      fontVariant: ['tabular-nums'],
    },
    statusText: {
      fontSize: compact ? 12 : 14,
      fontWeight: '600',
      color: phaseColors.primary,
      marginTop: 8,
      letterSpacing: 1,
    },
    subjectText: {
      fontSize: compact ? 14 : 16,
      fontWeight: '500',
      color: phaseColors.accent,
      marginTop: 4,
      opacity: 0.8,
    },
    modeIndicator: {
      position: 'absolute',
      top: 16,
      right: 16,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      backgroundColor: `${phaseColors.primary}20`,
    },
    modeText: {
      fontSize: 10,
      fontWeight: '600',
      color: phaseColors.primary,
      textTransform: 'uppercase',
    },
  });

  return (
    <View style={styles.container}>
      <Pressable
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={!onPress}
      >
        <Animated.View style={cardAnimatedStyle}>
          <Card style={[styles.card, { backgroundColor: phaseColors.surface }]}>
            <View style={styles.cardContent}>
              {/* Glow overlay */}
              <Animated.View style={[styles.glowOverlay, glowAnimatedStyle]} />
              
              {/* Progress ring */}
              {showProgress && mode === 'pomodoro' && (
                <>
                  <View style={styles.progressRing} />
                  <Animated.View style={[styles.progressIndicator, progressAnimatedStyle]} />
                </>
              )}
              
              {/* Mode indicator */}
              <View style={styles.modeIndicator}>
                <Text style={styles.modeText}>{mode}</Text>
              </View>
              
              {/* Timer content */}
              <Animated.View style={[styles.timeContainer, pulseAnimatedStyle]}>
                <Text style={styles.timeText}>
                  {formatTime(displayTime)}
                </Text>
                <Text style={styles.statusText}>
                  {getStatusText()}
                </Text>
                {selectedSubject && (
                  <Text style={styles.subjectText}>
                    {selectedSubject}
                  </Text>
                )}
              </Animated.View>
            </View>
          </Card>
        </Animated.View>
      </Pressable>
    </View>
  );
};

export default ExpressiveTimerCard;
