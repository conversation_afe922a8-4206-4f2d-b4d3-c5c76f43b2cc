import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../../constants/expressiveTheme';
import { Achievement } from '../../stores/achievementsStore';
import ExpressiveCard from '../surfaces/ExpressiveCard';

interface AchievementCardProps {
  achievement: Achievement;
  onPress?: (achievement: Achievement) => void;
  showProgress?: boolean;
  size?: 'small' | 'medium' | 'large';
  animationDelay?: number;
}

export const AchievementCard: React.FC<AchievementCardProps> = ({
  achievement,
  onPress,
  showProgress = true,
  size = 'medium',
  animationDelay = 0,
}) => {
  const theme = useDynamicTheme();
  const animatedValue = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: 1,
      duration: 500,
      delay: animationDelay,
      useNativeDriver: true,
    }).start();
  }, [animationDelay]);

  const styles = StyleSheet.create({
    container: {
      opacity: achievement.isUnlocked ? 1 : 0.6,
    },
    card: {
      padding: size === 'small' ? 12 : size === 'large' ? 20 : 16,
      borderRadius: size === 'small' ? 8 : size === 'large' ? 16 : 12,
      borderWidth: achievement.isUnlocked ? 2 : 1,
      borderColor: achievement.isUnlocked 
        ? getTierColor(achievement.tier, theme)
        : theme.colors.outline,
      backgroundColor: achievement.isUnlocked
        ? getTierBackgroundColor(achievement.tier, theme)
        : theme.colors.surface,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: size === 'small' ? 6 : 8,
    },
    icon: {
      fontSize: size === 'small' ? 20 : size === 'large' ? 32 : 24,
      marginRight: size === 'small' ? 8 : 12,
      opacity: achievement.isUnlocked ? 1 : 0.5,
    },
    titleContainer: {
      flex: 1,
    },
    title: {
      ...getTypographyForSize(size, 'title'),
      color: achievement.isUnlocked 
        ? getTierColor(achievement.tier, theme)
        : theme.colors.onSurface,
      fontWeight: '600',
    },
    tierBadge: {
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 8,
      backgroundColor: getTierColor(achievement.tier, theme),
      marginLeft: 8,
    },
    tierText: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onPrimary,
      fontSize: size === 'small' ? 8 : 10,
      fontWeight: '600',
      textTransform: 'uppercase',
    },
    description: {
      ...getTypographyForSize(size, 'description'),
      color: theme.colors.onSurfaceVariant,
      lineHeight: size === 'small' ? 16 : size === 'large' ? 22 : 18,
      marginBottom: showProgress && !achievement.isUnlocked ? 8 : 0,
    },
    progressContainer: {
      marginTop: 8,
    },
    progressBar: {
      height: 4,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 2,
      overflow: 'hidden',
    },
    progressFill: {
      height: '100%',
      backgroundColor: getTierColor(achievement.tier, theme),
      borderRadius: 2,
    },
    progressText: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onSurfaceVariant,
      marginTop: 4,
      textAlign: 'right',
    },
    rewardContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 8,
      paddingTop: 8,
      borderTopWidth: 1,
      borderTopColor: theme.colors.outline,
    },
    rewardText: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.primary,
      fontWeight: '600',
    },
    unlockedBadge: {
      position: 'absolute',
      top: -8,
      right: -8,
      backgroundColor: theme.colors.tertiary,
      borderRadius: 12,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderWidth: 2,
      borderColor: theme.colors.surface,
    },
    unlockedText: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onTertiary,
      fontWeight: '600',
      fontSize: 10,
    },
    secretOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderRadius: size === 'small' ? 8 : size === 'large' ? 16 : 12,
      justifyContent: 'center',
      alignItems: 'center',
    },
    secretText: {
      ...ExpressiveTypography.bodyMedium,
      color: theme.colors.onSurface,
      textAlign: 'center',
    },
  });

  const transform = [
    {
      scale: animatedValue.interpolate({
        inputRange: [0, 1],
        outputRange: [0.8, 1],
      }),
    },
    {
      translateY: animatedValue.interpolate({
        inputRange: [0, 1],
        outputRange: [20, 0],
      }),
    },
  ];

  const renderProgressBar = () => {
    if (!showProgress || achievement.isUnlocked) return null;

    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View 
            style={[
              styles.progressFill,
              { width: `${achievement.progress}%` }
            ]} 
          />
        </View>
        <Text style={styles.progressText}>
          {achievement.progress.toFixed(0)}% complete
        </Text>
      </View>
    );
  };

  const renderReward = () => {
    if (!achievement.reward || size === 'small') return null;

    return (
      <View style={styles.rewardContainer}>
        <Text style={styles.rewardText}>
          🏆 {achievement.reward.points} points
        </Text>
      </View>
    );
  };

  const renderSecretOverlay = () => {
    if (!achievement.isSecret || achievement.isUnlocked) return null;

    return (
      <View style={styles.secretOverlay}>
        <Text style={styles.secretText}>
          🔒{'\n'}Secret Achievement{'\n'}Keep exploring to unlock!
        </Text>
      </View>
    );
  };

  return (
    <Animated.View style={[styles.container, { transform }]}>
      <TouchableOpacity
        onPress={() => onPress?.(achievement)}
        activeOpacity={0.7}
        disabled={!onPress}
      >
        <View style={styles.card}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.icon}>{achievement.icon}</Text>
            <View style={styles.titleContainer}>
              <Text style={styles.title} numberOfLines={size === 'small' ? 1 : 2}>
                {achievement.title}
              </Text>
            </View>
            <View style={styles.tierBadge}>
              <Text style={styles.tierText}>{achievement.tier}</Text>
            </View>
          </View>

          {/* Description */}
          {size !== 'small' && (
            <Text style={styles.description} numberOfLines={size === 'large' ? 3 : 2}>
              {achievement.description}
            </Text>
          )}

          {/* Progress Bar */}
          {renderProgressBar()}

          {/* Reward */}
          {renderReward()}

          {/* Unlocked Badge */}
          {achievement.isUnlocked && (
            <View style={styles.unlockedBadge}>
              <Text style={styles.unlockedText}>✓</Text>
            </View>
          )}

          {/* Secret Overlay */}
          {renderSecretOverlay()}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

// Helper functions
const getTierColor = (tier: Achievement['tier'], theme: any): string => {
  switch (tier) {
    case 'bronze': return '#CD7F32';
    case 'silver': return '#C0C0C0';
    case 'gold': return '#FFD700';
    case 'platinum': return '#E5E4E2';
    case 'diamond': return '#B9F2FF';
    default: return theme.colors.primary;
  }
};

const getTierBackgroundColor = (tier: Achievement['tier'], theme: any): string => {
  switch (tier) {
    case 'bronze': return '#FFF8DC';
    case 'silver': return '#F8F8FF';
    case 'gold': return '#FFFACD';
    case 'platinum': return '#F5F5F5';
    case 'diamond': return '#F0FFFF';
    default: return theme.colors.primaryContainer;
  }
};

const getTypographyForSize = (size: 'small' | 'medium' | 'large', type: 'title' | 'description') => {
  if (type === 'title') {
    switch (size) {
      case 'small': return ExpressiveTypography.labelMedium;
      case 'large': return ExpressiveTypography.titleLarge;
      default: return ExpressiveTypography.titleMedium;
    }
  } else {
    switch (size) {
      case 'small': return ExpressiveTypography.labelSmall;
      case 'large': return ExpressiveTypography.bodyLarge;
      default: return ExpressiveTypography.bodyMedium;
    }
  }
};

export default AchievementCard;
