import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  Animated,
  Dimensions,
  TouchableOpacity,
  Easing,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../../constants/expressiveTheme';
import { Achievement } from '../../stores/achievementsStore';
import ExpressiveButton from '../buttons/ExpressiveButton';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface AchievementCelebrationProps {
  visible: boolean;
  achievement: Achievement | null;
  onClose: () => void;
}

export const AchievementCelebration: React.FC<AchievementCelebrationProps> = ({
  visible,
  achievement,
  onClose,
}) => {
  const theme = useDynamicTheme();
  
  // Animation values
  const overlayOpacity = useRef(new Animated.Value(0)).current;
  const cardScale = useRef(new Animated.Value(0)).current;
  const cardRotation = useRef(new Animated.Value(0)).current;
  const iconScale = useRef(new Animated.Value(0)).current;
  const iconRotation = useRef(new Animated.Value(0)).current;
  const confettiAnimations = useRef(
    Array.from({ length: 20 }, () => ({
      translateY: new Animated.Value(-100),
      translateX: new Animated.Value(Math.random() * screenWidth),
      rotation: new Animated.Value(0),
      opacity: new Animated.Value(1),
    }))
  ).current;

  const styles = StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    card: {
      backgroundColor: theme.colors.surface,
      borderRadius: 24,
      padding: 32,
      alignItems: 'center',
      maxWidth: screenWidth - 40,
      borderWidth: 3,
      borderColor: achievement ? getTierColor(achievement.tier) : theme.colors.primary,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 10 },
      shadowOpacity: 0.3,
      shadowRadius: 20,
      elevation: 20,
    },
    celebrationHeader: {
      alignItems: 'center',
      marginBottom: 24,
    },
    celebrationTitle: {
      ...ExpressiveTypography.headlineMedium,
      color: theme.colors.primary,
      fontWeight: '700',
      textAlign: 'center',
      marginBottom: 8,
    },
    celebrationSubtitle: {
      ...ExpressiveTypography.bodyLarge,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
    },
    achievementContainer: {
      alignItems: 'center',
      marginBottom: 24,
    },
    achievementIcon: {
      fontSize: 80,
      marginBottom: 16,
    },
    achievementTitle: {
      ...ExpressiveTypography.titleLarge,
      color: theme.colors.onSurface,
      fontWeight: '600',
      textAlign: 'center',
      marginBottom: 8,
    },
    achievementDescription: {
      ...ExpressiveTypography.bodyMedium,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      lineHeight: 20,
      marginBottom: 16,
    },
    tierBadge: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 20,
      backgroundColor: achievement ? getTierColor(achievement.tier) : theme.colors.primary,
      marginBottom: 16,
    },
    tierText: {
      ...ExpressiveTypography.labelLarge,
      color: '#FFFFFF',
      fontWeight: '700',
      textTransform: 'uppercase',
    },
    rewardContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.primaryContainer,
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderRadius: 16,
      marginBottom: 24,
    },
    rewardText: {
      ...ExpressiveTypography.titleMedium,
      color: theme.colors.onPrimaryContainer,
      fontWeight: '600',
      marginLeft: 8,
    },
    buttonContainer: {
      width: '100%',
    },
    confetti: {
      position: 'absolute',
      width: 8,
      height: 8,
      borderRadius: 4,
    },
    sparkle: {
      position: 'absolute',
      fontSize: 16,
    },
  });

  // Animation sequences
  useEffect(() => {
    if (visible && achievement) {
      // Reset animations
      overlayOpacity.setValue(0);
      cardScale.setValue(0);
      cardRotation.setValue(-10);
      iconScale.setValue(0);
      iconRotation.setValue(0);
      
      confettiAnimations.forEach(anim => {
        anim.translateY.setValue(-100);
        anim.rotation.setValue(0);
        anim.opacity.setValue(1);
      });

      // Start celebration sequence
      Animated.sequence([
        // Fade in overlay
        Animated.timing(overlayOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        
        // Card entrance with bounce
        Animated.parallel([
          Animated.spring(cardScale, {
            toValue: 1,
            tension: 100,
            friction: 8,
            useNativeDriver: true,
          }),
          Animated.spring(cardRotation, {
            toValue: 0,
            tension: 100,
            friction: 8,
            useNativeDriver: true,
          }),
        ]),
        
        // Icon celebration
        Animated.parallel([
          Animated.sequence([
            Animated.spring(iconScale, {
              toValue: 1.2,
              tension: 100,
              friction: 6,
              useNativeDriver: true,
            }),
            Animated.spring(iconScale, {
              toValue: 1,
              tension: 100,
              friction: 8,
              useNativeDriver: true,
            }),
          ]),
          Animated.timing(iconRotation, {
            toValue: 360,
            duration: 1000,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: true,
          }),
        ]),
      ]).start();

      // Confetti animation
      const confettiSequence = confettiAnimations.map((anim, index) => 
        Animated.parallel([
          Animated.timing(anim.translateY, {
            toValue: screenHeight + 100,
            duration: 3000 + Math.random() * 1000,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: true,
          }),
          Animated.timing(anim.rotation, {
            toValue: 720 + Math.random() * 360,
            duration: 3000 + Math.random() * 1000,
            useNativeDriver: true,
          }),
          Animated.sequence([
            Animated.delay(2000),
            Animated.timing(anim.opacity, {
              toValue: 0,
              duration: 1000,
              useNativeDriver: true,
            }),
          ]),
        ])
      );

      Animated.stagger(100, confettiSequence).start();
    }
  }, [visible, achievement]);

  const getTierColor = (tier: Achievement['tier']): string => {
    switch (tier) {
      case 'bronze': return '#CD7F32';
      case 'silver': return '#C0C0C0';
      case 'gold': return '#FFD700';
      case 'platinum': return '#E5E4E2';
      case 'diamond': return '#B9F2FF';
      default: return theme.colors.primary;
    }
  };

  const getConfettiColors = (): string[] => {
    if (!achievement) return [theme.colors.primary];
    
    switch (achievement.tier) {
      case 'bronze': return ['#CD7F32', '#DEB887', '#F4A460'];
      case 'silver': return ['#C0C0C0', '#D3D3D3', '#E6E6FA'];
      case 'gold': return ['#FFD700', '#FFA500', '#FFFF00'];
      case 'platinum': return ['#E5E4E2', '#F0F0F0', '#FFFFFF'];
      case 'diamond': return ['#B9F2FF', '#87CEEB', '#00BFFF'];
      default: return [theme.colors.primary, theme.colors.secondary, theme.colors.tertiary];
    }
  };

  const renderConfetti = () => {
    const colors = getConfettiColors();
    
    return confettiAnimations.map((anim, index) => {
      const color = colors[index % colors.length];
      
      return (
        <Animated.View
          key={index}
          style={[
            styles.confetti,
            {
              backgroundColor: color,
              transform: [
                { translateX: anim.translateX },
                { translateY: anim.translateY },
                { 
                  rotate: anim.rotation.interpolate({
                    inputRange: [0, 360],
                    outputRange: ['0deg', '360deg'],
                  })
                },
              ],
              opacity: anim.opacity,
            },
          ]}
        />
      );
    });
  };

  const renderSparkles = () => {
    const sparklePositions = [
      { top: '10%', left: '10%' },
      { top: '15%', right: '15%' },
      { top: '25%', left: '5%' },
      { top: '30%', right: '10%' },
      { bottom: '20%', left: '8%' },
      { bottom: '25%', right: '12%' },
    ];

    return sparklePositions.map((position, index) => (
      <Animated.Text
        key={index}
        style={[
          styles.sparkle,
          position,
          {
            opacity: overlayOpacity,
            transform: [
              {
                scale: overlayOpacity.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, 1],
                }),
              },
            ],
          },
        ]}
      >
        ✨
      </Animated.Text>
    ));
  };

  if (!achievement) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <Animated.View style={[styles.overlay, { opacity: overlayOpacity }]}>
        {/* Confetti */}
        {renderConfetti()}
        
        {/* Sparkles */}
        {renderSparkles()}
        
        {/* Achievement Card */}
        <Animated.View
          style={[
            styles.card,
            {
              transform: [
                { scale: cardScale },
                {
                  rotate: cardRotation.interpolate({
                    inputRange: [-10, 0],
                    outputRange: ['-10deg', '0deg'],
                  }),
                },
              ],
            },
          ]}
        >
          {/* Header */}
          <View style={styles.celebrationHeader}>
            <Text style={styles.celebrationTitle}>🎉 Achievement Unlocked! 🎉</Text>
            <Text style={styles.celebrationSubtitle}>
              Congratulations on your progress!
            </Text>
          </View>

          {/* Achievement Details */}
          <View style={styles.achievementContainer}>
            <Animated.Text
              style={[
                styles.achievementIcon,
                {
                  transform: [
                    { scale: iconScale },
                    {
                      rotate: iconRotation.interpolate({
                        inputRange: [0, 360],
                        outputRange: ['0deg', '360deg'],
                      }),
                    },
                  ],
                },
              ]}
            >
              {achievement.icon}
            </Animated.Text>
            
            <Text style={styles.achievementTitle}>{achievement.title}</Text>
            <Text style={styles.achievementDescription}>{achievement.description}</Text>
            
            <View style={styles.tierBadge}>
              <Text style={styles.tierText}>{achievement.tier} Achievement</Text>
            </View>
            
            {achievement.reward && (
              <View style={styles.rewardContainer}>
                <Text style={styles.rewardText}>🏆</Text>
                <Text style={styles.rewardText}>
                  +{achievement.reward.points} XP
                </Text>
              </View>
            )}
          </View>

          {/* Close Button */}
          <View style={styles.buttonContainer}>
            <ExpressiveButton
              title="Awesome!"
              variant="filled"
              onPress={onClose}
              icon="🎯"
            />
          </View>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

export default AchievementCelebration;
