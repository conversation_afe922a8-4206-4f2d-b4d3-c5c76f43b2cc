import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import { useDynamicTheme } from '../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../constants/expressiveTheme';
import ExpressiveCard from './surfaces/ExpressiveCard';
import ExpressiveButton from './buttons/ExpressiveButton';
import ExpressiveIcon from './icons/ExpressiveIcon';
import ExpressiveIconButton from './icons/ExpressiveIconButton';
import { IconProvider, useIconTheme, useThemedIcon } from './icons/IconProvider';
import {
  MaterialIcons,
  EmojiIcons,
  IconSizes,
  IconSets,
} from '../constants/icons';

/**
 * Icon System Demo Component
 * Demonstrates the comprehensive icon system with theming
 */
const IconSystemDemoContent: React.FC = () => {
  const theme = useDynamicTheme();
  const iconTheme = useIconTheme();
  const [selectedSize, setSelectedSize] = useState<keyof typeof IconSizes>('md');
  const [selectedVariant, setSelectedVariant] = useState<string>('primary');

  const iconSizes = Object.keys(IconSizes) as (keyof typeof IconSizes)[];
  const iconVariants = ['primary', 'secondary', 'tertiary', 'surface', 'error', 'warning', 'success'];

  // Sample Material Icons
  const sampleMaterialIcons = [
    MaterialIcons.home,
    MaterialIcons.timer,
    MaterialIcons.task,
    MaterialIcons.analytics,
    MaterialIcons.person,
    MaterialIcons.settings,
    MaterialIcons.notification,
    MaterialIcons.favorite,
    MaterialIcons.star,
    MaterialIcons.share,
    MaterialIcons.search,
    MaterialIcons.add,
    MaterialIcons.edit,
    MaterialIcons.delete,
    MaterialIcons.check,
    MaterialIcons.close,
  ];

  // Sample Emoji Icons
  const sampleEmojiIcons = [
    EmojiIcons.pomodoro,
    EmojiIcons.timer,
    EmojiIcons.focus,
    EmojiIcons.energy,
    EmojiIcons.success,
    EmojiIcons.fire,
    EmojiIcons.star,
    EmojiIcons.rocket,
    EmojiIcons.trophy,
    EmojiIcons.lightbulb,
    EmojiIcons.brain,
    EmojiIcons.target,
    EmojiIcons.celebration,
    EmojiIcons.thumbsUp,
    EmojiIcons.coffee,
    EmojiIcons.smile,
  ];

  const styles = StyleSheet.create({
    container: {
      padding: 16,
    },
    section: {
      marginBottom: 32,
    },
    sectionTitle: {
      ...ExpressiveTypography.headlineMedium,
      color: theme.colors.primary,
      marginBottom: 16,
    },
    subsectionTitle: {
      ...ExpressiveTypography.titleMedium,
      color: theme.colors.onSurface,
      marginBottom: 12,
    },
    description: {
      ...ExpressiveTypography.bodyMedium,
      color: theme.colors.onSurfaceVariant,
      marginBottom: 16,
      lineHeight: 20,
    },
    controlsRow: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      marginBottom: 16,
    },
    controlButton: {
      paddingHorizontal: 12,
      paddingVertical: 6,
    },
    iconGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 16,
      marginBottom: 16,
    },
    iconItem: {
      alignItems: 'center',
      justifyContent: 'center',
      width: 60,
      height: 60,
      backgroundColor: theme.colors.surfaceContainer,
      borderRadius: 12,
    },
    iconLabel: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginTop: 4,
      fontSize: 10,
    },
    buttonGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
      marginBottom: 16,
    },
    themedIconsRow: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
      marginBottom: 16,
    },
    themedIconContainer: {
      alignItems: 'center',
      gap: 8,
    },
    themeLabel: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onSurfaceVariant,
    },
  });

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Icon System</Text>
        <Text style={styles.description}>
          Comprehensive icon system with Material Icons, Emoji icons, theming support, and interactive components.
        </Text>
      </View>

      {/* Size Controls */}
      <View style={styles.section}>
        <Text style={styles.subsectionTitle}>Icon Sizes</Text>
        <View style={styles.controlsRow}>
          {iconSizes.map((size) => (
            <ExpressiveButton
              key={size}
              title={size.toUpperCase()}
              variant={selectedSize === size ? 'filled' : 'outlined'}
              size="small"
              style={styles.controlButton}
              onPress={() => setSelectedSize(size)}
            />
          ))}
        </View>
        
        <View style={styles.iconGrid}>
          {sampleMaterialIcons.slice(0, 8).map((iconName) => (
            <View key={iconName} style={styles.iconItem}>
              <ExpressiveIcon
                family="material"
                name={iconName}
                size={selectedSize}
                variant={selectedVariant}
              />
            </View>
          ))}
        </View>
      </View>

      {/* Color Variants */}
      <View style={styles.section}>
        <Text style={styles.subsectionTitle}>Color Variants</Text>
        <View style={styles.controlsRow}>
          {iconVariants.map((variant) => (
            <ExpressiveButton
              key={variant}
              title={variant}
              variant={selectedVariant === variant ? 'filled' : 'outlined'}
              size="small"
              style={styles.controlButton}
              onPress={() => setSelectedVariant(variant)}
            />
          ))}
        </View>
        
        <View style={styles.iconGrid}>
          {sampleMaterialIcons.slice(0, 8).map((iconName) => (
            <View key={iconName} style={styles.iconItem}>
              <ExpressiveIcon
                family="material"
                name={iconName}
                size="lg"
                variant={selectedVariant as any}
              />
            </View>
          ))}
        </View>
      </View>

      {/* Material Icons */}
      <View style={styles.section}>
        <Text style={styles.subsectionTitle}>Material Icons</Text>
        <ExpressiveCard
          title="Material Design Icons"
          subtitle="Standard Material 3 iconography"
          variant="outlined"
        >
          <View style={styles.iconGrid}>
            {sampleMaterialIcons.map((iconName) => (
              <View key={iconName} style={styles.iconItem}>
                <ExpressiveIcon
                  family="material"
                  name={iconName}
                  size="lg"
                  variant="surface"
                />
              </View>
            ))}
          </View>
        </ExpressiveCard>
      </View>

      {/* Emoji Icons */}
      <View style={styles.section}>
        <Text style={styles.subsectionTitle}>Emoji Icons</Text>
        <ExpressiveCard
          title="Expressive Emoji Icons"
          subtitle="Colorful and expressive iconography"
          variant="outlined"
        >
          <View style={styles.iconGrid}>
            {sampleEmojiIcons.map((iconName) => (
              <View key={iconName} style={styles.iconItem}>
                <ExpressiveIcon
                  family="emoji"
                  name={iconName}
                  size="lg"
                />
              </View>
            ))}
          </View>
        </ExpressiveCard>
      </View>

      {/* Themed Icons */}
      <View style={styles.section}>
        <Text style={styles.subsectionTitle}>Theme-Aware Icons</Text>
        <Text style={styles.description}>
          Icons that automatically adapt to light and dark themes:
        </Text>
        
        <ExpressiveCard variant="outlined">
          <View style={styles.themedIconsRow}>
            <View style={styles.themedIconContainer}>
              <ExpressiveIcon
                family="material"
                name={useThemedIcon('home')}
                size="xl"
                variant="primary"
              />
              <Text style={styles.themeLabel}>Home</Text>
            </View>
            
            <View style={styles.themedIconContainer}>
              <ExpressiveIcon
                family="material"
                name={useThemedIcon('favorite')}
                size="xl"
                variant="error"
              />
              <Text style={styles.themeLabel}>Favorite</Text>
            </View>
            
            <View style={styles.themedIconContainer}>
              <ExpressiveIcon
                family="material"
                name={useThemedIcon('notification')}
                size="xl"
                variant="secondary"
              />
              <Text style={styles.themeLabel}>Notification</Text>
            </View>
            
            <View style={styles.themedIconContainer}>
              <ExpressiveIcon
                family="material"
                name={useThemedIcon('settings')}
                size="xl"
                variant="tertiary"
              />
              <Text style={styles.themeLabel}>Settings</Text>
            </View>
          </View>
        </ExpressiveCard>
      </View>

      {/* Icon Buttons */}
      <View style={styles.section}>
        <Text style={styles.subsectionTitle}>Icon Buttons</Text>
        
        <ExpressiveCard
          title="Standard Icon Buttons"
          subtitle="Different variants and sizes"
          variant="outlined"
        >
          <View style={styles.buttonGrid}>
            <ExpressiveIconButton
              iconName={MaterialIcons.favorite}
              variant="standard"
              size="small"
              onPress={() => Alert.alert('Standard Small', 'Pressed!')}
            />
            <ExpressiveIconButton
              iconName={MaterialIcons.favorite}
              variant="filled"
              size="medium"
              onPress={() => Alert.alert('Filled Medium', 'Pressed!')}
            />
            <ExpressiveIconButton
              iconName={MaterialIcons.favorite}
              variant="tonal"
              size="large"
              onPress={() => Alert.alert('Tonal Large', 'Pressed!')}
            />
            <ExpressiveIconButton
              iconName={MaterialIcons.favorite}
              variant="outlined"
              size="medium"
              onPress={() => Alert.alert('Outlined Medium', 'Pressed!')}
            />
          </View>
        </ExpressiveCard>
        
        <ExpressiveCard
          title="Action Icon Buttons"
          subtitle="Common actions with animations"
          variant="outlined"
          style={{ marginTop: 16 }}
        >
          <View style={styles.buttonGrid}>
            <ExpressiveIconButton
              iconName={MaterialIcons.add}
              variant="filled"
              colorVariant="primary"
              onPress={() => Alert.alert('Add', 'Create new item')}
            />
            <ExpressiveIconButton
              iconName={MaterialIcons.edit}
              variant="tonal"
              colorVariant="secondary"
              onPress={() => Alert.alert('Edit', 'Edit item')}
            />
            <ExpressiveIconButton
              iconName={MaterialIcons.delete}
              variant="outlined"
              colorVariant="error"
              onPress={() => Alert.alert('Delete', 'Delete item')}
            />
            <ExpressiveIconButton
              iconName={MaterialIcons.share}
              variant="standard"
              colorVariant="tertiary"
              onPress={() => Alert.alert('Share', 'Share item')}
            />
          </View>
        </ExpressiveCard>
      </View>

      {/* Icon Sets */}
      <View style={styles.section}>
        <Text style={styles.subsectionTitle}>Predefined Icon Sets</Text>
        <Text style={styles.description}>
          Organized icon collections for common use cases:
        </Text>
        
        <ExpressiveCard
          title="Navigation Icons"
          variant="outlined"
          style={{ marginBottom: 12 }}
        >
          <View style={styles.iconGrid}>
            {Object.entries(IconSets.navigation).map(([key, config]) => (
              <View key={key} style={styles.iconItem}>
                <ExpressiveIcon
                  family={config.family}
                  name={config.name}
                  size="lg"
                  variant="primary"
                />
                <Text style={styles.iconLabel}>{key}</Text>
              </View>
            ))}
          </View>
        </ExpressiveCard>
        
        <ExpressiveCard
          title="Timer Icons"
          variant="outlined"
          style={{ marginBottom: 12 }}
        >
          <View style={styles.iconGrid}>
            {Object.entries(IconSets.timer).map(([key, config]) => (
              <View key={key} style={styles.iconItem}>
                <ExpressiveIcon
                  family={config.family}
                  name={config.name}
                  size="lg"
                  variant="secondary"
                />
                <Text style={styles.iconLabel}>{key}</Text>
              </View>
            ))}
          </View>
        </ExpressiveCard>
      </View>
    </ScrollView>
  );
};

export const IconSystemDemo: React.FC = () => {
  return (
    <IconProvider defaultFamily="material" defaultSize="md">
      <IconSystemDemoContent />
    </IconProvider>
  );
};

export default IconSystemDemo;
