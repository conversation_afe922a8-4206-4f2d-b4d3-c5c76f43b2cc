import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  RefreshControl,
  StatusBar,
} from 'react-native';
import { useDynamicTheme } from '../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../constants/expressiveTheme';
import { useAnalyticsStore } from '../stores/analyticsStore';
import {
  TimeDistributionChart,
  ProductivityTrends,
  StudyStreak,
  GoalProgress,
  DateRangeSelector,
  TimeAggregationView,
  TimerModeComparison,
} from '../components/analytics';
import {
  AchievementCard,
  AchievementCelebration,
} from '../components/achievements';
import { useAchievementsStore } from '../stores/achievementsStore';
import ExpressiveCard from '../components/surfaces/ExpressiveCard';
import { getCurrentUser } from '../services/supabase/client';

export const AnalyticsScreen: React.FC = () => {
  const theme = useDynamicTheme();
  const {
    sessions,
    metrics,
    timeDistribution,
    productivityTrends,
    goalProgress,
    dateRange,
    isLoading,
    error,
    loadAnalytics,
    setDateRange,
    calculateMetrics,
    calculateTimeDistribution,
    calculateProductivityTrends,
    calculateGoalProgress,
  } = useAnalyticsStore();

  const {
    achievements,
    showCelebration,
    celebrationAchievement,
    hideCelebration,
    getUnlockedAchievements,
    checkAchievements,
    loadAchievements,
  } = useAchievementsStore();

  // Local state
  const [refreshing, setRefreshing] = useState(false);
  const [aggregationType, setAggregationType] = useState<'daily' | 'weekly' | 'monthly'>('daily');
  const [chartType, setChartType] = useState<'line' | 'bar' | 'area'>('line');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.surface,
    },
    header: {
      paddingHorizontal: 16,
      paddingVertical: 20,
      backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
    },
    title: {
      ...ExpressiveTypography.headlineMedium,
      color: theme.colors.onSurface,
      marginBottom: 8,
    },
    subtitle: {
      ...ExpressiveTypography.bodyMedium,
      color: theme.colors.onSurfaceVariant,
      lineHeight: 20,
    },
    content: {
      flex: 1,
    },
    section: {
      marginBottom: 16,
    },
    dateRangeContainer: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: theme.colors.surfaceContainer,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32,
    },
    errorText: {
      ...ExpressiveTypography.bodyLarge,
      color: theme.colors.error,
      textAlign: 'center',
      marginBottom: 16,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32,
    },
    emptyText: {
      ...ExpressiveTypography.bodyLarge,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginBottom: 16,
    },
    quickStatsContainer: {
      flexDirection: 'row',
      paddingHorizontal: 16,
      paddingVertical: 12,
      gap: 12,
    },
    quickStatCard: {
      flex: 1,
      backgroundColor: theme.colors.primaryContainer,
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 12,
      alignItems: 'center',
    },
    quickStatValue: {
      ...ExpressiveTypography.titleMedium,
      color: theme.colors.onPrimaryContainer,
      fontWeight: '600',
    },
    quickStatLabel: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onPrimaryContainer,
      marginTop: 2,
    },
  });

  // Initialize data
  useEffect(() => {
    const initializeAnalytics = async () => {
      try {
        const user = await getCurrentUser();
        if (user) {
          await loadAnalytics(user.id, dateRange);
          await loadAchievements();

          // Check for new achievements based on current metrics
          if (metrics) {
            await checkAchievements({
              totalStudyTime: metrics.totalStudyTime,
              totalSessions: metrics.totalSessions,
              currentStreak: metrics.currentStreak,
              longestStreak: metrics.longestStreak,
              averageProductivity: metrics.productivityScore,
            });
          }
        }
      } catch (error) {
        console.error('Failed to initialize analytics:', error);
      }
    };

    initializeAnalytics();
  }, [dateRange, metrics]);

  // Handle date range change
  const handleDateRangeChange = useCallback((newRange: typeof dateRange) => {
    setDateRange(newRange);
  }, [setDateRange]);

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      const user = await getCurrentUser();
      if (user) {
        await loadAnalytics(user.id, dateRange);
      }
    } catch (error) {
      console.error('Failed to refresh analytics:', error);
    } finally {
      setRefreshing(false);
    }
  }, [loadAnalytics, dateRange]);

  // Format time duration
  const formatDuration = useCallback((seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  }, []);

  // Mock data for demonstration (since we don't have real study sessions yet)
  const mockTimeDistribution = [
    { subject: 'Mathematics', time: 7200, percentage: 40, color: '#6750A4', sessions: 5 },
    { subject: 'Physics', time: 5400, percentage: 30, color: '#7C4DFF', sessions: 4 },
    { subject: 'Chemistry', time: 3600, percentage: 20, color: '#FF6B6B', sessions: 3 },
    { subject: 'Biology', time: 1800, percentage: 10, color: '#4ECDC4', sessions: 2 },
  ];

  const mockProductivityTrends = [
    { date: '2024-01-01', rating: 3.5, sessions: 3, totalTime: 7200 },
    { date: '2024-01-02', rating: 4.0, sessions: 4, totalTime: 8100 },
    { date: '2024-01-03', rating: 3.8, sessions: 3, totalTime: 6300 },
    { date: '2024-01-04', rating: 4.2, sessions: 5, totalTime: 9000 },
    { date: '2024-01-05', rating: 4.5, sessions: 4, totalTime: 7800 },
  ];

  const mockGoalProgress = {
    daily: { target: 7200, current: 5400, percentage: 75 },
    weekly: { target: 50400, current: 32400, percentage: 64 },
    monthly: { target: 216000, current: 129600, percentage: 60 },
  };

  // Render error state
  if (error) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor={theme.colors.surface} />
        <View style={styles.header}>
          <Text style={styles.title}>Analytics</Text>
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={theme.colors.surface} />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Analytics</Text>
        <Text style={styles.subtitle}>
          Track your productivity and see how you're improving over time.
        </Text>
      </View>

      {/* Date Range Selector */}
      <View style={styles.dateRangeContainer}>
        <DateRangeSelector
          selectedRange={dateRange}
          onRangeChange={handleDateRangeChange}
        />
      </View>

      {/* Quick Stats */}
      {metrics && (
        <View style={styles.quickStatsContainer}>
          <View style={styles.quickStatCard}>
            <Text style={styles.quickStatValue}>
              {formatDuration(metrics.totalStudyTime)}
            </Text>
            <Text style={styles.quickStatLabel}>Total Time</Text>
          </View>
          <View style={styles.quickStatCard}>
            <Text style={styles.quickStatValue}>{metrics.totalSessions}</Text>
            <Text style={styles.quickStatLabel}>Sessions</Text>
          </View>
          <View style={styles.quickStatCard}>
            <Text style={styles.quickStatValue}>{metrics.currentStreak}</Text>
            <Text style={styles.quickStatLabel}>Day Streak</Text>
          </View>
          <View style={styles.quickStatCard}>
            <Text style={styles.quickStatValue}>
              {metrics.productivityScore.toFixed(1)}⭐
            </Text>
            <Text style={styles.quickStatLabel}>Rating</Text>
          </View>
        </View>
      )}

      {/* Content */}
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing || isLoading}
            onRefresh={handleRefresh}
            tintColor={theme.colors.primary}
            colors={[theme.colors.primary]}
          />
        }
      >
        {/* Time Aggregation View */}
        <View style={styles.section}>
          <TimeAggregationView
            sessions={sessions}
            dateRange={dateRange}
            aggregationType={aggregationType}
            chartType={chartType}
            onAggregationChange={setAggregationType}
            onChartTypeChange={setChartType}
          />
        </View>

        {/* Time Distribution Chart */}
        <View style={styles.section}>
          <TimeDistributionChart
            data={timeDistribution.length > 0 ? timeDistribution : mockTimeDistribution}
            chartType="pie"
            showLegend={true}
            showPercentages={true}
          />
        </View>

        {/* Productivity Trends */}
        <View style={styles.section}>
          <ProductivityTrends
            data={productivityTrends.length > 0 ? productivityTrends : mockProductivityTrends}
            chartType="line"
            showAverage={true}
            showDataPoints={true}
          />
        </View>

        {/* Study Streak */}
        <View style={styles.section}>
          <StudyStreak
            currentStreak={metrics?.currentStreak || 7}
            longestStreak={metrics?.longestStreak || 15}
            showAnimation={true}
          />
        </View>

        {/* Timer Mode Comparison */}
        <View style={styles.section}>
          <TimerModeComparison
            sessions={sessions}
            chartType="bar"
            showProductivity={true}
            onModePress={(mode) => {
              console.log(`${mode} mode pressed`);
            }}
          />
        </View>

        {/* Goal Progress */}
        <View style={styles.section}>
          <GoalProgress
            goalProgress={goalProgress || mockGoalProgress}
            showAnimation={true}
            onEditGoals={() => {
              // TODO: Navigate to goal settings
              console.log('Edit goals pressed');
            }}
          />
        </View>

        {/* Recent Achievements */}
        <View style={styles.section}>
          <ExpressiveCard>
            <View style={{ padding: 16 }}>
              <Text style={{
                ...ExpressiveTypography.titleMedium,
                color: theme.colors.onSurface,
                marginBottom: 16,
              }}>
                🏆 Recent Achievements
              </Text>

              {getUnlockedAchievements().slice(0, 3).length > 0 ? (
                <View style={{ gap: 12 }}>
                  {getUnlockedAchievements()
                    .slice(-3) // Get last 3 unlocked achievements
                    .reverse() // Show most recent first
                    .map((achievement, index) => (
                      <AchievementCard
                        key={achievement.id}
                        achievement={achievement}
                        size="small"
                        showProgress={false}
                        animationDelay={index * 100}
                        onPress={() => {
                          console.log('Achievement pressed:', achievement.title);
                        }}
                      />
                    ))}
                </View>
              ) : (
                <View style={{
                  alignItems: 'center',
                  paddingVertical: 24,
                }}>
                  <Text style={{
                    fontSize: 32,
                    marginBottom: 8,
                  }}>🎯</Text>
                  <Text style={{
                    ...ExpressiveTypography.bodyMedium,
                    color: theme.colors.onSurfaceVariant,
                    textAlign: 'center',
                  }}>
                    Start studying to unlock your first achievement!
                  </Text>
                </View>
              )}
            </View>
          </ExpressiveCard>
        </View>
      </ScrollView>

      {/* Achievement Celebration Modal */}
      <AchievementCelebration
        visible={showCelebration}
        achievement={celebrationAchievement}
        onClose={hideCelebration}
      />
    </View>
  );
};

export default AnalyticsScreen;
