import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
} from 'react-native';
import { useDynamicTheme } from '../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../constants/expressiveTheme';
import ExpressiveCard from '../components/surfaces/ExpressiveCard';
import ExpressiveButton from '../components/buttons/ExpressiveButton';
import ExpressiveFAB from '../components/buttons/ExpressiveFAB';

// ===========================================
// 🏠 HOME SCREEN
// ===========================================

interface HomeScreenProps {
  navigation: any;
}

export const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const theme = useDynamicTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      padding: 16,
      paddingBottom: 100, // Space for FAB
    },
    header: {
      marginBottom: 24,
    },
    title: {
      ...ExpressiveTypography.displaySmall,
      color: theme.colors.onBackground,
      marginBottom: 8,
    },
    subtitle: {
      ...ExpressiveTypography.bodyLarge,
      color: theme.colors.onSurfaceVariant,
      lineHeight: 24,
    },
    section: {
      marginBottom: 24,
    },
    sectionTitle: {
      ...ExpressiveTypography.headlineSmall,
      color: theme.colors.onBackground,
      marginBottom: 16,
    },
    cardGrid: {
      gap: 16,
    },
    quickActions: {
      flexDirection: 'row',
      gap: 12,
      flexWrap: 'wrap',
      marginTop: 16,
    },
  });

  const handleStartTimer = () => {
    navigation.navigate('Timer');
  };

  const handleViewAnalytics = () => {
    navigation.navigate('Analytics');
  };

  const handleManageTasks = () => {
    navigation.navigate('Tasks');
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Welcome back!</Text>
          <Text style={styles.subtitle}>
            Ready to boost your productivity? Start a focus session or check your progress.
          </Text>
        </View>

        {/* Quick Stats */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Today's Progress</Text>
          <View style={styles.cardGrid}>
            <ExpressiveCard
              title="Focus Time"
              subtitle="2h 30m completed"
              description="Great job! You're 15 minutes ahead of yesterday."
              variant="elevated"
              elevation={2}
              colorVariant="primary"
            />
            
            <ExpressiveCard
              title="Tasks Completed"
              subtitle="8 out of 12 tasks"
              description="You're making excellent progress today."
              variant="filled"
              colorVariant="success"
            />
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <ExpressiveCard
            title="Start Your Day"
            subtitle="Begin with a focused work session"
            variant="outlined"
          >
            <View style={styles.quickActions}>
              <ExpressiveButton
                title="Pomodoro Timer"
                variant="filled"
                icon={<Text style={{ color: theme.colors.onPrimary }}>🍅</Text>}
                onPress={handleStartTimer}
              />
              <ExpressiveButton
                title="View Analytics"
                variant="outlined"
                icon={<Text style={{ color: theme.colors.primary }}>📊</Text>}
                onPress={handleViewAnalytics}
              />
              <ExpressiveButton
                title="Manage Tasks"
                variant="tonal"
                icon={<Text style={{ color: theme.colors.onSecondaryContainer }}>📋</Text>}
                onPress={handleManageTasks}
              />
            </View>
          </ExpressiveCard>
        </View>

        {/* Recent Activity */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>
          <View style={styles.cardGrid}>
            <ExpressiveCard
              title="Mathematics Study"
              subtitle="45 minutes • 2 hours ago"
              description="Completed calculus practice problems"
              variant="elevated"
              elevation={1}
            />
            
            <ExpressiveCard
              title="Project Planning"
              subtitle="25 minutes • 4 hours ago"
              description="Organized tasks for the week"
              variant="elevated"
              elevation={1}
            />
          </View>
        </View>
      </ScrollView>

      {/* Floating Action Button */}
      <ExpressiveFAB
        icon={<Text style={{ fontSize: 24, color: theme.colors.onPrimaryContainer }}>▶️</Text>}
        label="Start Timer"
        extended={true}
        position="bottomRight"
        onPress={handleStartTimer}
      />
    </View>
  );
};

export default HomeScreen;
