import { useState } from 'react';
import {
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  IconButton,
  useTheme,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuthStore } from '@/stores/authStore';

interface ForgotPasswordScreenProps {
  navigation: any; // Will be properly typed later with navigation types
}

export default function ForgotPasswordScreen({ navigation }: ForgotPasswordScreenProps) {
  const theme = useTheme();
  const { resetPassword, isLoading, error, clearError } = useAuthStore();
  
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  const [isEmailSent, setIsEmailSent] = useState(false);

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleResetPassword = async () => {
    // Clear previous errors
    setEmailError('');
    clearError();

    // Validation
    if (!email.trim()) {
      setEmailError('Email is required');
      return;
    }

    if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      return;
    }

    try {
      await resetPassword(email.trim());
      setIsEmailSent(true);
    } catch (error) {
      // Error is already set in the store
      console.error('Reset password error:', error);
    }
  };

  const navigateToLogin = () => {
    navigation.navigate('Login');
  };

  const handleBackPress = () => {
    navigation.goBack();
  };

  if (isEmailSent) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.content}>
          <View style={styles.header}>
            <IconButton
              icon="check-circle"
              size={64}
              iconColor={theme.colors.primary}
              style={styles.successIcon}
            />
            <Text variant="headlineMedium" style={[styles.title, { color: theme.colors.onBackground }]}>
              Check Your Email
            </Text>
            <Text variant="bodyLarge" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
              We've sent a password reset link to{'\n'}
              <Text style={{ color: theme.colors.primary, fontWeight: '600' }}>{email}</Text>
            </Text>
            <Text variant="bodyMedium" style={[styles.instructions, { color: theme.colors.onSurfaceVariant }]}>
              Click the link in the email to reset your password. If you don't see the email, check your spam folder.
            </Text>
          </View>

          <View style={styles.actions}>
            <Button
              mode="contained"
              onPress={navigateToLogin}
              style={styles.backToLoginButton}
            >
              Back to Sign In
            </Button>
            
            <Button
              mode="text"
              onPress={() => setIsEmailSent(false)}
              style={styles.resendButton}
            >
              Try Different Email
            </Button>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.content}>
            {/* Back Button */}
            <View style={styles.backButtonContainer}>
              <IconButton
                icon="arrow-left"
                size={24}
                onPress={handleBackPress}
                style={styles.backButton}
              />
            </View>

            {/* Header */}
            <View style={styles.header}>
              <Text variant="headlineLarge" style={[styles.title, { color: theme.colors.onBackground }]}>
                Reset Password
              </Text>
              <Text variant="bodyLarge" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
                Enter your email address and we'll send you a link to reset your password
              </Text>
            </View>

            {/* Reset Form */}
            <Card style={styles.card}>
              <Card.Content style={styles.cardContent}>
                {/* Email Input */}
                <TextInput
                  label="Email"
                  value={email}
                  onChangeText={setEmail}
                  mode="outlined"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoComplete="email"
                  error={!!emailError}
                  style={styles.input}
                />
                {emailError ? (
                  <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
                    {emailError}
                  </Text>
                ) : null}

                {/* General Error */}
                {error ? (
                  <Text variant="bodySmall" style={[styles.errorText, { color: theme.colors.error }]}>
                    {error}
                  </Text>
                ) : null}

                {/* Reset Button */}
                <Button
                  mode="contained"
                  onPress={handleResetPassword}
                  loading={isLoading}
                  disabled={isLoading}
                  style={styles.resetButton}
                >
                  Send Reset Link
                </Button>
              </Card.Content>
            </Card>

            {/* Back to Login Link */}
            <View style={styles.backToLoginContainer}>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                Remember your password?{' '}
              </Text>
              <Button mode="text" onPress={navigateToLogin} compact>
                Sign In
              </Button>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 16,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    maxWidth: 400,
    alignSelf: 'center',
    width: '100%',
  },
  backButtonContainer: {
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  backButton: {
    margin: 0,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    textAlign: 'center',
    lineHeight: 24,
  },
  instructions: {
    textAlign: 'center',
    lineHeight: 20,
    marginTop: 16,
  },
  successIcon: {
    marginBottom: 16,
  },
  card: {
    marginBottom: 24,
  },
  cardContent: {
    padding: 24,
  },
  input: {
    marginBottom: 8,
  },
  errorText: {
    marginBottom: 16,
    marginLeft: 16,
  },
  resetButton: {
    marginTop: 8,
  },
  actions: {
    marginTop: 32,
  },
  backToLoginButton: {
    marginBottom: 16,
  },
  resendButton: {
    alignSelf: 'center',
  },
  backToLoginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
