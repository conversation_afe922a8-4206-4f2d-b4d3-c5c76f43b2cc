import { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Avatar,
  Button,
  TextInput,
  Divider,
  List,
  Switch,
  useTheme,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuthStore } from '@/stores/authStore';

interface ProfileScreenProps {
  navigation: any; // Will be properly typed later with navigation types
}

export default function ProfileScreen({ navigation }: ProfileScreenProps) {
  const theme = useTheme();
  const { user, updateProfile, signOut, isLoading } = useAuthStore();
  
  const [fullName, setFullName] = useState(user?.user_metadata?.full_name || '');
  const [dailyTargetHours, setDailyTargetHours] = useState('8');
  const [dailyTargetMinutes, setDailyTargetMinutes] = useState('0');
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [darkModeEnabled, setDarkModeEnabled] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (user?.user_metadata?.full_name) {
      setFullName(user.user_metadata.full_name);
    }
    // Convert daily target from seconds to hours and minutes
    const dailyTargetSeconds = user?.user_metadata?.daily_target_seconds || 28800; // Default 8 hours
    const hours = Math.floor(dailyTargetSeconds / 3600);
    const minutes = Math.floor((dailyTargetSeconds % 3600) / 60);
    setDailyTargetHours(hours.toString());
    setDailyTargetMinutes(minutes.toString());
  }, [user]);

  const handleSaveProfile = async () => {
    try {
      setIsSaving(true);
      
      // Convert hours and minutes to seconds
      const hours = parseInt(dailyTargetHours) || 0;
      const minutes = parseInt(dailyTargetMinutes) || 0;
      const dailyTargetSeconds = (hours * 3600) + (minutes * 60);

      await updateProfile({
        full_name: fullName.trim(),
        daily_target_seconds: dailyTargetSeconds,
      });

      setIsEditing(false);
      Alert.alert('Success', 'Profile updated successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to update profile');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            try {
              await signOut();
            } catch (error) {
              Alert.alert('Error', 'Failed to sign out');
            }
          },
        },
      ]
    );
  };

  const navigateToSubjects = () => {
    navigation.navigate('Subjects');
  };

  const navigateToAnalytics = () => {
    navigation.navigate('Analytics');
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Profile Header */}
        <Card style={styles.profileCard}>
          <Card.Content style={styles.profileContent}>
            <View style={styles.avatarContainer}>
              <Avatar.Text
                size={80}
                label={fullName ? fullName.charAt(0).toUpperCase() : 'U'}
                style={{ backgroundColor: theme.colors.primary }}
              />
              <Button
                mode="text"
                onPress={() => {/* TODO: Implement avatar change */}}
                style={styles.changeAvatarButton}
              >
                Change Photo
              </Button>
            </View>
            
            <View style={styles.userInfo}>
              <Text variant="headlineSmall" style={{ color: theme.colors.onSurface }}>
                {fullName || 'User'}
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                {user?.email}
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Profile Settings */}
        <Card style={styles.settingsCard}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface }}>
                Profile Information
              </Text>
              <Button
                mode="text"
                onPress={() => setIsEditing(!isEditing)}
                disabled={isSaving}
              >
                {isEditing ? 'Cancel' : 'Edit'}
              </Button>
            </View>

            {/* Full Name */}
            <TextInput
              label="Full Name"
              value={fullName}
              onChangeText={setFullName}
              mode="outlined"
              disabled={!isEditing}
              style={styles.input}
            />

            {/* Daily Target */}
            <Text variant="bodyMedium" style={[styles.label, { color: theme.colors.onSurfaceVariant }]}>
              Daily Study Target
            </Text>
            <View style={styles.timeInputContainer}>
              <TextInput
                label="Hours"
                value={dailyTargetHours}
                onChangeText={setDailyTargetHours}
                mode="outlined"
                keyboardType="numeric"
                disabled={!isEditing}
                style={styles.timeInput}
              />
              <TextInput
                label="Minutes"
                value={dailyTargetMinutes}
                onChangeText={setDailyTargetMinutes}
                mode="outlined"
                keyboardType="numeric"
                disabled={!isEditing}
                style={styles.timeInput}
              />
            </View>

            {isEditing && (
              <Button
                mode="contained"
                onPress={handleSaveProfile}
                loading={isSaving}
                disabled={isSaving}
                style={styles.saveButton}
              >
                Save Changes
              </Button>
            )}
          </Card.Content>
        </Card>

        {/* App Settings */}
        <Card style={styles.settingsCard}>
          <Card.Content>
            <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              App Settings
            </Text>

            <List.Item
              title="Notifications"
              description="Receive study reminders and updates"
              left={(props) => <List.Icon {...props} icon="bell" />}
              right={() => (
                <Switch
                  value={notificationsEnabled}
                  onValueChange={setNotificationsEnabled}
                />
              )}
            />

            <Divider />

            <List.Item
              title="Dark Mode"
              description="Use dark theme"
              left={(props) => <List.Icon {...props} icon="theme-light-dark" />}
              right={() => (
                <Switch
                  value={darkModeEnabled}
                  onValueChange={setDarkModeEnabled}
                />
              )}
            />
          </Card.Content>
        </Card>

        {/* Quick Actions */}
        <Card style={styles.settingsCard}>
          <Card.Content>
            <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Quick Actions
            </Text>

            <List.Item
              title="Manage Subjects"
              description="Add, edit, or remove study subjects"
              left={(props) => <List.Icon {...props} icon="book-open-variant" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={navigateToSubjects}
            />

            <Divider />

            <List.Item
              title="Study Analytics"
              description="View your study progress and statistics"
              left={(props) => <List.Icon {...props} icon="chart-line" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={navigateToAnalytics}
            />

            <Divider />

            <List.Item
              title="Export Data"
              description="Download your study data"
              left={(props) => <List.Icon {...props} icon="download" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => {/* TODO: Implement data export */}}
            />
          </Card.Content>
        </Card>

        {/* Sign Out */}
        <Card style={styles.settingsCard}>
          <Card.Content>
            <Button
              mode="outlined"
              onPress={handleSignOut}
              disabled={isLoading}
              style={styles.signOutButton}
              textColor={theme.colors.error}
            >
              Sign Out
            </Button>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  profileCard: {
    marginBottom: 16,
  },
  profileContent: {
    alignItems: 'center',
    padding: 24,
  },
  avatarContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  changeAvatarButton: {
    marginTop: 8,
  },
  userInfo: {
    alignItems: 'center',
  },
  settingsCard: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  input: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 8,
    marginLeft: 16,
  },
  timeInputContainer: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 16,
  },
  timeInput: {
    flex: 1,
  },
  saveButton: {
    marginTop: 8,
  },
  signOutButton: {
    borderColor: 'transparent',
  },
});
