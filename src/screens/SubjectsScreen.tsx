import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Dimensions,
  StatusBar,
  Alert,
} from 'react-native';
import {
  Text,
  Surface,
  Searchbar,
  FAB,
  IconButton,
  Menu,
  Chip,
  Portal,
  Modal,
} from 'react-native-paper';
import { FlatGrid } from 'react-native-super-grid';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useDynamicTheme } from '../contexts/DynamicThemeProvider';
import { useSubjectStore, Subject } from '../stores/subjectStore';
import { SubjectCard, SubjectCreationModal } from '../components/subjects';
import { DEFAULT_SUBJECT_COLORS, COLOR_PALETTES } from '../constants/colorPalette';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export const SubjectsScreen: React.FC = () => {
  const theme = useDynamicTheme();
  const insets = useSafeAreaInsets();
  
  const {
    subjects,
    searchQuery,
    sortBy,
    sortOrder,
    colorFilter,
    isLoading,
    error,
    setSearchQuery,
    setSortBy,
    setSortOrder,
    setColorFilter,
    clearFilters,
    getFilteredSubjects,
    getFavoriteSubjects,
    getRecentSubjects,
    loadSubjects,
    deleteSubject,
    updateSubject,
  } = useSubjectStore();

  // Local state
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSortMenu, setShowSortMenu] = useState(false);
  const [selectedSubject, setSelectedSubject] = useState<Subject | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Load subjects on mount
  useEffect(() => {
    loadSubjects();
  }, [loadSubjects]);

  // Handle subject creation success
  const handleCreateSuccess = (subject: Subject) => {
    setShowCreateModal(false);
    // Optionally show success message or navigate
  };

  // Handle subject edit
  const handleEditSubject = (subject: Subject) => {
    setSelectedSubject(subject);
    setShowEditModal(true);
  };

  // Handle subject delete
  const handleDeleteSubject = (subject: Subject) => {
    Alert.alert(
      'Delete Subject',
      `Are you sure you want to delete "${subject.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const success = await deleteSubject(subject.id);
            if (!success) {
              Alert.alert('Error', 'Failed to delete subject. Please try again.');
            }
          },
        },
      ]
    );
  };

  // Handle sort option selection
  const handleSortSelect = (newSortBy: typeof sortBy) => {
    if (sortBy === newSortBy) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(newSortBy);
      setSortOrder('asc');
    }
    setShowSortMenu(false);
  };

  // Handle color filter
  const handleColorFilter = (color: string | null) => {
    setColorFilter(colorFilter === color ? null : color);
  };

  // Get filtered and sorted subjects
  const filteredSubjects = getFilteredSubjects();
  const favoriteSubjects = getFavoriteSubjects();
  const recentSubjects = getRecentSubjects();

  // Render subject card
  const renderSubjectCard = ({ item }: { item: Subject }) => (
    <SubjectCard
      subject={item}
      onPress={() => {
        // Handle subject selection - could navigate to timer with this subject
        console.log('Selected subject:', item.name);
      }}
      onEdit={handleEditSubject}
      onDelete={handleDeleteSubject}
      showStats={true}
      showProgress={true}
      compact={viewMode === 'list'}
    />
  );

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyTitle}>No Subjects Yet</Text>
      <Text style={styles.emptyDescription}>
        Create your first subject to start organizing your study sessions
      </Text>
      <FAB
        icon="plus"
        label="Create Subject"
        onPress={() => setShowCreateModal(true)}
        style={styles.emptyFab}
      />
    </View>
  );

  // Render color filter chips
  const renderColorFilters = () => (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.colorFilters}
      contentContainerStyle={styles.colorFiltersContent}
    >
      <Chip
        mode={colorFilter === null ? 'flat' : 'outlined'}
        selected={colorFilter === null}
        onPress={() => handleColorFilter(null)}
        style={styles.filterChip}
      >
        All Colors
      </Chip>
      {DEFAULT_SUBJECT_COLORS.map((color) => (
        <Chip
          key={color}
          mode={colorFilter === color ? 'flat' : 'outlined'}
          selected={colorFilter === color}
          onPress={() => handleColorFilter(color)}
          style={[
            styles.filterChip,
            { backgroundColor: colorFilter === color ? `${color}20` : 'transparent' },
          ]}
          textStyle={{ color: colorFilter === color ? color : theme.colors.onSurface }}
        >
          <View
            style={[
              styles.colorChipIndicator,
              { backgroundColor: color },
            ]}
          />
        </Chip>
      ))}
    </ScrollView>
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    statusBar: {
      height: insets.top,
      backgroundColor: theme.colors.surface,
    },
    header: {
      backgroundColor: theme.colors.surface,
      paddingHorizontal: 20,
      paddingVertical: 16,
      elevation: 2,
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: '700',
      color: theme.colors.onSurface,
      textAlign: 'center',
      marginBottom: 16,
    },
    headerActions: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 16,
    },
    searchContainer: {
      flex: 1,
      marginRight: 8,
    },
    searchbar: {
      backgroundColor: theme.colors.surfaceVariant,
      elevation: 0,
    },
    headerButtons: {
      flexDirection: 'row',
      gap: 4,
    },
    colorFilters: {
      marginTop: 12,
    },
    colorFiltersContent: {
      paddingHorizontal: 20,
      gap: 8,
    },
    filterChip: {
      marginRight: 8,
    },
    colorChipIndicator: {
      width: 12,
      height: 12,
      borderRadius: 6,
      marginRight: 4,
    },
    content: {
      flex: 1,
      padding: 16,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.onSurface,
      marginBottom: 12,
      marginTop: 16,
    },
    grid: {
      flex: 1,
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32,
    },
    emptyTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: theme.colors.onSurface,
      marginBottom: 8,
    },
    emptyDescription: {
      fontSize: 16,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginBottom: 24,
    },
    emptyFab: {
      backgroundColor: theme.colors.primaryContainer,
    },
    fab: {
      position: 'absolute',
      bottom: 16 + insets.bottom,
      right: 16,
      backgroundColor: theme.colors.primaryContainer,
    },
    statsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      padding: 16,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 12,
      marginBottom: 16,
    },
    statItem: {
      alignItems: 'center',
    },
    statValue: {
      fontSize: 20,
      fontWeight: '600',
      color: theme.colors.onSurfaceVariant,
    },
    statLabel: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
      marginTop: 4,
    },
    errorContainer: {
      padding: 16,
      backgroundColor: theme.colors.errorContainer,
      borderRadius: 12,
      margin: 16,
    },
    errorText: {
      color: theme.colors.onErrorContainer,
      textAlign: 'center',
    },
  });

  return (
    <View style={styles.container}>
      {/* Status Bar */}
      <View style={styles.statusBar} />
      <StatusBar
        backgroundColor={theme.colors.surface}
        barStyle={theme.isDark ? 'light-content' : 'dark-content'}
      />

      {/* Header */}
      <Surface style={styles.header} elevation={2}>
        <Text style={styles.headerTitle}>My Subjects</Text>
        
        {/* Search and Actions */}
        <View style={styles.headerActions}>
          <View style={styles.searchContainer}>
            <Searchbar
              placeholder="Search subjects..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              style={styles.searchbar}
              elevation={0}
            />
          </View>
          
          <View style={styles.headerButtons}>
            <Menu
              visible={showSortMenu}
              onDismiss={() => setShowSortMenu(false)}
              anchor={
                <IconButton
                  icon="sort"
                  onPress={() => setShowSortMenu(true)}
                />
              }
            >
              <Menu.Item
                onPress={() => handleSortSelect('name')}
                title="Name"
                leadingIcon={sortBy === 'name' ? (sortOrder === 'asc' ? 'sort-alphabetical-ascending' : 'sort-alphabetical-descending') : 'sort-alphabetical-ascending'}
              />
              <Menu.Item
                onPress={() => handleSortSelect('created_at')}
                title="Date Created"
                leadingIcon={sortBy === 'created_at' ? (sortOrder === 'asc' ? 'sort-calendar-ascending' : 'sort-calendar-descending') : 'sort-calendar-ascending'}
              />
              <Menu.Item
                onPress={() => handleSortSelect('totalTime')}
                title="Study Time"
                leadingIcon={sortBy === 'totalTime' ? (sortOrder === 'asc' ? 'sort-numeric-ascending' : 'sort-numeric-descending') : 'sort-numeric-ascending'}
              />
              <Menu.Item
                onPress={() => handleSortSelect('lastStudied')}
                title="Last Studied"
                leadingIcon={sortBy === 'lastStudied' ? (sortOrder === 'asc' ? 'sort-clock-ascending' : 'sort-clock-descending') : 'sort-clock-ascending'}
              />
            </Menu>
            
            <IconButton
              icon={viewMode === 'grid' ? 'view-list' : 'view-grid'}
              onPress={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
            />
          </View>
        </View>

        {/* Color Filters */}
        {renderColorFilters()}
      </Surface>

      {/* Content */}
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      {subjects.length > 0 && (
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{subjects.length}</Text>
            <Text style={styles.statLabel}>Total Subjects</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{favoriteSubjects.length}</Text>
            <Text style={styles.statLabel}>Active</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{recentSubjects.length}</Text>
            <Text style={styles.statLabel}>Recent</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {subjects.reduce((total, subject) => total + (subject.todayTime || 0), 0)}s
            </Text>
            <Text style={styles.statLabel}>Today (seconds)</Text>
          </View>
        </View>
      )}

      {filteredSubjects.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatGrid
          itemDimension={viewMode === 'grid' ? 160 : screenWidth - 32}
          data={filteredSubjects}
          style={styles.grid}
          spacing={16}
          renderItem={renderSubjectCard}
          maxItemsPerRow={viewMode === 'grid' ? 2 : 1}
          staticDimension={screenWidth}
          fixed={false}
          maxDimension={screenWidth}
        />
      )}

      {/* Create Subject FAB */}
      {subjects.length > 0 && (
        <FAB
          icon="plus"
          onPress={() => setShowCreateModal(true)}
          style={styles.fab}
          variant="primary"
        />
      )}

      {/* Create Subject Modal */}
      <SubjectCreationModal
        visible={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={handleCreateSuccess}
      />

      {/* Edit Subject Modal */}
      <SubjectCreationModal
        visible={showEditModal}
        onClose={() => setShowEditModal(false)}
        onSuccess={() => setShowEditModal(false)}
        initialName={selectedSubject?.name}
        initialColor={selectedSubject?.color}
      />
    </View>
  );
};

export default SubjectsScreen;
