import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  StatusBar,
} from 'react-native';
import { useDynamicTheme } from '../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../constants/expressiveTheme';
import { useTasksStore, Task } from '../stores/tasksStore';
import {
  KanbanBoard,
  TaskTable,
  TaskCreationModal,
} from '../components/tasks';
import ExpressiveFAB from '../components/buttons/ExpressiveFAB';

interface TasksScreenProps {
  groupId?: string;
}

export const TasksScreen: React.FC<TasksScreenProps> = ({ groupId }) => {
  const theme = useDynamicTheme();
  const {
    viewMode,
    setViewMode,
    error,
    loadTasks,
    subscribeToTasks,
    unsubscribeFromTasks,
    isSubscribed,
  } = useTasksStore();

  // Local state
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [selectedColumnId, setSelectedColumnId] = useState('todo');
  const [refreshing, setRefreshing] = useState(false);

  // Initialize data and subscriptions
  useEffect(() => {
    const initializeData = async () => {
      try {
        await loadTasks(groupId);

        // Setup real-time subscriptions
        if (!isSubscribed) {
          await subscribeToTasks(groupId);
        }
      } catch (error) {
        console.error('Failed to initialize tasks:', error);
      }
    };

    initializeData();

    // Cleanup subscriptions on unmount
    return () => {
      unsubscribeFromTasks();
    };
  }, [groupId, loadTasks, subscribeToTasks, unsubscribeFromTasks, isSubscribed]);

  // Handle view mode toggle
  const handleViewModeToggle = useCallback((mode: 'kanban' | 'table') => {
    setViewMode(mode);
  }, [setViewMode]);

  // Handle task press
  const handleTaskPress = useCallback((task: Task) => {
    // TODO: Navigate to task detail screen or open edit modal
    Alert.alert(
      task.title,
      task.description || 'No description',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Edit', onPress: () => console.log('Edit task:', task.id) },
      ]
    );
  }, []);

  // Handle create task
  const handleCreateTask = useCallback((columnId?: string) => {
    setSelectedColumnId(columnId || 'todo');
    setIsCreateModalVisible(true);
  }, []);

  // Handle task created
  const handleTaskCreated = useCallback((task: Task) => {
    console.log('Task created:', task);
    // Task will be automatically added to the store via the service
  }, []);

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await loadTasks(groupId);
    } catch (error) {
      console.error('Failed to refresh tasks:', error);
      Alert.alert('Error', 'Failed to refresh tasks. Please try again.');
    } finally {
      setRefreshing(false);
    }
  }, [loadTasks, groupId]);

  // Handle retry
  const handleRetry = useCallback(async () => {
    await handleRefresh();
  }, [handleRefresh]);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.surface,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
    },
    headerTitle: {
      ...ExpressiveTypography.headlineSmall,
      color: theme.colors.onSurface,
    },
    viewToggle: {
      flexDirection: 'row',
      backgroundColor: theme.colors.surfaceContainer,
      borderRadius: 20,
      padding: 2,
    },
    viewToggleButton: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 18,
      minWidth: 80,
      alignItems: 'center',
    },
    activeViewToggleButton: {
      backgroundColor: theme.colors.primary,
    },
    viewToggleText: {
      ...ExpressiveTypography.labelMedium,
      color: theme.colors.onSurfaceVariant,
    },
    activeViewToggleText: {
      color: theme.colors.onPrimary,
      fontWeight: '600',
    },
    content: {
      flex: 1,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32,
    },
    errorText: {
      ...ExpressiveTypography.bodyLarge,
      color: theme.colors.error,
      textAlign: 'center',
      marginBottom: 16,
    },
    retryButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 20,
    },
    retryButtonText: {
      ...ExpressiveTypography.labelLarge,
      color: theme.colors.onPrimary,
      fontWeight: '600',
    },
    fab: {
      position: 'absolute',
      bottom: 24,
      right: 24,
    },
  });

  // Render error state
  if (error && !refreshing) {
    return (
      <View style={styles.container}>
        <StatusBar
          barStyle="dark-content"
          backgroundColor={theme.colors.surface}
        />
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Tasks</Text>
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={theme.colors.surface}
      />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {groupId ? 'Group Tasks' : 'My Tasks'}
        </Text>

        {/* View Toggle */}
        <View style={styles.viewToggle}>
          <TouchableOpacity
            style={[
              styles.viewToggleButton,
              viewMode === 'kanban' && styles.activeViewToggleButton,
            ]}
            onPress={() => handleViewModeToggle('kanban')}
          >
            <Text
              style={[
                styles.viewToggleText,
                viewMode === 'kanban' && styles.activeViewToggleText,
              ]}
            >
              Board
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.viewToggleButton,
              viewMode === 'table' && styles.activeViewToggleButton,
            ]}
            onPress={() => handleViewModeToggle('table')}
          >
            <Text
              style={[
                styles.viewToggleText,
                viewMode === 'table' && styles.activeViewToggleText,
              ]}
            >
              List
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {viewMode === 'kanban' ? (
          <KanbanBoard
            groupId={groupId}
            onTaskPress={handleTaskPress}
            onCreateTask={handleCreateTask}
            refreshing={refreshing}
            onRefresh={handleRefresh}
          />
        ) : (
          <TaskTable
            groupId={groupId}
            onTaskPress={handleTaskPress}
            onCreateTask={() => handleCreateTask()}
            refreshing={refreshing}
            onRefresh={handleRefresh}
          />
        )}
      </View>

      {/* Floating Action Button */}
      <View style={styles.fab}>
        <ExpressiveFAB
          icon="+"
          onPress={() => handleCreateTask()}
          size="large"
          variant="primary"
        />
      </View>

      {/* Task Creation Modal */}
      <TaskCreationModal
        visible={isCreateModalVisible}
        onClose={() => setIsCreateModalVisible(false)}
        initialColumnId={selectedColumnId}
        groupId={groupId}
        onTaskCreated={handleTaskCreated}
      />
    </View>
  );
};

export default TasksScreen;
