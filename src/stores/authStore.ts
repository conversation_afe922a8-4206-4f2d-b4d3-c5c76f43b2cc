import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { User, Session, AuthError } from '@supabase/supabase-js';
import { supabase, getCurrentUser, getCurrentSession, signOut } from '../services/supabase/client';
import { signInWithGoogle as googleSignIn, configureGoogleSignIn, signOutFromGoogle } from '../services/auth/googleAuth';

// Types for the auth store
interface AuthState {
  // State
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
  
  // Actions
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, fullName?: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updateProfile: (updates: { full_name?: string; avatar_url?: string; daily_target_seconds?: number }) => Promise<void>;
  
  // Internal actions
  setUser: (user: User | null) => void;
  setSession: (session: Session | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  initialize: () => Promise<void>;
  clearError: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      // Initial state
      user: null,
      session: null,
      isLoading: false,
      isAuthenticated: false,
      error: null,

      // Actions
      signIn: async (email: string, password: string) => {
        try {
          set({ isLoading: true, error: null });
          
          const { data, error } = await supabase.auth.signInWithPassword({
            email,
            password,
          });

          if (error) throw error;

          set({
            user: data.user,
            session: data.session,
            isAuthenticated: !!data.user,
            isLoading: false,
          });
        } catch (error) {
          const authError = error as AuthError;
          set({
            error: authError.message,
            isLoading: false,
            isAuthenticated: false,
          });
          throw error;
        }
      },

      signUp: async (email: string, password: string, fullName?: string) => {
        try {
          set({ isLoading: true, error: null });
          
          const { data, error } = await supabase.auth.signUp({
            email,
            password,
            options: {
              data: {
                full_name: fullName,
              },
            },
          });

          if (error) throw error;

          // If user is created but not confirmed, show appropriate message
          if (data.user && !data.session) {
            set({
              error: 'Please check your email to confirm your account',
              isLoading: false,
            });
          } else {
            set({
              user: data.user,
              session: data.session,
              isAuthenticated: !!data.user,
              isLoading: false,
            });
          }
        } catch (error) {
          const authError = error as AuthError;
          set({
            error: authError.message,
            isLoading: false,
            isAuthenticated: false,
          });
          throw error;
        }
      },

      signInWithGoogle: async () => {
        try {
          set({ isLoading: true, error: null });

          const { user, session } = await googleSignIn();

          set({
            user,
            session,
            isAuthenticated: !!user,
            isLoading: false,
          });
        } catch (error) {
          const authError = error as Error;
          set({
            error: authError.message,
            isLoading: false,
            isAuthenticated: false,
          });
          throw error;
        }
      },

      signOut: async () => {
        try {
          set({ isLoading: true, error: null });

          // Sign out from both Supabase and Google
          await Promise.all([
            signOut(),
            signOutFromGoogle(),
          ]);

          set({
            user: null,
            session: null,
            isAuthenticated: false,
            isLoading: false,
          });
        } catch (error) {
          const authError = error as AuthError;
          set({
            error: authError.message,
            isLoading: false,
          });
          throw error;
        }
      },

      resetPassword: async (email: string) => {
        try {
          set({ isLoading: true, error: null });
          
          const { error } = await supabase.auth.resetPasswordForEmail(email, {
            redirectTo: 'isotope-ai://auth/reset-password',
          });

          if (error) throw error;

          set({
            error: 'Password reset email sent. Please check your inbox.',
            isLoading: false,
          });
        } catch (error) {
          const authError = error as AuthError;
          set({
            error: authError.message,
            isLoading: false,
          });
          throw error;
        }
      },

      updateProfile: async (updates) => {
        try {
          set({ isLoading: true, error: null });
          
          const { data, error } = await supabase.auth.updateUser({
            data: updates,
          });

          if (error) throw error;

          set({
            user: data.user,
            isLoading: false,
          });
        } catch (error) {
          const authError = error as AuthError;
          set({
            error: authError.message,
            isLoading: false,
          });
          throw error;
        }
      },

      // Internal actions
      setUser: (user) => set({ user, isAuthenticated: !!user }),
      setSession: (session) => set({ session }),
      setLoading: (isLoading) => set({ isLoading }),
      setError: (error) => set({ error }),
      clearError: () => set({ error: null }),

      initialize: async () => {
        try {
          set({ isLoading: true });

          // Configure Google Sign-In
          configureGoogleSignIn();

          // Get current session
          const session = await getCurrentSession();
          const user = await getCurrentUser();

          set({
            user,
            session,
            isAuthenticated: !!user,
            isLoading: false,
          });

          // Listen for auth changes
          supabase.auth.onAuthStateChange((_event, session) => {
            set({
              user: session?.user ?? null,
              session,
              isAuthenticated: !!session?.user,
            });
          });
        } catch (error) {
          console.error('Error initializing auth:', error);
          set({
            isLoading: false,
            error: 'Failed to initialize authentication',
          });
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        session: state.session,
        isAuthenticated: state.isAuthenticated,
      }),
    },
  ),
);
