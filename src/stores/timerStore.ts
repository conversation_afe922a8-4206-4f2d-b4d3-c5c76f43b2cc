import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

export type TimerMode = 'pomodoro' | 'stopwatch';
export type TimerPhase = 'work' | 'shortBreak' | 'longBreak' | 'pause';
export type TimerStatus = 'idle' | 'running' | 'paused' | 'completed';

export interface TimerSession {
  id: string;
  subject: string;
  taskName?: string;
  startTime: Date;
  endTime?: Date;
  duration: number;
  mode: TimerMode;
  phase: TimerPhase;
  completed: boolean;
  notes?: string;
  productivityRating?: number;
}

export interface TimerState {
  // Core timer state
  status: TimerStatus;
  mode: TimerMode;
  currentPhase: TimerPhase;
  displayTime: number; // in seconds
  elapsedTime: number; // in seconds
  
  // Session management
  selectedSubject: string | null;
  selectedSubjectColor: string | null;
  currentTaskName: string | null;
  sessionStartTime: Date | null;
  completedSessions: TimerSession[];
  currentSessionId: string | null;
  
  // Pomodoro specific
  pomodoroCount: number;
  workDuration: number; // 25 minutes default
  shortBreakDuration: number; // 5 minutes default
  longBreakDuration: number; // 15 minutes default
  longBreakInterval: number; // after 4 pomodoros
  
  // Stopwatch specific
  notificationIntervals: number[]; // custom intervals in minutes
  lastNotificationTime: number;
  
  // Settings
  autoStartBreaks: boolean;
  autoStartPomodoros: boolean;
  soundEnabled: boolean;
  vibrationEnabled: boolean;
  
  // Actions
  setMode: (mode: TimerMode) => void;
  setSubject: (subject: string, color?: string) => void;
  setTaskName: (taskName: string) => void;
  startTimer: () => void;
  pauseTimer: () => void;
  stopTimer: () => void;
  resetTimer: () => void;
  completeSession: (notes?: string, rating?: number) => void;
  skipPhase: () => void;
  updateDisplayTime: (time: number) => void;
  updateElapsedTime: (time: number) => void;
  
  // Pomodoro actions
  startWork: () => void;
  startShortBreak: () => void;
  startLongBreak: () => void;
  
  // Settings actions
  updateSettings: (settings: Partial<Pick<TimerState, 'workDuration' | 'shortBreakDuration' | 'longBreakDuration' | 'longBreakInterval' | 'autoStartBreaks' | 'autoStartPomodoros' | 'soundEnabled' | 'vibrationEnabled'>>) => void;
  
  // Persistence
  loadPersistedState: () => Promise<void>;
  persistState: () => Promise<void>;
  clearPersistedState: () => Promise<void>;
}

const TIMER_STORAGE_KEY = 'timer_state';

const getInitialPhaseTime = (phase: TimerPhase, state: Partial<TimerState>): number => {
  switch (phase) {
    case 'work':
      return (state.workDuration || 25) * 60;
    case 'shortBreak':
      return (state.shortBreakDuration || 5) * 60;
    case 'longBreak':
      return (state.longBreakDuration || 15) * 60;
    case 'pause':
      return 0;
    default:
      return 25 * 60;
  }
};

export const useTimerStore = create<TimerState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    status: 'idle',
    mode: 'pomodoro',
    currentPhase: 'work',
    displayTime: 25 * 60, // 25 minutes in seconds
    elapsedTime: 0,
    
    selectedSubject: null,
    selectedSubjectColor: null,
    currentTaskName: null,
    sessionStartTime: null,
    completedSessions: [],
    currentSessionId: null,
    
    pomodoroCount: 0,
    workDuration: 25,
    shortBreakDuration: 5,
    longBreakDuration: 15,
    longBreakInterval: 4,
    
    notificationIntervals: [15, 30, 45, 60], // default intervals
    lastNotificationTime: 0,
    
    autoStartBreaks: false,
    autoStartPomodoros: false,
    soundEnabled: true,
    vibrationEnabled: true,
    
    // Actions
    setMode: (mode) => {
      set((state) => {
        const newPhase = mode === 'pomodoro' ? 'work' : 'pause';
        const newDisplayTime = mode === 'pomodoro' 
          ? getInitialPhaseTime(newPhase, state)
          : 0;
        
        return {
          mode,
          currentPhase: newPhase,
          displayTime: newDisplayTime,
          elapsedTime: 0,
          status: 'idle',
          pomodoroCount: mode === 'pomodoro' ? state.pomodoroCount : 0,
        };
      });
    },
    
    setSubject: (subject, color) => set({
      selectedSubject: subject,
      selectedSubjectColor: color || null
    }),
    setTaskName: (taskName) => set({ currentTaskName: taskName }),
    
    startTimer: () => {
      set((state) => ({
        status: 'running',
        sessionStartTime: state.sessionStartTime || new Date(),
        currentSessionId: state.currentSessionId || `session_${Date.now()}`,
      }));
    },
    
    pauseTimer: () => set({ status: 'paused' }),
    
    stopTimer: () => {
      set((state) => ({
        status: 'idle',
        displayTime: state.mode === 'pomodoro' 
          ? getInitialPhaseTime(state.currentPhase, state)
          : 0,
        elapsedTime: 0,
        sessionStartTime: null,
        currentSessionId: null,
      }));
    },
    
    resetTimer: () => {
      set((state) => ({
        status: 'idle',
        currentPhase: state.mode === 'pomodoro' ? 'work' : 'pause',
        displayTime: state.mode === 'pomodoro' 
          ? getInitialPhaseTime('work', state)
          : 0,
        elapsedTime: 0,
        sessionStartTime: null,
        currentSessionId: null,
        pomodoroCount: 0,
        lastNotificationTime: 0,
      }));
    },
    
    completeSession: (notes, rating) => {
      set((state) => {
        if (!state.sessionStartTime || !state.selectedSubject) return state;
        
        const session: TimerSession = {
          id: state.currentSessionId || `session_${Date.now()}`,
          subject: state.selectedSubject,
          taskName: state.currentTaskName || undefined,
          startTime: state.sessionStartTime,
          endTime: new Date(),
          duration: state.elapsedTime,
          mode: state.mode,
          phase: state.currentPhase,
          completed: true,
          notes,
          productivityRating: rating,
        };
        
        return {
          completedSessions: [...state.completedSessions, session],
          status: 'completed',
        };
      });
    },
    
    skipPhase: () => {
      set((state) => {
        if (state.mode !== 'pomodoro') return state;
        
        let nextPhase: TimerPhase;
        let newPomodoroCount = state.pomodoroCount;
        
        if (state.currentPhase === 'work') {
          newPomodoroCount += 1;
          nextPhase = newPomodoroCount % state.longBreakInterval === 0 
            ? 'longBreak' 
            : 'shortBreak';
        } else {
          nextPhase = 'work';
        }
        
        return {
          currentPhase: nextPhase,
          displayTime: getInitialPhaseTime(nextPhase, state),
          elapsedTime: 0,
          pomodoroCount: newPomodoroCount,
          status: state.autoStartBreaks || state.autoStartPomodoros ? 'running' : 'idle',
        };
      });
    },
    
    updateDisplayTime: (time) => set({ displayTime: time }),
    updateElapsedTime: (time) => set({ elapsedTime: time }),
    
    startWork: () => {
      set((state) => ({
        currentPhase: 'work',
        displayTime: getInitialPhaseTime('work', state),
        elapsedTime: 0,
        status: 'idle',
      }));
    },
    
    startShortBreak: () => {
      set((state) => ({
        currentPhase: 'shortBreak',
        displayTime: getInitialPhaseTime('shortBreak', state),
        elapsedTime: 0,
        status: 'idle',
      }));
    },
    
    startLongBreak: () => {
      set((state) => ({
        currentPhase: 'longBreak',
        displayTime: getInitialPhaseTime('longBreak', state),
        elapsedTime: 0,
        status: 'idle',
      }));
    },
    
    updateSettings: (settings) => {
      set((state) => ({
        ...state,
        ...settings,
        displayTime: state.status === 'idle' 
          ? getInitialPhaseTime(state.currentPhase, { ...state, ...settings })
          : state.displayTime,
      }));
    },
    
    loadPersistedState: async () => {
      try {
        const stored = await AsyncStorage.getItem(TIMER_STORAGE_KEY);
        if (stored) {
          const parsedState = JSON.parse(stored);
          set((state) => ({
            ...state,
            ...parsedState,
            status: 'idle', // Always start idle after app restart
            sessionStartTime: parsedState.sessionStartTime 
              ? new Date(parsedState.sessionStartTime) 
              : null,
            completedSessions: parsedState.completedSessions?.map((session: any) => ({
              ...session,
              startTime: new Date(session.startTime),
              endTime: session.endTime ? new Date(session.endTime) : undefined,
            })) || [],
          }));
        }
      } catch (error) {
        console.error('Failed to load timer state:', error);
      }
    },
    
    persistState: async () => {
      try {
        const state = get();
        const stateToPersist = {
          mode: state.mode,
          currentPhase: state.currentPhase,
          selectedSubject: state.selectedSubject,
          currentTaskName: state.currentTaskName,
          pomodoroCount: state.pomodoroCount,
          workDuration: state.workDuration,
          shortBreakDuration: state.shortBreakDuration,
          longBreakDuration: state.longBreakDuration,
          longBreakInterval: state.longBreakInterval,
          notificationIntervals: state.notificationIntervals,
          autoStartBreaks: state.autoStartBreaks,
          autoStartPomodoros: state.autoStartPomodoros,
          soundEnabled: state.soundEnabled,
          vibrationEnabled: state.vibrationEnabled,
          completedSessions: state.completedSessions,
        };
        await AsyncStorage.setItem(TIMER_STORAGE_KEY, JSON.stringify(stateToPersist));
      } catch (error) {
        console.error('Failed to persist timer state:', error);
      }
    },
    
    clearPersistedState: async () => {
      try {
        await AsyncStorage.removeItem(TIMER_STORAGE_KEY);
      } catch (error) {
        console.error('Failed to clear timer state:', error);
      }
    },
  }))
);
