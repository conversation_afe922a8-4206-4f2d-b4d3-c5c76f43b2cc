import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

// ===========================================
// 🏆 ACHIEVEMENT TYPES
// ===========================================

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: 'streak' | 'time' | 'sessions' | 'productivity' | 'milestone' | 'special';
  tier: 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond';
  requirement: {
    type: 'streak' | 'total_time' | 'session_count' | 'productivity_avg' | 'custom';
    value: number;
    timeframe?: 'daily' | 'weekly' | 'monthly' | 'all_time';
  };
  reward?: {
    points: number;
    badge?: string;
    unlocks?: string[];
  };
  isUnlocked: boolean;
  unlockedAt?: string;
  progress: number; // 0-100
  isSecret?: boolean;
}

export interface AchievementNotification {
  id: string;
  achievement: Achievement;
  timestamp: string;
  isRead: boolean;
  celebrationShown: boolean;
}

export interface UserProgress {
  totalStudyTime: number;
  totalSessions: number;
  currentStreak: number;
  longestStreak: number;
  averageProductivity: number;
  completedAchievements: number;
  totalPoints: number;
  level: number;
  experiencePoints: number;
  nextLevelXP: number;
}

// ===========================================
// 🏆 ACHIEVEMENT STATE
// ===========================================

export interface AchievementsState {
  // Data
  achievements: Achievement[];
  notifications: AchievementNotification[];
  userProgress: UserProgress;
  
  // UI State
  isLoading: boolean;
  error: string | null;
  showCelebration: boolean;
  celebrationAchievement: Achievement | null;
  
  // Actions - Achievement Management
  loadAchievements: () => Promise<void>;
  checkAchievements: (userStats: Partial<UserProgress>) => Promise<Achievement[]>;
  unlockAchievement: (achievementId: string) => Promise<void>;
  
  // Actions - Notifications
  addNotification: (achievement: Achievement) => void;
  markNotificationRead: (notificationId: string) => void;
  clearNotifications: () => void;
  
  // Actions - Celebration
  showAchievementCelebration: (achievement: Achievement) => void;
  hideCelebration: () => void;
  
  // Actions - Progress
  updateUserProgress: (progress: Partial<UserProgress>) => void;
  calculateLevel: (experiencePoints: number) => { level: number; nextLevelXP: number };
  addExperiencePoints: (points: number) => void;
  
  // Actions - Persistence
  persistState: () => Promise<void>;
  loadPersistedState: () => Promise<void>;
  
  // Getters
  getUnlockedAchievements: () => Achievement[];
  getLockedAchievements: () => Achievement[];
  getAchievementsByCategory: (category: Achievement['category']) => Achievement[];
  getUnreadNotifications: () => AchievementNotification[];
  getCompletionPercentage: () => number;
}

const ACHIEVEMENTS_STORAGE_KEY = 'achievements_state';

// Default achievements
const DEFAULT_ACHIEVEMENTS: Omit<Achievement, 'isUnlocked' | 'unlockedAt' | 'progress'>[] = [
  // Streak Achievements
  {
    id: 'first_day',
    title: 'Getting Started',
    description: 'Complete your first study session',
    icon: '🌱',
    category: 'milestone',
    tier: 'bronze',
    requirement: { type: 'session_count', value: 1 },
    reward: { points: 10 },
  },
  {
    id: 'streak_3',
    title: 'Building Momentum',
    description: 'Study for 3 days in a row',
    icon: '🔥',
    category: 'streak',
    tier: 'bronze',
    requirement: { type: 'streak', value: 3 },
    reward: { points: 25 },
  },
  {
    id: 'streak_7',
    title: 'Week Warrior',
    description: 'Study for 7 days in a row',
    icon: '⚡',
    category: 'streak',
    tier: 'silver',
    requirement: { type: 'streak', value: 7 },
    reward: { points: 50 },
  },
  {
    id: 'streak_30',
    title: 'Consistency Champion',
    description: 'Study for 30 days in a row',
    icon: '👑',
    category: 'streak',
    tier: 'gold',
    requirement: { type: 'streak', value: 30 },
    reward: { points: 200 },
  },
  {
    id: 'streak_100',
    title: 'Century Club',
    description: 'Study for 100 days in a row',
    icon: '💎',
    category: 'streak',
    tier: 'diamond',
    requirement: { type: 'streak', value: 100 },
    reward: { points: 1000 },
  },
  
  // Time Achievements
  {
    id: 'time_1h',
    title: 'First Hour',
    description: 'Study for 1 hour total',
    icon: '⏰',
    category: 'time',
    tier: 'bronze',
    requirement: { type: 'total_time', value: 3600 },
    reward: { points: 15 },
  },
  {
    id: 'time_10h',
    title: 'Dedicated Learner',
    description: 'Study for 10 hours total',
    icon: '📚',
    category: 'time',
    tier: 'silver',
    requirement: { type: 'total_time', value: 36000 },
    reward: { points: 75 },
  },
  {
    id: 'time_100h',
    title: 'Study Master',
    description: 'Study for 100 hours total',
    icon: '🎓',
    category: 'time',
    tier: 'gold',
    requirement: { type: 'total_time', value: 360000 },
    reward: { points: 500 },
  },
  {
    id: 'time_1000h',
    title: 'Knowledge Seeker',
    description: 'Study for 1000 hours total',
    icon: '🧠',
    category: 'time',
    tier: 'platinum',
    requirement: { type: 'total_time', value: 3600000 },
    reward: { points: 2000 },
  },
  
  // Session Achievements
  {
    id: 'sessions_10',
    title: 'Getting Into Rhythm',
    description: 'Complete 10 study sessions',
    icon: '🎯',
    category: 'sessions',
    tier: 'bronze',
    requirement: { type: 'session_count', value: 10 },
    reward: { points: 30 },
  },
  {
    id: 'sessions_50',
    title: 'Session Specialist',
    description: 'Complete 50 study sessions',
    icon: '🏃',
    category: 'sessions',
    tier: 'silver',
    requirement: { type: 'session_count', value: 50 },
    reward: { points: 100 },
  },
  {
    id: 'sessions_200',
    title: 'Focus Expert',
    description: 'Complete 200 study sessions',
    icon: '🎪',
    category: 'sessions',
    tier: 'gold',
    requirement: { type: 'session_count', value: 200 },
    reward: { points: 300 },
  },
  
  // Productivity Achievements
  {
    id: 'productivity_4',
    title: 'Quality Focus',
    description: 'Maintain 4+ star average productivity',
    icon: '⭐',
    category: 'productivity',
    tier: 'silver',
    requirement: { type: 'productivity_avg', value: 4.0 },
    reward: { points: 150 },
  },
  {
    id: 'productivity_45',
    title: 'Excellence Standard',
    description: 'Maintain 4.5+ star average productivity',
    icon: '🌟',
    category: 'productivity',
    tier: 'gold',
    requirement: { type: 'productivity_avg', value: 4.5 },
    reward: { points: 400 },
  },
  
  // Special Achievements
  {
    id: 'early_bird',
    title: 'Early Bird',
    description: 'Study before 7 AM',
    icon: '🌅',
    category: 'special',
    tier: 'bronze',
    requirement: { type: 'custom', value: 1 },
    reward: { points: 25 },
    isSecret: true,
  },
  {
    id: 'night_owl',
    title: 'Night Owl',
    description: 'Study after 10 PM',
    icon: '🦉',
    category: 'special',
    tier: 'bronze',
    requirement: { type: 'custom', value: 1 },
    reward: { points: 25 },
    isSecret: true,
  },
  {
    id: 'weekend_warrior',
    title: 'Weekend Warrior',
    description: 'Study on both Saturday and Sunday',
    icon: '🏋️',
    category: 'special',
    tier: 'silver',
    requirement: { type: 'custom', value: 1 },
    reward: { points: 50 },
  },
];

// Helper function to calculate level from XP
const calculateLevelFromXP = (xp: number): { level: number; nextLevelXP: number } => {
  // Level formula: level = floor(sqrt(xp / 100))
  // XP required for level n: n^2 * 100
  const level = Math.floor(Math.sqrt(xp / 100)) + 1;
  const nextLevelXP = Math.pow(level, 2) * 100;
  
  return { level, nextLevelXP };
};

// Helper function to generate ID
const generateId = () => Math.random().toString(36).substr(2, 9);

export const useAchievementsStore = create<AchievementsState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    achievements: DEFAULT_ACHIEVEMENTS.map(achievement => ({
      ...achievement,
      isUnlocked: false,
      progress: 0,
    })),
    notifications: [],
    userProgress: {
      totalStudyTime: 0,
      totalSessions: 0,
      currentStreak: 0,
      longestStreak: 0,
      averageProductivity: 0,
      completedAchievements: 0,
      totalPoints: 0,
      level: 1,
      experiencePoints: 0,
      nextLevelXP: 100,
    },
    
    isLoading: false,
    error: null,
    showCelebration: false,
    celebrationAchievement: null,
    
    // Achievement Management Actions
    loadAchievements: async () => {
      try {
        set({ isLoading: true, error: null });
        await get().loadPersistedState();
        set({ isLoading: false });
      } catch (error) {
        set({ error: 'Failed to load achievements', isLoading: false });
        console.error('Error loading achievements:', error);
      }
    },
    
    checkAchievements: async (userStats: Partial<UserProgress>) => {
      const state = get();
      const newlyUnlocked: Achievement[] = [];
      
      // Update user progress
      const updatedProgress = { ...state.userProgress, ...userStats };
      set({ userProgress: updatedProgress });
      
      // Check each locked achievement
      const updatedAchievements = state.achievements.map(achievement => {
        if (achievement.isUnlocked) return achievement;
        
        let progress = 0;
        let shouldUnlock = false;
        
        switch (achievement.requirement.type) {
          case 'streak':
            progress = Math.min((updatedProgress.currentStreak / achievement.requirement.value) * 100, 100);
            shouldUnlock = updatedProgress.currentStreak >= achievement.requirement.value;
            break;
          case 'total_time':
            progress = Math.min((updatedProgress.totalStudyTime / achievement.requirement.value) * 100, 100);
            shouldUnlock = updatedProgress.totalStudyTime >= achievement.requirement.value;
            break;
          case 'session_count':
            progress = Math.min((updatedProgress.totalSessions / achievement.requirement.value) * 100, 100);
            shouldUnlock = updatedProgress.totalSessions >= achievement.requirement.value;
            break;
          case 'productivity_avg':
            progress = Math.min((updatedProgress.averageProductivity / achievement.requirement.value) * 100, 100);
            shouldUnlock = updatedProgress.averageProductivity >= achievement.requirement.value;
            break;
        }
        
        if (shouldUnlock && !achievement.isUnlocked) {
          const unlockedAchievement = {
            ...achievement,
            isUnlocked: true,
            unlockedAt: new Date().toISOString(),
            progress: 100,
          };
          newlyUnlocked.push(unlockedAchievement);
          return unlockedAchievement;
        }
        
        return { ...achievement, progress };
      });
      
      set({ achievements: updatedAchievements });
      
      // Add notifications for newly unlocked achievements
      newlyUnlocked.forEach(achievement => {
        get().addNotification(achievement);
        get().addExperiencePoints(achievement.reward?.points || 0);
      });
      
      // Update completed achievements count
      const completedCount = updatedAchievements.filter(a => a.isUnlocked).length;
      set(state => ({
        userProgress: {
          ...state.userProgress,
          completedAchievements: completedCount,
        }
      }));
      
      // Persist state
      await get().persistState();
      
      return newlyUnlocked;
    },

    unlockAchievement: async (achievementId: string) => {
      const state = get();
      const achievement = state.achievements.find(a => a.id === achievementId);

      if (achievement && !achievement.isUnlocked) {
        const unlockedAchievement = {
          ...achievement,
          isUnlocked: true,
          unlockedAt: new Date().toISOString(),
          progress: 100,
        };

        const updatedAchievements = state.achievements.map(a =>
          a.id === achievementId ? unlockedAchievement : a
        );

        set({ achievements: updatedAchievements });
        get().addNotification(unlockedAchievement);
        get().addExperiencePoints(achievement.reward?.points || 0);

        await get().persistState();
      }
    },

    // Notification Actions
    addNotification: (achievement: Achievement) => {
      const notification: AchievementNotification = {
        id: generateId(),
        achievement,
        timestamp: new Date().toISOString(),
        isRead: false,
        celebrationShown: false,
      };

      set(state => ({
        notifications: [notification, ...state.notifications],
      }));

      // Show celebration
      get().showAchievementCelebration(achievement);
    },

    markNotificationRead: (notificationId: string) => {
      set(state => ({
        notifications: state.notifications.map(notification =>
          notification.id === notificationId
            ? { ...notification, isRead: true }
            : notification
        ),
      }));
    },

    clearNotifications: () => {
      set({ notifications: [] });
    },

    // Celebration Actions
    showAchievementCelebration: (achievement: Achievement) => {
      set({
        showCelebration: true,
        celebrationAchievement: achievement,
      });
    },

    hideCelebration: () => {
      set({
        showCelebration: false,
        celebrationAchievement: null,
      });
    },

    // Progress Actions
    updateUserProgress: (progress: Partial<UserProgress>) => {
      set(state => ({
        userProgress: { ...state.userProgress, ...progress },
      }));
    },

    calculateLevel: (experiencePoints: number) => {
      return calculateLevelFromXP(experiencePoints);
    },

    addExperiencePoints: (points: number) => {
      set(state => {
        const newXP = state.userProgress.experiencePoints + points;
        const { level, nextLevelXP } = calculateLevelFromXP(newXP);

        return {
          userProgress: {
            ...state.userProgress,
            experiencePoints: newXP,
            totalPoints: state.userProgress.totalPoints + points,
            level,
            nextLevelXP,
          },
        };
      });
    },

    // Persistence Actions
    persistState: async () => {
      try {
        const state = get();
        const stateToPersist = {
          achievements: state.achievements,
          notifications: state.notifications,
          userProgress: state.userProgress,
        };

        await AsyncStorage.setItem(ACHIEVEMENTS_STORAGE_KEY, JSON.stringify(stateToPersist));
      } catch (error) {
        console.error('Failed to persist achievements state:', error);
      }
    },

    loadPersistedState: async () => {
      try {
        const stored = await AsyncStorage.getItem(ACHIEVEMENTS_STORAGE_KEY);
        if (stored) {
          const parsedState = JSON.parse(stored);
          set(state => ({
            ...state,
            ...parsedState,
            isLoading: false,
            error: null,
          }));
        }
      } catch (error) {
        console.error('Failed to load persisted achievements state:', error);
      }
    },

    // Getters
    getUnlockedAchievements: () => {
      return get().achievements.filter(achievement => achievement.isUnlocked);
    },

    getLockedAchievements: () => {
      return get().achievements.filter(achievement => !achievement.isUnlocked && !achievement.isSecret);
    },

    getAchievementsByCategory: (category: Achievement['category']) => {
      return get().achievements.filter(achievement => achievement.category === category);
    },

    getUnreadNotifications: () => {
      return get().notifications.filter(notification => !notification.isRead);
    },

    getCompletionPercentage: () => {
      const state = get();
      const totalAchievements = state.achievements.filter(a => !a.isSecret).length;
      const unlockedAchievements = state.achievements.filter(a => a.isUnlocked && !a.isSecret).length;

      return totalAchievements > 0 ? (unlockedAchievements / totalAchievements) * 100 : 0;
    },
  }))
);
