import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { analyticsService } from '../services/supabase/analytics';
import { getCurrentUser } from '../services/supabase/client';

// ===========================================
// 📊 ANALYTICS TYPES
// ===========================================

export interface StudySession {
  id: string;
  user_id: string;
  subject: string;
  task_name: string | null;
  task_type: string | null;
  start_time: string;
  end_time: string | null;
  duration: number;
  mode: 'pomodoro' | 'stopwatch';
  phase: 'work' | 'shortBreak' | 'longBreak' | 'pause';
  completed: boolean;
  date: string;
  notes: string | null;
  productivity_rating: number | null;
  created_at: string;
  updated_at: string;
}

export interface AnalyticsMetrics {
  totalStudyTime: number;
  averageSessionLength: number;
  totalSessions: number;
  completedSessions: number;
  productivityScore: number;
  streakDays: number;
  currentStreak: number;
  longestStreak: number;
  subjectDistribution: { [subject: string]: number };
  dailyGoalProgress: number;
  weeklyGoalProgress: number;
  monthlyGoalProgress: number;
}

export interface TimeDistribution {
  subject: string;
  time: number;
  percentage: number;
  color: string;
  sessions: number;
}

export interface ProductivityTrend {
  date: string;
  rating: number;
  sessions: number;
  totalTime: number;
}

export interface GoalProgress {
  daily: {
    target: number;
    current: number;
    percentage: number;
  };
  weekly: {
    target: number;
    current: number;
    percentage: number;
  };
  monthly: {
    target: number;
    current: number;
    percentage: number;
  };
}

export interface ChartConfiguration {
  timeRange: 'day' | 'week' | 'month' | 'year';
  chartType: 'line' | 'bar' | 'pie' | 'area';
  showAverage: boolean;
  showTrend: boolean;
  groupBy: 'day' | 'week' | 'month';
}

export interface DateRange {
  start: Date;
  end: Date;
  preset: 'today' | 'yesterday' | 'week' | 'month' | 'year' | 'custom';
}

// ===========================================
// 📊 ANALYTICS STATE
// ===========================================

export interface AnalyticsState {
  // Data
  sessions: StudySession[];
  metrics: AnalyticsMetrics | null;
  timeDistribution: TimeDistribution[];
  productivityTrends: ProductivityTrend[];
  goalProgress: GoalProgress | null;
  
  // UI State
  isLoading: boolean;
  error: string | null;
  dateRange: DateRange;
  chartConfig: ChartConfiguration;
  selectedSubjects: string[];
  
  // Actions - Data Loading
  loadAnalytics: (userId: string, dateRange?: DateRange) => Promise<void>;
  refreshAnalytics: () => Promise<void>;
  
  // Actions - Date Range
  setDateRange: (range: DateRange) => void;
  setDatePreset: (preset: DateRange['preset']) => void;
  
  // Actions - Chart Configuration
  setChartConfig: (config: Partial<ChartConfiguration>) => void;
  setTimeRange: (range: ChartConfiguration['timeRange']) => void;
  setChartType: (type: ChartConfiguration['chartType']) => void;
  
  // Actions - Filters
  setSelectedSubjects: (subjects: string[]) => void;
  toggleSubject: (subject: string) => void;
  clearFilters: () => void;
  
  // Actions - Calculations
  calculateMetrics: () => void;
  calculateTimeDistribution: () => void;
  calculateProductivityTrends: () => void;
  calculateGoalProgress: () => void;
  calculateStreak: () => number;
  
  // Actions - Persistence
  persistState: () => Promise<void>;
  loadPersistedState: () => Promise<void>;
  clearPersistedState: () => Promise<void>;
  
  // Getters
  getFilteredSessions: () => StudySession[];
  getSubjectList: () => string[];
  getTotalStudyTime: () => number;
  getAverageProductivity: () => number;
  getSessionsInRange: (start: Date, end: Date) => StudySession[];
}

const ANALYTICS_STORAGE_KEY = 'analytics_state';

// Default date range (last 7 days)
const getDefaultDateRange = (): DateRange => {
  const end = new Date();
  const start = new Date();
  start.setDate(start.getDate() - 7);
  
  return {
    start,
    end,
    preset: 'week',
  };
};

// Default chart configuration
const DEFAULT_CHART_CONFIG: ChartConfiguration = {
  timeRange: 'week',
  chartType: 'line',
  showAverage: true,
  showTrend: true,
  groupBy: 'day',
};

// Helper function to generate ID
const generateId = () => Math.random().toString(36).substr(2, 9);

// Helper function to get date string
const getDateString = (date: Date) => date.toISOString().split('T')[0];

// Helper function to filter sessions by date range
const filterSessionsByDateRange = (sessions: StudySession[], dateRange: DateRange): StudySession[] => {
  return sessions.filter(session => {
    const sessionDate = new Date(session.date);
    return sessionDate >= dateRange.start && sessionDate <= dateRange.end;
  });
};

// Helper function to calculate streak
const calculateStreakFromSessions = (sessions: StudySession[]): { current: number; longest: number } => {
  if (sessions.length === 0) return { current: 0, longest: 0 };

  // Group sessions by date
  const sessionsByDate = sessions.reduce((acc, session) => {
    const date = session.date;
    if (!acc[date]) acc[date] = [];
    acc[date].push(session);
    return acc;
  }, {} as { [date: string]: StudySession[] });

  // Get sorted dates
  const dates = Object.keys(sessionsByDate).sort();
  
  let currentStreak = 0;
  let longestStreak = 0;
  let tempStreak = 0;
  
  const today = getDateString(new Date());
  const yesterday = getDateString(new Date(Date.now() - 24 * 60 * 60 * 1000));
  
  // Calculate current streak (from today backwards)
  for (let i = dates.length - 1; i >= 0; i--) {
    const date = dates[i];
    if (date === today || (currentStreak === 0 && date === yesterday)) {
      currentStreak++;
    } else if (i === dates.length - 1) {
      break;
    } else {
      const prevDate = new Date(dates[i + 1]);
      const currDate = new Date(date);
      const diffDays = Math.floor((prevDate.getTime() - currDate.getTime()) / (24 * 60 * 60 * 1000));
      
      if (diffDays === 1) {
        currentStreak++;
      } else {
        break;
      }
    }
  }
  
  // Calculate longest streak
  for (let i = 0; i < dates.length; i++) {
    tempStreak = 1;
    
    for (let j = i + 1; j < dates.length; j++) {
      const prevDate = new Date(dates[j - 1]);
      const currDate = new Date(dates[j]);
      const diffDays = Math.floor((currDate.getTime() - prevDate.getTime()) / (24 * 60 * 60 * 1000));
      
      if (diffDays === 1) {
        tempStreak++;
      } else {
        break;
      }
    }
    
    longestStreak = Math.max(longestStreak, tempStreak);
  }
  
  return { current: currentStreak, longest: longestStreak };
};

export const useAnalyticsStore = create<AnalyticsState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    sessions: [],
    metrics: null,
    timeDistribution: [],
    productivityTrends: [],
    goalProgress: null,
    
    isLoading: false,
    error: null,
    dateRange: getDefaultDateRange(),
    chartConfig: DEFAULT_CHART_CONFIG,
    selectedSubjects: [],
    
    // Data Loading Actions
    loadAnalytics: async (userId: string, dateRange?: DateRange) => {
      try {
        set({ isLoading: true, error: null });

        const user = await getCurrentUser();
        if (!user) {
          throw new Error('User not authenticated');
        }

        // Use provided date range or current state
        const targetDateRange = dateRange || get().dateRange;

        // Load study sessions from Supabase
        const sessions = await analyticsService.getStudySessions(user.id, targetDateRange);

        // Update sessions in state
        set({ sessions });

        // Calculate all metrics
        get().calculateMetrics();
        get().calculateTimeDistribution();
        get().calculateProductivityTrends();
        get().calculateGoalProgress();

        // Persist the updated state
        await get().persistState();

        set({ isLoading: false });
      } catch (error) {
        set({ error: 'Failed to load analytics data', isLoading: false });
        console.error('Error loading analytics:', error);
      }
    },
    
    refreshAnalytics: async () => {
      try {
        const user = await getCurrentUser();
        if (!user) {
          throw new Error('User not authenticated');
        }

        await get().loadAnalytics(user.id);
      } catch (error) {
        console.error('Failed to refresh analytics:', error);
        set({ error: 'Failed to refresh analytics data' });
      }
    },
    
    // Date Range Actions
    setDateRange: (range: DateRange) => {
      set({ dateRange: range });
      get().calculateMetrics();
      get().calculateTimeDistribution();
      get().calculateProductivityTrends();
      get().calculateGoalProgress();
    },
    
    setDatePreset: (preset: DateRange['preset']) => {
      const now = new Date();
      let start: Date;
      let end: Date = new Date(now);
      
      switch (preset) {
        case 'today':
          start = new Date(now);
          break;
        case 'yesterday':
          start = new Date(now);
          start.setDate(start.getDate() - 1);
          end = new Date(start);
          break;
        case 'week':
          start = new Date(now);
          start.setDate(start.getDate() - 7);
          break;
        case 'month':
          start = new Date(now);
          start.setMonth(start.getMonth() - 1);
          break;
        case 'year':
          start = new Date(now);
          start.setFullYear(start.getFullYear() - 1);
          break;
        default:
          return;
      }
      
      get().setDateRange({ start, end, preset });
    },

    // Chart Configuration Actions
    setChartConfig: (config: Partial<ChartConfiguration>) => {
      set(state => ({
        chartConfig: { ...state.chartConfig, ...config }
      }));
    },

    setTimeRange: (range: ChartConfiguration['timeRange']) => {
      get().setChartConfig({ timeRange: range });
    },

    setChartType: (type: ChartConfiguration['chartType']) => {
      get().setChartConfig({ chartType: type });
    },

    // Filter Actions
    setSelectedSubjects: (subjects: string[]) => {
      set({ selectedSubjects: subjects });
      get().calculateMetrics();
      get().calculateTimeDistribution();
      get().calculateProductivityTrends();
    },

    toggleSubject: (subject: string) => {
      const state = get();
      const subjects = state.selectedSubjects.includes(subject)
        ? state.selectedSubjects.filter(s => s !== subject)
        : [...state.selectedSubjects, subject];

      state.setSelectedSubjects(subjects);
    },

    clearFilters: () => {
      set({ selectedSubjects: [] });
      get().calculateMetrics();
      get().calculateTimeDistribution();
      get().calculateProductivityTrends();
    },

    // Calculation Actions
    calculateMetrics: () => {
      const state = get();
      const filteredSessions = state.getFilteredSessions();

      if (filteredSessions.length === 0) {
        set({ metrics: null });
        return;
      }

      const totalStudyTime = filteredSessions.reduce((sum, session) => sum + session.duration, 0);
      const completedSessions = filteredSessions.filter(session => session.completed);
      const averageSessionLength = totalStudyTime / filteredSessions.length;

      // Calculate productivity score (average of ratings)
      const ratingsSum = filteredSessions
        .filter(session => session.productivity_rating !== null)
        .reduce((sum, session) => sum + (session.productivity_rating || 0), 0);
      const ratingsCount = filteredSessions.filter(session => session.productivity_rating !== null).length;
      const productivityScore = ratingsCount > 0 ? ratingsSum / ratingsCount : 0;

      // Calculate subject distribution
      const subjectDistribution = filteredSessions.reduce((acc, session) => {
        acc[session.subject] = (acc[session.subject] || 0) + session.duration;
        return acc;
      }, {} as { [subject: string]: number });

      // Calculate streak
      const { current: currentStreak, longest: longestStreak } = calculateStreakFromSessions(filteredSessions);

      const metrics: AnalyticsMetrics = {
        totalStudyTime,
        averageSessionLength,
        totalSessions: filteredSessions.length,
        completedSessions: completedSessions.length,
        productivityScore,
        streakDays: currentStreak,
        currentStreak,
        longestStreak,
        subjectDistribution,
        dailyGoalProgress: 0, // TODO: Calculate based on daily goals
        weeklyGoalProgress: 0, // TODO: Calculate based on weekly goals
        monthlyGoalProgress: 0, // TODO: Calculate based on monthly goals
      };

      set({ metrics });
    },

    calculateTimeDistribution: () => {
      const state = get();
      const filteredSessions = state.getFilteredSessions();

      if (filteredSessions.length === 0) {
        set({ timeDistribution: [] });
        return;
      }

      const totalTime = filteredSessions.reduce((sum, session) => sum + session.duration, 0);
      const subjectData = filteredSessions.reduce((acc, session) => {
        if (!acc[session.subject]) {
          acc[session.subject] = { time: 0, sessions: 0 };
        }
        acc[session.subject].time += session.duration;
        acc[session.subject].sessions += 1;
        return acc;
      }, {} as { [subject: string]: { time: number; sessions: number } });

      // Generate colors for subjects
      const colors = [
        '#6750A4', '#7C4DFF', '#FF6B6B', '#4ECDC4', '#45B7D1',
        '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'
      ];

      const timeDistribution: TimeDistribution[] = Object.entries(subjectData)
        .map(([subject, data], index) => ({
          subject,
          time: data.time,
          percentage: (data.time / totalTime) * 100,
          color: colors[index % colors.length],
          sessions: data.sessions,
        }))
        .sort((a, b) => b.time - a.time);

      set({ timeDistribution });
    },

    calculateProductivityTrends: () => {
      const state = get();
      const filteredSessions = state.getFilteredSessions();

      if (filteredSessions.length === 0) {
        set({ productivityTrends: [] });
        return;
      }

      // Group sessions by date
      const sessionsByDate = filteredSessions.reduce((acc, session) => {
        const date = session.date;
        if (!acc[date]) {
          acc[date] = [];
        }
        acc[date].push(session);
        return acc;
      }, {} as { [date: string]: StudySession[] });

      const productivityTrends: ProductivityTrend[] = Object.entries(sessionsByDate)
        .map(([date, sessions]) => {
          const ratingsSum = sessions
            .filter(session => session.productivity_rating !== null)
            .reduce((sum, session) => sum + (session.productivity_rating || 0), 0);
          const ratingsCount = sessions.filter(session => session.productivity_rating !== null).length;
          const averageRating = ratingsCount > 0 ? ratingsSum / ratingsCount : 0;
          const totalTime = sessions.reduce((sum, session) => sum + session.duration, 0);

          return {
            date,
            rating: averageRating,
            sessions: sessions.length,
            totalTime,
          };
        })
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

      set({ productivityTrends });
    },

    calculateGoalProgress: () => {
      const state = get();
      const filteredSessions = state.getFilteredSessions();

      // TODO: Implement goal progress calculation based on user goals
      // For now, using placeholder values
      const goalProgress: GoalProgress = {
        daily: {
          target: 7200, // 2 hours in seconds
          current: 0,
          percentage: 0,
        },
        weekly: {
          target: 50400, // 14 hours in seconds
          current: 0,
          percentage: 0,
        },
        monthly: {
          target: 216000, // 60 hours in seconds
          current: 0,
          percentage: 0,
        },
      };

      set({ goalProgress });
    },

    calculateStreak: () => {
      const state = get();
      const { current } = calculateStreakFromSessions(state.sessions);
      return current;
    },

    // Persistence Actions
    persistState: async () => {
      try {
        const state = get();
        const stateToPersist = {
          sessions: state.sessions,
          dateRange: state.dateRange,
          chartConfig: state.chartConfig,
          selectedSubjects: state.selectedSubjects,
        };

        await AsyncStorage.setItem(ANALYTICS_STORAGE_KEY, JSON.stringify(stateToPersist));
      } catch (error) {
        console.error('Failed to persist analytics state:', error);
      }
    },

    loadPersistedState: async () => {
      try {
        const stored = await AsyncStorage.getItem(ANALYTICS_STORAGE_KEY);
        if (stored) {
          const parsedState = JSON.parse(stored);
          set(state => ({
            ...state,
            ...parsedState,
            dateRange: {
              ...parsedState.dateRange,
              start: new Date(parsedState.dateRange.start),
              end: new Date(parsedState.dateRange.end),
            },
            isLoading: false,
            error: null,
          }));
        }
      } catch (error) {
        console.error('Failed to load persisted analytics state:', error);
      }
    },

    clearPersistedState: async () => {
      try {
        await AsyncStorage.removeItem(ANALYTICS_STORAGE_KEY);
        set({
          sessions: [],
          metrics: null,
          timeDistribution: [],
          productivityTrends: [],
          goalProgress: null,
          dateRange: getDefaultDateRange(),
          chartConfig: DEFAULT_CHART_CONFIG,
          selectedSubjects: [],
        });
      } catch (error) {
        console.error('Failed to clear persisted analytics state:', error);
      }
    },

    // Getters
    getFilteredSessions: () => {
      const state = get();
      let filtered = filterSessionsByDateRange(state.sessions, state.dateRange);

      if (state.selectedSubjects.length > 0) {
        filtered = filtered.filter(session =>
          state.selectedSubjects.includes(session.subject)
        );
      }

      return filtered;
    },

    getSubjectList: () => {
      const state = get();
      const subjects = [...new Set(state.sessions.map(session => session.subject))];
      return subjects.sort();
    },

    getTotalStudyTime: () => {
      const state = get();
      return state.getFilteredSessions().reduce((sum, session) => sum + session.duration, 0);
    },

    getAverageProductivity: () => {
      const state = get();
      const sessions = state.getFilteredSessions();
      const ratingsSum = sessions
        .filter(session => session.productivity_rating !== null)
        .reduce((sum, session) => sum + (session.productivity_rating || 0), 0);
      const ratingsCount = sessions.filter(session => session.productivity_rating !== null).length;

      return ratingsCount > 0 ? ratingsSum / ratingsCount : 0;
    },

    getSessionsInRange: (start: Date, end: Date) => {
      const state = get();
      return state.sessions.filter(session => {
        const sessionDate = new Date(session.date);
        return sessionDate >= start && sessionDate <= end;
      });
    },
  }))
);
