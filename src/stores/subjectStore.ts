import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface Subject {
  id: string;
  user_id: string;
  name: string;
  color: string;
  created_at: string;
  updated_at: string;
  // Computed fields
  totalTime?: number;
  sessionsToday?: number;
  lastStudied?: string;
  averageSessionLength?: number;
}

export interface SubjectStats {
  totalTime: number;
  sessionsCount: number;
  averageSessionLength: number;
  lastStudied: Date | null;
  streak: number;
  todayTime: number;
  weekTime: number;
  monthTime: number;
}

export interface SubjectState {
  // Data
  subjects: Subject[];
  selectedSubject: Subject | null;
  isLoading: boolean;
  error: string | null;
  
  // Filters and sorting
  searchQuery: string;
  sortBy: 'name' | 'created_at' | 'totalTime' | 'lastStudied';
  sortOrder: 'asc' | 'desc';
  colorFilter: string | null;
  
  // Statistics
  subjectStats: Record<string, SubjectStats>;
  
  // Actions - CRUD
  createSubject: (name: string, color: string) => Promise<Subject | null>;
  updateSubject: (id: string, updates: Partial<Subject>) => Promise<boolean>;
  deleteSubject: (id: string) => Promise<boolean>;
  getSubject: (id: string) => Subject | null;
  getSubjectByName: (name: string) => Subject | null;
  
  // Actions - Selection
  selectSubject: (subject: Subject | null) => void;
  selectSubjectById: (id: string) => void;
  selectSubjectByName: (name: string) => void;
  
  // Actions - Filtering and Sorting
  setSearchQuery: (query: string) => void;
  setSortBy: (sortBy: SubjectState['sortBy']) => void;
  setSortOrder: (order: SubjectState['sortOrder']) => void;
  setColorFilter: (color: string | null) => void;
  clearFilters: () => void;
  
  // Actions - Statistics
  updateSubjectStats: (subjectId: string, sessionData: any) => void;
  getSubjectStats: (subjectId: string) => SubjectStats | null;
  calculateDailyTime: (subjectId: string) => number;
  
  // Actions - Data Management
  loadSubjects: () => Promise<void>;
  refreshSubjects: () => Promise<void>;
  syncWithServer: () => Promise<void>;
  
  // Actions - Persistence
  persistState: () => Promise<void>;
  loadPersistedState: () => Promise<void>;
  clearPersistedState: () => Promise<void>;
  
  // Computed getters
  getFilteredSubjects: () => Subject[];
  getFavoriteSubjects: () => Subject[];
  getRecentSubjects: () => Subject[];
  getMostStudiedSubjects: () => Subject[];
}

const SUBJECTS_STORAGE_KEY = 'subjects_state';

// Default colors for new subjects
export const DEFAULT_SUBJECT_COLORS = [
  '#6750A4', '#7C4DFF', '#3F51B5', '#2196F3',
  '#00BCD4', '#009688', '#4CAF50', '#8BC34A',
  '#CDDC39', '#FFEB3B', '#FFC107', '#FF9800',
  '#FF5722', '#F44336', '#E91E63', '#9C27B0',
];

// Helper function to generate unique ID
const generateId = () => `subject_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// Helper function to get random color
const getRandomColor = (existingColors: string[]) => {
  const availableColors = DEFAULT_SUBJECT_COLORS.filter(color => !existingColors.includes(color));
  if (availableColors.length === 0) {
    return DEFAULT_SUBJECT_COLORS[Math.floor(Math.random() * DEFAULT_SUBJECT_COLORS.length)];
  }
  return availableColors[Math.floor(Math.random() * availableColors.length)];
};

// Helper function to sort subjects
const sortSubjects = (subjects: Subject[], sortBy: string, sortOrder: string): Subject[] => {
  return [...subjects].sort((a, b) => {
    let aValue: any;
    let bValue: any;
    
    switch (sortBy) {
      case 'name':
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
        break;
      case 'created_at':
        aValue = new Date(a.created_at).getTime();
        bValue = new Date(b.created_at).getTime();
        break;
      case 'totalTime':
        aValue = a.totalTime || 0;
        bValue = b.totalTime || 0;
        break;
      case 'lastStudied':
        aValue = a.lastStudied ? new Date(a.lastStudied).getTime() : 0;
        bValue = b.lastStudied ? new Date(b.lastStudied).getTime() : 0;
        break;
      default:
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
    }
    
    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  });
};

export const useSubjectStore = create<SubjectState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    subjects: [],
    selectedSubject: null,
    isLoading: false,
    error: null,
    
    searchQuery: '',
    sortBy: 'name',
    sortOrder: 'asc',
    colorFilter: null,
    
    subjectStats: {},
    
    // CRUD Actions
    createSubject: async (name: string, color: string) => {
      try {
        set({ isLoading: true, error: null });
        
        const existingSubject = get().subjects.find(s => s.name.toLowerCase() === name.toLowerCase());
        if (existingSubject) {
          set({ error: 'Subject with this name already exists', isLoading: false });
          return null;
        }
        
        const now = new Date().toISOString();
        const newSubject: Subject = {
          id: generateId(),
          user_id: 'current_user', // This would come from auth context
          name: name.trim(),
          color: color || getRandomColor(get().subjects.map(s => s.color)),
          created_at: now,
          updated_at: now,
          totalTime: 0,
          sessionsToday: 0,
        };
        
        set(state => ({
          subjects: [...state.subjects, newSubject],
          isLoading: false,
        }));
        
        // Persist changes
        await get().persistState();
        
        return newSubject;
      } catch (error) {
        set({ error: 'Failed to create subject', isLoading: false });
        console.error('Error creating subject:', error);
        return null;
      }
    },
    
    updateSubject: async (id: string, updates: Partial<Subject>) => {
      try {
        set({ isLoading: true, error: null });
        
        const subjectIndex = get().subjects.findIndex(s => s.id === id);
        if (subjectIndex === -1) {
          set({ error: 'Subject not found', isLoading: false });
          return false;
        }
        
        const updatedSubject = {
          ...get().subjects[subjectIndex],
          ...updates,
          updated_at: new Date().toISOString(),
        };
        
        set(state => ({
          subjects: state.subjects.map(s => s.id === id ? updatedSubject : s),
          selectedSubject: state.selectedSubject?.id === id ? updatedSubject : state.selectedSubject,
          isLoading: false,
        }));
        
        // Persist changes
        await get().persistState();
        
        return true;
      } catch (error) {
        set({ error: 'Failed to update subject', isLoading: false });
        console.error('Error updating subject:', error);
        return false;
      }
    },
    
    deleteSubject: async (id: string) => {
      try {
        set({ isLoading: true, error: null });
        
        set(state => ({
          subjects: state.subjects.filter(s => s.id !== id),
          selectedSubject: state.selectedSubject?.id === id ? null : state.selectedSubject,
          subjectStats: Object.fromEntries(
            Object.entries(state.subjectStats).filter(([key]) => key !== id)
          ),
          isLoading: false,
        }));
        
        // Persist changes
        await get().persistState();
        
        return true;
      } catch (error) {
        set({ error: 'Failed to delete subject', isLoading: false });
        console.error('Error deleting subject:', error);
        return false;
      }
    },
    
    getSubject: (id: string) => {
      return get().subjects.find(s => s.id === id) || null;
    },
    
    getSubjectByName: (name: string) => {
      return get().subjects.find(s => s.name.toLowerCase() === name.toLowerCase()) || null;
    },
    
    // Selection Actions
    selectSubject: (subject: Subject | null) => {
      set({ selectedSubject: subject });
    },
    
    selectSubjectById: (id: string) => {
      const subject = get().getSubject(id);
      set({ selectedSubject: subject });
    },
    
    selectSubjectByName: (name: string) => {
      const subject = get().getSubjectByName(name);
      set({ selectedSubject: subject });
    },
    
    // Filtering and Sorting Actions
    setSearchQuery: (query: string) => {
      set({ searchQuery: query });
    },
    
    setSortBy: (sortBy: SubjectState['sortBy']) => {
      set({ sortBy });
    },
    
    setSortOrder: (order: SubjectState['sortOrder']) => {
      set({ sortOrder: order });
    },
    
    setColorFilter: (color: string | null) => {
      set({ colorFilter: color });
    },
    
    clearFilters: () => {
      set({
        searchQuery: '',
        sortBy: 'name',
        sortOrder: 'asc',
        colorFilter: null,
      });
    },
    
    // Statistics Actions
    updateSubjectStats: (subjectId: string, sessionData: any) => {
      const currentStats = get().subjectStats[subjectId] || {
        totalTime: 0,
        sessionsCount: 0,
        averageSessionLength: 0,
        lastStudied: null,
        streak: 0,
        todayTime: 0,
        weekTime: 0,
        monthTime: 0,
      };
      
      const newStats: SubjectStats = {
        ...currentStats,
        totalTime: currentStats.totalTime + sessionData.duration,
        sessionsCount: currentStats.sessionsCount + 1,
        lastStudied: new Date(),
        todayTime: currentStats.todayTime + sessionData.duration,
      };
      
      newStats.averageSessionLength = newStats.totalTime / newStats.sessionsCount;
      
      set(state => ({
        subjectStats: {
          ...state.subjectStats,
          [subjectId]: newStats,
        },
      }));
      
      // Update subject with computed stats
      get().updateSubject(subjectId, {
        totalTime: newStats.totalTime,
        lastStudied: newStats.lastStudied.toISOString(),
        averageSessionLength: newStats.averageSessionLength,
      });
    },
    
    getSubjectStats: (subjectId: string) => {
      return get().subjectStats[subjectId] || null;
    },
    
    calculateDailyTime: (subjectId: string) => {
      const stats = get().subjectStats[subjectId];
      return stats?.todayTime || 0;
    },
    
    // Data Management Actions
    loadSubjects: async () => {
      try {
        set({ isLoading: true, error: null });
        
        // In a real app, this would fetch from Supabase
        // For now, load from persisted state
        await get().loadPersistedState();
        
        set({ isLoading: false });
      } catch (error) {
        set({ error: 'Failed to load subjects', isLoading: false });
        console.error('Error loading subjects:', error);
      }
    },
    
    refreshSubjects: async () => {
      await get().loadSubjects();
    },
    
    syncWithServer: async () => {
      // TODO: Implement server sync with Supabase
      console.log('Syncing subjects with server...');
    },
    
    // Persistence Actions
    persistState: async () => {
      try {
        const state = get();
        const stateToPersist = {
          subjects: state.subjects,
          selectedSubject: state.selectedSubject,
          subjectStats: state.subjectStats,
          searchQuery: state.searchQuery,
          sortBy: state.sortBy,
          sortOrder: state.sortOrder,
          colorFilter: state.colorFilter,
        };
        
        await AsyncStorage.setItem(SUBJECTS_STORAGE_KEY, JSON.stringify(stateToPersist));
      } catch (error) {
        console.error('Failed to persist subjects state:', error);
      }
    },
    
    loadPersistedState: async () => {
      try {
        const stored = await AsyncStorage.getItem(SUBJECTS_STORAGE_KEY);
        if (stored) {
          const parsedState = JSON.parse(stored);
          set(state => ({
            ...state,
            ...parsedState,
            isLoading: false,
            error: null,
          }));
        }
      } catch (error) {
        console.error('Failed to load persisted subjects state:', error);
      }
    },
    
    clearPersistedState: async () => {
      try {
        await AsyncStorage.removeItem(SUBJECTS_STORAGE_KEY);
        set({
          subjects: [],
          selectedSubject: null,
          subjectStats: {},
          searchQuery: '',
          sortBy: 'name',
          sortOrder: 'asc',
          colorFilter: null,
        });
      } catch (error) {
        console.error('Failed to clear persisted subjects state:', error);
      }
    },
    
    // Computed getters
    getFilteredSubjects: () => {
      const state = get();
      let filtered = state.subjects;
      
      // Apply search filter
      if (state.searchQuery) {
        const query = state.searchQuery.toLowerCase();
        filtered = filtered.filter(subject =>
          subject.name.toLowerCase().includes(query)
        );
      }
      
      // Apply color filter
      if (state.colorFilter) {
        filtered = filtered.filter(subject => subject.color === state.colorFilter);
      }
      
      // Apply sorting
      return sortSubjects(filtered, state.sortBy, state.sortOrder);
    },
    
    getFavoriteSubjects: () => {
      const state = get();
      return state.subjects
        .filter(subject => (subject.totalTime || 0) > 0)
        .sort((a, b) => (b.totalTime || 0) - (a.totalTime || 0))
        .slice(0, 5);
    },
    
    getRecentSubjects: () => {
      const state = get();
      return state.subjects
        .filter(subject => subject.lastStudied)
        .sort((a, b) => {
          const aTime = a.lastStudied ? new Date(a.lastStudied).getTime() : 0;
          const bTime = b.lastStudied ? new Date(b.lastStudied).getTime() : 0;
          return bTime - aTime;
        })
        .slice(0, 5);
    },
    
    getMostStudiedSubjects: () => {
      const state = get();
      return state.subjects
        .filter(subject => (subject.totalTime || 0) > 0)
        .sort((a, b) => (b.totalTime || 0) - (a.totalTime || 0))
        .slice(0, 10);
    },
  }))
);
