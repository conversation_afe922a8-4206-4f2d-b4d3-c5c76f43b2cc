import { Platform } from 'react-native';
import MaterialYou from 'react-native-material-you-colors';
import type { MaterialYouPalette } from 'react-native-material-you-colors';
import { ExpressiveColors } from './expressiveTheme';

// ===========================================
// 🌈 DYNAMIC COLOR SYSTEM
// ===========================================

// Material You Generation Styles
export type GenerationStyle = 
  | 'TONAL_SPOT'      // Default - balanced and versatile
  | 'VIBRANT'         // High chroma, energetic
  | 'EXPRESSIVE'      // Creative and artistic
  | 'SPRITZ'          // Soft and gentle
  | 'RAINBOW'         // Playful and colorful
  | 'FRUIT_SALAD'     // Bright and fun
  | 'CONTENT'         // Content-aware
  | 'MONOCHROMATIC';  // Single hue variations

// Fallback seed colors for different contexts
export const FallbackSeedColors = {
  primary: '#6750A4',      // Material 3 default purple
  study: '#1976D2',        // Study-focused blue
  creative: '#E91E63',     // Creative pink
  calm: '#4CAF50',         // Calming green
  energy: '#FF9800',       // Energetic orange
  focus: '#9C27B0',        // Focus purple
  neutral: '#607D8B',      // Neutral blue-grey
} as const;

// Dynamic color configuration
export interface DynamicColorConfig {
  enabled: boolean;
  fallbackSeedColor: string;
  generationStyle: GenerationStyle;
  autoDetectWallpaper: boolean;
  respectSystemTheme: boolean;
}

// Default configuration
export const defaultDynamicColorConfig: DynamicColorConfig = {
  enabled: true,
  fallbackSeedColor: FallbackSeedColors.primary,
  generationStyle: 'TONAL_SPOT',
  autoDetectWallpaper: true,
  respectSystemTheme: true,
};

// Material You palette to theme colors mapping
export function mapMaterialYouToTheme(palette: MaterialYouPalette, isDark: boolean = false) {
  // Material You palette structure:
  // Each array contains 13 shades: [0-50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950]
  // Index mapping: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
  
  if (isDark) {
    return {
      // Primary colors - using accent1 palette
      primary: palette.system_accent1[4],           // 200 - lighter for dark theme
      onPrimary: palette.system_accent1[11],        // 900 - dark text on light primary
      primaryContainer: palette.system_accent1[9],  // 700 - darker container
      onPrimaryContainer: palette.system_accent1[2], // 100 - light text on dark container
      
      // Secondary colors - using accent2 palette
      secondary: palette.system_accent2[4],         // 200
      onSecondary: palette.system_accent2[11],      // 900
      secondaryContainer: palette.system_accent2[9], // 700
      onSecondaryContainer: palette.system_accent2[2], // 100
      
      // Tertiary colors - using accent3 palette
      tertiary: palette.system_accent3[4],          // 200
      onTertiary: palette.system_accent3[11],       // 900
      tertiaryContainer: palette.system_accent3[9], // 700
      onTertiaryContainer: palette.system_accent3[2], // 100
      
      // Surface colors - using neutral1 palette
      surface: palette.system_neutral1[11],         // 900 - dark surface
      onSurface: palette.system_neutral1[2],        // 100 - light text
      surfaceVariant: palette.system_neutral2[10],  // 800
      onSurfaceVariant: palette.system_neutral2[3], // 300
      surfaceDim: palette.system_neutral1[12],      // 950 - darkest
      surfaceBright: palette.system_neutral1[9],    // 700 - lighter than surface
      surfaceContainerLowest: palette.system_neutral1[12], // 950
      surfaceContainerLow: palette.system_neutral1[11],    // 900
      surfaceContainer: palette.system_neutral1[10],       // 800
      surfaceContainerHigh: palette.system_neutral1[9],    // 700
      surfaceContainerHighest: palette.system_neutral1[8], // 600
      
      // Background
      background: palette.system_neutral1[11],      // 900 - dark background
      onBackground: palette.system_neutral1[2],     // 100 - light text
      
      // Outline colors
      outline: palette.system_neutral2[7],          // 500
      outlineVariant: palette.system_neutral2[9],   // 700
      
      // Inverse colors
      inverseSurface: palette.system_neutral1[2],   // 100 - light surface for dark theme
      inverseOnSurface: palette.system_neutral1[11], // 900 - dark text
      inversePrimary: palette.system_accent1[7],    // 500 - standard primary for light theme
      
      // Other
      shadow: '#000000',
      scrim: '#000000',
      surfaceDisabled: `${palette.system_neutral1[2]}1F`, // 100 with 12% opacity
      onSurfaceDisabled: `${palette.system_neutral1[2]}61`, // 100 with 38% opacity
      backdrop: `${palette.system_neutral2[9]}66`, // 700 with 40% opacity
    };
  } else {
    return {
      // Primary colors - using accent1 palette
      primary: palette.system_accent1[7],           // 500 - standard primary
      onPrimary: palette.system_accent1[0],         // 50 - very light text
      primaryContainer: palette.system_accent1[2],  // 100 - light container
      onPrimaryContainer: palette.system_accent1[11], // 900 - dark text on light container
      
      // Secondary colors - using accent2 palette
      secondary: palette.system_accent2[7],         // 500
      onSecondary: palette.system_accent2[0],       // 50
      secondaryContainer: palette.system_accent2[2], // 100
      onSecondaryContainer: palette.system_accent2[11], // 900
      
      // Tertiary colors - using accent3 palette
      tertiary: palette.system_accent3[7],          // 500
      onTertiary: palette.system_accent3[0],        // 50
      tertiaryContainer: palette.system_accent3[2], // 100
      onTertiaryContainer: palette.system_accent3[11], // 900
      
      // Surface colors - using neutral1 palette
      surface: palette.system_neutral1[1],          // 10 - very light surface
      onSurface: palette.system_neutral1[11],       // 900 - dark text
      surfaceVariant: palette.system_neutral2[2],   // 100
      onSurfaceVariant: palette.system_neutral2[9], // 700
      surfaceDim: palette.system_neutral1[4],       // 300
      surfaceBright: palette.system_neutral1[0],    // 50 - brightest
      surfaceContainerLowest: palette.system_neutral1[0],  // 50
      surfaceContainerLow: palette.system_neutral1[1],     // 10
      surfaceContainer: palette.system_neutral1[2],        // 100
      surfaceContainerHigh: palette.system_neutral1[3],    // 200
      surfaceContainerHighest: palette.system_neutral1[4], // 300
      
      // Background
      background: palette.system_neutral1[1],       // 10 - very light background
      onBackground: palette.system_neutral1[11],    // 900 - dark text
      
      // Outline colors
      outline: palette.system_neutral2[7],          // 500
      outlineVariant: palette.system_neutral2[3],   // 200
      
      // Inverse colors
      inverseSurface: palette.system_neutral1[11],  // 900 - dark surface for light theme
      inverseOnSurface: palette.system_neutral1[1], // 10 - light text
      inversePrimary: palette.system_accent1[4],    // 200 - lighter primary for dark theme
      
      // Other
      shadow: '#000000',
      scrim: '#000000',
      surfaceDisabled: `${palette.system_neutral1[11]}1F`, // 900 with 12% opacity
      onSurfaceDisabled: `${palette.system_neutral1[11]}61`, // 900 with 38% opacity
      backdrop: `${palette.system_neutral2[9]}66`, // 700 with 40% opacity
    };
  }
}

// Check if Material You is supported on the current platform/device
export function isMaterialYouSupported(): boolean {
  try {
    return MaterialYou.isSupported;
  } catch (error) {
    console.warn('Material You support check failed:', error);
    return false;
  }
}

// Get Material You palette with fallback
export function getMaterialYouPalette(
  config: Partial<DynamicColorConfig> = {}
): MaterialYouPalette {
  const finalConfig = { ...defaultDynamicColorConfig, ...config };
  
  try {
    if (finalConfig.enabled && isMaterialYouSupported() && finalConfig.autoDetectWallpaper) {
      // Try to get system palette
      return MaterialYou.getMaterialYouPalette(
        finalConfig.fallbackSeedColor,
        finalConfig.generationStyle
      );
    } else {
      // Generate from fallback seed color
      return MaterialYou.generatePaletteFromColor(
        finalConfig.fallbackSeedColor,
        finalConfig.generationStyle
      );
    }
  } catch (error) {
    console.warn('Failed to get Material You palette, using fallback:', error);
    // Final fallback - generate from default seed color
    return MaterialYou.generatePaletteFromColor(
      FallbackSeedColors.primary,
      'TONAL_SPOT'
    );
  }
}

// Generate palette from custom seed color
export function generatePaletteFromSeedColor(
  seedColor: string,
  style: GenerationStyle = 'TONAL_SPOT'
): MaterialYouPalette {
  try {
    return MaterialYou.generatePaletteFromColor(seedColor, style);
  } catch (error) {
    console.warn('Failed to generate palette from seed color:', error);
    // Fallback to default
    return MaterialYou.generatePaletteFromColor(FallbackSeedColors.primary, 'TONAL_SPOT');
  }
}

// Utility to extract dominant color from image (placeholder for future implementation)
export async function extractDominantColorFromImage(imageUri: string): Promise<string> {
  // This would require additional libraries like react-native-image-colors
  // For now, return a default color
  console.warn('Image color extraction not implemented yet');
  return FallbackSeedColors.primary;
}

// Get generation style based on context
export function getContextualGenerationStyle(context: string): GenerationStyle {
  const contextMap: Record<string, GenerationStyle> = {
    study: 'TONAL_SPOT',
    focus: 'VIBRANT',
    creative: 'EXPRESSIVE',
    calm: 'SPRITZ',
    energy: 'RAINBOW',
    fun: 'FRUIT_SALAD',
    content: 'CONTENT',
    minimal: 'MONOCHROMATIC',
  };
  
  return contextMap[context.toLowerCase()] || 'TONAL_SPOT';
}

// Export Material You utilities
export { MaterialYou };
export type { MaterialYouPalette };
