import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';
import { Platform } from 'react-native';

// ===========================================
// 🎨 MATERIAL 3 EXPRESSIVE COLOR SYSTEM
// ===========================================

// Expressive color palettes with enhanced emotional impact
export const ExpressiveColors = {
  // Primary Palette - Deep Purple (Study Focus)
  primary: {
    0: '#000000',
    10: '#21005D',
    20: '#371E73',
    25: '#42298A',
    30: '#4F378B',
    35: '#5D4397',
    40: '#6750A4',
    50: '#7F67BE',
    60: '#9A82DB',
    70: '#B69DF8',
    80: '#D0BCFF',
    90: '#EADDFF',
    95: '#F6EDFF',
    98: '#FEF7FF',
    99: '#FFFBFE',
    100: '#FFFFFF',
  },

  // Secondary Palette - Neutral Violet (Balance)
  secondary: {
    0: '#000000',
    10: '#1D192B',
    20: '#332D41',
    25: '#3E384C',
    30: '#4A4458',
    35: '#565063',
    40: '#625B71',
    50: '#7A7289',
    60: '#948FA3',
    70: '#B0A7C0',
    80: '#CCC2DC',
    90: '#E8DEF8',
    95: '#F6EDFF',
    98: '#FEF7FF',
    99: '#FFFBFE',
    100: '#FFFFFF',
  },

  // Tertiary Palette - Warm Pink (Creativity)
  tertiary: {
    0: '#000000',
    10: '#31111D',
    20: '#492532',
    25: '#55303D',
    30: '#633B48',
    35: '#704654',
    40: '#7D5260',
    50: '#986977',
    60: '#B58392',
    70: '#D29DAC',
    80: '#EFB8C8',
    90: '#FFD8E4',
    95: '#FFECF1',
    98: '#FFF8F9',
    99: '#FFFBFA',
    100: '#FFFFFF',
  },

  // Error Palette - Expressive Red
  error: {
    0: '#000000',
    10: '#410002',
    20: '#690005',
    25: '#7E0007',
    30: '#93000A',
    35: '#A80710',
    40: '#BA1A1A',
    50: '#DE3730',
    60: '#FF5449',
    70: '#FF897D',
    80: '#FFB4AB',
    90: '#FFDAD6',
    95: '#FFEDEA',
    98: '#FFF8F7',
    99: '#FFFBFF',
    100: '#FFFFFF',
  },

  // Success Palette - Expressive Green
  success: {
    0: '#000000',
    10: '#002204',
    20: '#003909',
    25: '#00440C',
    30: '#005010',
    35: '#005D16',
    40: '#006B1C',
    50: '#008A2E',
    60: '#00AC42',
    70: '#1DD75B',
    80: '#4AE474',
    90: '#6FF58E',
    95: '#B7FFAB',
    98: '#EAFFF0',
    99: '#F5FFF7',
    100: '#FFFFFF',
  },

  // Warning Palette - Expressive Orange
  warning: {
    0: '#000000',
    10: '#2D1600',
    20: '#452B00',
    25: '#523300',
    30: '#5F3C00',
    35: '#6D4600',
    40: '#7B5000',
    50: '#996600',
    60: '#B87E00',
    70: '#D89700',
    80: '#F9B000',
    90: '#FFCC47',
    95: '#FFDF9C',
    98: '#FFF2D5',
    99: '#FFF8F0',
    100: '#FFFFFF',
  },

  // Neutral Palette - Enhanced Grays
  neutral: {
    0: '#000000',
    4: '#0F0D13',
    6: '#141218',
    10: '#1C1B1F',
    12: '#201F23',
    17: '#2B2930',
    20: '#313033',
    22: '#36343B',
    24: '#3B383E',
    25: '#3E3B42',
    30: '#48464C',
    35: '#544F58',
    40: '#605D64',
    50: '#787579',
    60: '#918E96',
    70: '#ACA9B1',
    80: '#C8C5CD',
    87: '#DDD8E1',
    90: '#E6E0E9',
    92: '#ECE6F0',
    94: '#F3EDF7',
    95: '#F5EFF7',
    96: '#F7F2FA',
    98: '#FEF7FF',
    99: '#FFFBFE',
    100: '#FFFFFF',
  },

  // Neutral Variant Palette - Tinted Grays
  neutralVariant: {
    0: '#000000',
    10: '#1D1A22',
    20: '#322F37',
    25: '#3D3A42',
    30: '#49454F',
    35: '#55515C',
    40: '#615D68',
    50: '#7A7581',
    60: '#948F9B',
    70: '#B0A9B6',
    80: '#CCC4D2',
    90: '#E8E0EE',
    95: '#F6EDFF',
    98: '#FEF7FF',
    99: '#FFFBFE',
    100: '#FFFFFF',
  },
};

// ===========================================
// ✍️ MATERIAL 3 EXPRESSIVE TYPOGRAPHY SYSTEM
// ===========================================

// Enhanced font weights for emotional impact
export const ExpressiveFontWeights = {
  thin: '100' as const,
  extraLight: '200' as const,
  light: '300' as const,
  regular: '400' as const,
  medium: '500' as const,
  semiBold: '600' as const,
  bold: '700' as const,
  extraBold: '800' as const,
  black: '900' as const,
};

// Platform-specific font families with expressive characteristics
export const ExpressiveFontFamilies = {
  // Primary brand font - optimized for readability and emotional impact
  brand: Platform.select({
    ios: 'SF Pro Display',
    android: 'Roboto',
    web: '"SF Pro Display", "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif',
    default: 'System',
  }),

  // Text font - optimized for body content
  text: Platform.select({
    ios: 'SF Pro Text',
    android: 'Roboto',
    web: '"SF Pro Text", "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif',
    default: 'System',
  }),

  // Monospace font for code and data
  mono: Platform.select({
    ios: 'SF Mono',
    android: 'Roboto Mono',
    web: '"SF Mono", "Roboto Mono", "Fira Code", "Consolas", monospace',
    default: 'monospace',
  }),

  // Expressive font for creative elements
  expressive: Platform.select({
    ios: 'SF Pro Rounded',
    android: 'Google Sans',
    web: '"SF Pro Rounded", "Google Sans", "Roboto", sans-serif',
    default: 'System',
  }),
};

// Material 3 Expressive Typography Scale with emotional enhancements
export const ExpressiveTypography = {
  // Display styles - for hero content and major headings
  displayLarge: {
    fontFamily: ExpressiveFontFamilies.brand,
    fontWeight: ExpressiveFontWeights.regular,
    fontSize: 57,
    lineHeight: 64,
    letterSpacing: -0.25,
    // Expressive enhancements
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },

  displayMedium: {
    fontFamily: ExpressiveFontFamilies.brand,
    fontWeight: ExpressiveFontWeights.regular,
    fontSize: 45,
    lineHeight: 52,
    letterSpacing: 0,
    textShadowColor: 'rgba(0, 0, 0, 0.08)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },

  displaySmall: {
    fontFamily: ExpressiveFontFamilies.brand,
    fontWeight: ExpressiveFontWeights.regular,
    fontSize: 36,
    lineHeight: 44,
    letterSpacing: 0,
  },

  // Headline styles - for section headers and important content
  headlineLarge: {
    fontFamily: ExpressiveFontFamilies.brand,
    fontWeight: ExpressiveFontWeights.semiBold, // Enhanced weight for impact
    fontSize: 32,
    lineHeight: 40,
    letterSpacing: 0,
  },

  headlineMedium: {
    fontFamily: ExpressiveFontFamilies.brand,
    fontWeight: ExpressiveFontWeights.semiBold,
    fontSize: 28,
    lineHeight: 36,
    letterSpacing: 0,
  },

  headlineSmall: {
    fontFamily: ExpressiveFontFamilies.brand,
    fontWeight: ExpressiveFontWeights.semiBold,
    fontSize: 24,
    lineHeight: 32,
    letterSpacing: 0,
  },

  // Title styles - for card headers and list items
  titleLarge: {
    fontFamily: ExpressiveFontFamilies.text,
    fontWeight: ExpressiveFontWeights.medium,
    fontSize: 22,
    lineHeight: 28,
    letterSpacing: 0,
  },

  titleMedium: {
    fontFamily: ExpressiveFontFamilies.text,
    fontWeight: ExpressiveFontWeights.medium,
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: 0.15,
  },

  titleSmall: {
    fontFamily: ExpressiveFontFamilies.text,
    fontWeight: ExpressiveFontWeights.medium,
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.1,
  },

  // Body styles - for main content
  bodyLarge: {
    fontFamily: ExpressiveFontFamilies.text,
    fontWeight: ExpressiveFontWeights.regular,
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: 0.5,
  },

  bodyMedium: {
    fontFamily: ExpressiveFontFamilies.text,
    fontWeight: ExpressiveFontWeights.regular,
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.25,
  },

  bodySmall: {
    fontFamily: ExpressiveFontFamilies.text,
    fontWeight: ExpressiveFontWeights.regular,
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.4,
  },

  // Label styles - for buttons and form elements
  labelLarge: {
    fontFamily: ExpressiveFontFamilies.text,
    fontWeight: ExpressiveFontWeights.medium,
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.1,
  },

  labelMedium: {
    fontFamily: ExpressiveFontFamilies.text,
    fontWeight: ExpressiveFontWeights.medium,
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.5,
  },

  labelSmall: {
    fontFamily: ExpressiveFontFamilies.text,
    fontWeight: ExpressiveFontWeights.medium,
    fontSize: 11,
    lineHeight: 16,
    letterSpacing: 0.5,
  },
};

// Expressive Typography Variants for specific use cases
export const ExpressiveTypographyVariants = {
  // Timer display - large, bold, attention-grabbing
  timerDisplay: {
    fontFamily: ExpressiveFontFamilies.brand,
    fontWeight: ExpressiveFontWeights.bold,
    fontSize: 48,
    lineHeight: 56,
    letterSpacing: -0.5,
    textAlign: 'center' as const,
  },

  // Subject labels - colorful and distinctive
  subjectLabel: {
    fontFamily: ExpressiveFontFamilies.text,
    fontWeight: ExpressiveFontWeights.semiBold,
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.8,
    textTransform: 'uppercase' as const,
  },

  // Statistics - clear and readable
  statisticValue: {
    fontFamily: ExpressiveFontFamilies.brand,
    fontWeight: ExpressiveFontWeights.bold,
    fontSize: 24,
    lineHeight: 32,
    letterSpacing: 0,
  },

  statisticLabel: {
    fontFamily: ExpressiveFontFamilies.text,
    fontWeight: ExpressiveFontWeights.medium,
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.4,
    textTransform: 'uppercase' as const,
  },

  // Button text with enhanced readability
  buttonPrimary: {
    fontFamily: ExpressiveFontFamilies.text,
    fontWeight: ExpressiveFontWeights.semiBold,
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.25,
    textTransform: 'uppercase' as const,
  },

  buttonSecondary: {
    fontFamily: ExpressiveFontFamilies.text,
    fontWeight: ExpressiveFontWeights.medium,
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.25,
  },

  // Navigation and tabs
  navigationLabel: {
    fontFamily: ExpressiveFontFamilies.text,
    fontWeight: ExpressiveFontWeights.medium,
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.5,
  },

  tabLabel: {
    fontFamily: ExpressiveFontFamilies.text,
    fontWeight: ExpressiveFontWeights.semiBold,
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.1,
  },

  // Form elements
  inputLabel: {
    fontFamily: ExpressiveFontFamilies.text,
    fontWeight: ExpressiveFontWeights.medium,
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.4,
  },

  inputText: {
    fontFamily: ExpressiveFontFamilies.text,
    fontWeight: ExpressiveFontWeights.regular,
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: 0.5,
  },

  // Error and helper text
  helperText: {
    fontFamily: ExpressiveFontFamilies.text,
    fontWeight: ExpressiveFontWeights.regular,
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.4,
  },

  errorText: {
    fontFamily: ExpressiveFontFamilies.text,
    fontWeight: ExpressiveFontWeights.medium,
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.4,
  },
};

// Emotional Typography - styles that convey specific emotions
export const EmotionalTypography = {
  // Focus state - for concentration and study
  focus: {
    ...ExpressiveTypography.headlineMedium,
    fontWeight: ExpressiveFontWeights.bold,
    letterSpacing: 0.5,
    textShadowColor: 'rgba(103, 80, 164, 0.2)', // Primary color shadow
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },

  // Energy state - for motivation and action
  energy: {
    ...ExpressiveTypography.titleLarge,
    fontWeight: ExpressiveFontWeights.extraBold,
    letterSpacing: 1,
    textTransform: 'uppercase' as const,
  },

  // Calm state - for relaxation and breaks
  calm: {
    ...ExpressiveTypography.bodyLarge,
    fontWeight: ExpressiveFontWeights.light,
    letterSpacing: 1.5,
    lineHeight: 28, // Increased line height for breathing room
  },

  // Success state - for achievements and completion
  success: {
    ...ExpressiveTypography.titleMedium,
    fontWeight: ExpressiveFontWeights.semiBold,
    letterSpacing: 0.8,
  },

  // Alert state - for warnings and important information
  alert: {
    ...ExpressiveTypography.labelLarge,
    fontWeight: ExpressiveFontWeights.bold,
    letterSpacing: 0.5,
    textTransform: 'uppercase' as const,
  },

  // Creativity state - for creative and artistic content
  creativity: {
    ...ExpressiveTypography.headlineSmall,
    fontFamily: ExpressiveFontFamilies.expressive,
    fontWeight: ExpressiveFontWeights.medium,
    letterSpacing: 0.2,
  },
};

export const expressiveLightTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    
    // Primary Colors
    primary: ExpressiveColors.primary[40],
    onPrimary: ExpressiveColors.primary[100],
    primaryContainer: ExpressiveColors.primary[90],
    onPrimaryContainer: ExpressiveColors.primary[10],
    
    // Secondary Colors
    secondary: ExpressiveColors.secondary[40],
    onSecondary: ExpressiveColors.secondary[100],
    secondaryContainer: ExpressiveColors.secondary[90],
    onSecondaryContainer: ExpressiveColors.secondary[10],
    
    // Tertiary Colors
    tertiary: ExpressiveColors.tertiary[40],
    onTertiary: ExpressiveColors.tertiary[100],
    tertiaryContainer: ExpressiveColors.tertiary[90],
    onTertiaryContainer: ExpressiveColors.tertiary[10],
    
    // Error Colors
    error: ExpressiveColors.error[40],
    onError: ExpressiveColors.error[100],
    errorContainer: ExpressiveColors.error[90],
    onErrorContainer: ExpressiveColors.error[10],
    
    // Success Colors (Custom)
    success: ExpressiveColors.success[40],
    onSuccess: ExpressiveColors.success[100],
    successContainer: ExpressiveColors.success[90],
    onSuccessContainer: ExpressiveColors.success[10],
    
    // Warning Colors (Custom)
    warning: ExpressiveColors.warning[40],
    onWarning: ExpressiveColors.warning[100],
    warningContainer: ExpressiveColors.warning[90],
    onWarningContainer: ExpressiveColors.warning[10],
    
    // Surface Colors
    surface: ExpressiveColors.neutral[99],
    onSurface: ExpressiveColors.neutral[10],
    surfaceVariant: ExpressiveColors.neutralVariant[90],
    onSurfaceVariant: ExpressiveColors.neutralVariant[30],
    surfaceDim: ExpressiveColors.neutral[80],
    surfaceBright: ExpressiveColors.neutral[98],
    surfaceContainerLowest: ExpressiveColors.neutral[100],
    surfaceContainerLow: ExpressiveColors.neutral[95],
    surfaceContainer: ExpressiveColors.neutral[90],
    surfaceContainerHigh: ExpressiveColors.neutral[80],
    surfaceContainerHighest: ExpressiveColors.neutral[70],
    
    // Background
    background: ExpressiveColors.neutral[99],
    onBackground: ExpressiveColors.neutral[10],
    
    // Outline
    outline: ExpressiveColors.neutralVariant[50],
    outlineVariant: ExpressiveColors.neutralVariant[80],
    
    // Inverse Colors
    inverseSurface: ExpressiveColors.neutral[20],
    inverseOnSurface: ExpressiveColors.neutral[95],
    inversePrimary: ExpressiveColors.primary[80],
    
    // Other
    shadow: ExpressiveColors.neutral[0],
    scrim: ExpressiveColors.neutral[0],
    surfaceDisabled: `${ExpressiveColors.neutral[10]}1F`, // 12% opacity
    onSurfaceDisabled: `${ExpressiveColors.neutral[10]}61`, // 38% opacity
    backdrop: `${ExpressiveColors.neutralVariant[20]}66`, // 40% opacity
  },

  // Typography system
  fonts: ExpressiveTypography,

  // Expressive enhancements
  expressive: {
    // Enhanced elevation system
    elevation: {
      level0: 'transparent',
      level1: ExpressiveColors.primary[95],
      level2: ExpressiveColors.primary[90],
      level3: ExpressiveColors.primary[80],
      level4: ExpressiveColors.primary[70],
      level5: ExpressiveColors.primary[60],
    },
    
    // Emotional color mappings
    emotions: {
      focus: ExpressiveColors.primary[40],
      energy: ExpressiveColors.warning[40],
      calm: ExpressiveColors.secondary[40],
      creativity: ExpressiveColors.tertiary[40],
      success: ExpressiveColors.success[40],
      alert: ExpressiveColors.error[40],
    },
    
    // Study-specific colors
    subjects: {
      physics: '#1976D2',      // Blue
      chemistry: '#388E3C',    // Green
      mathematics: '#F57C00',  // Orange
      biology: '#7B1FA2',      // Purple
      english: '#D32F2F',      // Red
      history: '#5D4037',      // Brown
      geography: '#00796B',    // Teal
      computer: '#455A64',     // Blue Grey
    },
  },
};

export const expressiveDarkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,

    // Primary Colors
    primary: ExpressiveColors.primary[80],
    onPrimary: ExpressiveColors.primary[20],
    primaryContainer: ExpressiveColors.primary[30],
    onPrimaryContainer: ExpressiveColors.primary[90],

    // Secondary Colors
    secondary: ExpressiveColors.secondary[80],
    onSecondary: ExpressiveColors.secondary[20],
    secondaryContainer: ExpressiveColors.secondary[30],
    onSecondaryContainer: ExpressiveColors.secondary[90],

    // Tertiary Colors
    tertiary: ExpressiveColors.tertiary[80],
    onTertiary: ExpressiveColors.tertiary[20],
    tertiaryContainer: ExpressiveColors.tertiary[30],
    onTertiaryContainer: ExpressiveColors.tertiary[90],

    // Error Colors
    error: ExpressiveColors.error[80],
    onError: ExpressiveColors.error[20],
    errorContainer: ExpressiveColors.error[30],
    onErrorContainer: ExpressiveColors.error[90],

    // Success Colors (Custom)
    success: ExpressiveColors.success[80],
    onSuccess: ExpressiveColors.success[20],
    successContainer: ExpressiveColors.success[30],
    onSuccessContainer: ExpressiveColors.success[90],

    // Warning Colors (Custom)
    warning: ExpressiveColors.warning[80],
    onWarning: ExpressiveColors.warning[20],
    warningContainer: ExpressiveColors.warning[30],
    onWarningContainer: ExpressiveColors.warning[90],

    // Surface Colors
    surface: ExpressiveColors.neutral[10],
    onSurface: ExpressiveColors.neutral[90],
    surfaceVariant: ExpressiveColors.neutralVariant[30],
    onSurfaceVariant: ExpressiveColors.neutralVariant[80],
    surfaceDim: ExpressiveColors.neutral[10],
    surfaceBright: ExpressiveColors.neutral[20],
    surfaceContainerLowest: ExpressiveColors.neutral[0],
    surfaceContainerLow: ExpressiveColors.neutral[10],
    surfaceContainer: ExpressiveColors.neutral[20],
    surfaceContainerHigh: ExpressiveColors.neutral[30],
    surfaceContainerHighest: ExpressiveColors.neutral[40],

    // Background
    background: ExpressiveColors.neutral[10],
    onBackground: ExpressiveColors.neutral[90],

    // Outline
    outline: ExpressiveColors.neutralVariant[60],
    outlineVariant: ExpressiveColors.neutralVariant[30],

    // Inverse Colors
    inverseSurface: ExpressiveColors.neutral[90],
    inverseOnSurface: ExpressiveColors.neutral[20],
    inversePrimary: ExpressiveColors.primary[40],

    // Other
    shadow: ExpressiveColors.neutral[0],
    scrim: ExpressiveColors.neutral[0],
    surfaceDisabled: `${ExpressiveColors.neutral[90]}1F`, // 12% opacity
    onSurfaceDisabled: `${ExpressiveColors.neutral[90]}61`, // 38% opacity
    backdrop: `${ExpressiveColors.neutralVariant[20]}66`, // 40% opacity
  },

  // Typography system
  fonts: ExpressiveTypography,

  // Expressive enhancements
  expressive: {
    // Enhanced elevation system for dark mode
    elevation: {
      level0: 'transparent',
      level1: ExpressiveColors.primary[20],
      level2: ExpressiveColors.primary[25],
      level3: ExpressiveColors.primary[30],
      level4: ExpressiveColors.primary[35],
      level5: ExpressiveColors.primary[40],
    },

    // Emotional color mappings (adjusted for dark mode)
    emotions: {
      focus: ExpressiveColors.primary[80],
      energy: ExpressiveColors.warning[80],
      calm: ExpressiveColors.secondary[80],
      creativity: ExpressiveColors.tertiary[80],
      success: ExpressiveColors.success[80],
      alert: ExpressiveColors.error[80],
    },

    // Study-specific colors (adjusted for dark mode)
    subjects: {
      physics: '#42A5F5',      // Light Blue
      chemistry: '#66BB6A',    // Light Green
      mathematics: '#FFA726',  // Light Orange
      biology: '#AB47BC',      // Light Purple
      english: '#EF5350',      // Light Red
      history: '#8D6E63',      // Light Brown
      geography: '#26A69A',    // Light Teal
      computer: '#78909C',     // Light Blue Grey
    },
  },
};

// ===========================================
// 🎭 THEME UTILITIES
// ===========================================

export const getTheme = (isDark: boolean) => {
  return isDark ? expressiveDarkTheme : expressiveLightTheme;
};

export const getExpressiveColor = (colorName: keyof typeof ExpressiveColors, tone: number) => {
  return ExpressiveColors[colorName][tone as keyof typeof ExpressiveColors[typeof colorName]];
};

export const getSubjectColor = (subject: string, isDark: boolean = false) => {
  const theme = getTheme(isDark);
  const subjectKey = subject.toLowerCase() as keyof typeof theme.expressive.subjects;
  return theme.expressive.subjects[subjectKey] || theme.colors.primary;
};

export const getEmotionalColor = (emotion: string, isDark: boolean = false) => {
  const theme = getTheme(isDark);
  const emotionKey = emotion.toLowerCase() as keyof typeof theme.expressive.emotions;
  return theme.expressive.emotions[emotionKey] || theme.colors.primary;
};

// Typography utilities
export const getTypographyStyle = (variant: keyof typeof ExpressiveTypography) => {
  return ExpressiveTypography[variant];
};

export const getTypographyVariant = (variant: keyof typeof ExpressiveTypographyVariants) => {
  return ExpressiveTypographyVariants[variant];
};

export const getEmotionalTypography = (emotion: keyof typeof EmotionalTypography) => {
  return EmotionalTypography[emotion];
};

// Font weight utility
export const getFontWeight = (weight: keyof typeof ExpressiveFontWeights) => {
  return ExpressiveFontWeights[weight];
};

// Font family utility
export const getFontFamily = (family: keyof typeof ExpressiveFontFamilies) => {
  return ExpressiveFontFamilies[family];
};

// Legacy exports for backward compatibility
export const lightTheme = expressiveLightTheme;
export const darkTheme = expressiveDarkTheme;
