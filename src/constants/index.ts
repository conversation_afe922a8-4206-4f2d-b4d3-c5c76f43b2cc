// Constants exports
// This file will be populated as constants are created

// Re-export all expressive theme constants including typography
export * from './expressiveTheme';

// Typography-specific exports for easier access
export {
  ExpressiveTypography,
  ExpressiveTypographyVariants,
  EmotionalTypography,
  ExpressiveFontWeights,
  ExpressiveFontFamilies,
  getTypographyStyle,
  getTypographyVariant,
  getEmotionalTypography,
  getFontWeight,
  getFontFamily,
} from './expressiveTheme';

// Dynamic color system exports
export * from './dynamicColors';
export {
  FallbackSeedColors,
  defaultDynamicColorConfig,
  getMaterialYouPalette,
  generatePaletteFromSeedColor,
  mapMaterialYouToTheme,
  isMaterialYouSupported,
  getContextualGenerationStyle,
  MaterialYou,
} from './dynamicColors';

// Motion system exports
export * from './expressiveMotion';
export {
  ExpressiveEasing,
  ExpressiveDurations,
  ExpressiveSprings,
  ExpressiveDelays,
  ExpressiveOrigins,
  ExpressiveAnimations,
  EmotionalMotion,
  GestureMotion,
  LoadingMotion,
  PageTransitions,
  MotionUtils,
} from './expressiveMotion';

// Surface elevation system exports
export * from './surfaceElevation';
export {
  ExpressiveElevation,
  ColoredElevation,
  SurfaceContainers,
  BlurEffects,
  getElevationStyle,
  getAnimatedElevationStyle,
  getSurfaceContainerColor,
  getElevationWithRadius,
  getContextualElevation,
  getSurfaceElevation,
} from './surfaceElevation';

// Icon system exports
export * from './icons';
export {
  IconSizes,
  MaterialIcons,
  EmojiIcons,
  IconSets,
  getIconSize,
  getIconConfig,
} from './icons';

// Color palette system exports
export * from './colorPalette';
export {
  MATERIAL_PRIMARY_COLORS,
  MATERIAL_SECONDARY_COLORS,
  MATERIAL_TERTIARY_COLORS,
  MATERIAL_WARM_COLORS,
  SEMANTIC_COLORS,
  COLOR_PALETTES,
  ALL_COLORS,
  SUBJECT_COLOR_RECOMMENDATIONS,
  DEFAULT_SUBJECT_COLORS,
  ColorUtils,
} from './colorPalette';

// Placeholder to prevent TypeScript errors
export {};
