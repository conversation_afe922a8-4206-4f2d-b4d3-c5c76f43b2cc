import { Platform, Animated } from 'react-native';
import { ExpressiveColors } from './expressiveTheme';

// ===========================================
// 🏔️ MATERIAL 3 EXPRESSIVE SURFACE ELEVATION SYSTEM
// ===========================================

// Elevation levels following Material 3 specification
export type ElevationLevel = 0 | 1 | 2 | 3 | 4 | 5;

// Surface container types
export type SurfaceContainer = 
  | 'surface'
  | 'surfaceContainer'
  | 'surfaceContainerLow'
  | 'surfaceContainerHigh'
  | 'surfaceContainerHighest';

// Elevation configuration interface
export interface ElevationConfig {
  level: ElevationLevel;
  shadowColor?: string;
  shadowOpacity?: number;
  shadowRadius?: number;
  shadowOffset?: { width: number; height: number };
  elevation?: number; // Android elevation
  backgroundColor?: string;
  borderRadius?: number;
}

// Material 3 Elevation Mapping
export const MaterialElevationLevels = {
  level0: {
    shadowHeight: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  level1: {
    shadowHeight: 1,
    shadowRadius: 3,
    elevation: 1,
  },
  level2: {
    shadowHeight: 2,
    shadowRadius: 6,
    elevation: 2,
  },
  level3: {
    shadowHeight: 4,
    shadowRadius: 8,
    elevation: 4,
  },
  level4: {
    shadowHeight: 6,
    shadowRadius: 10,
    elevation: 6,
  },
  level5: {
    shadowHeight: 8,
    shadowRadius: 12,
    elevation: 8,
  },
} as const;

// Enhanced Expressive Elevation System
export const ExpressiveElevation = {
  // Standard elevation levels with enhanced shadows
  level0: {
    shadowColor: '#000000',
    shadowOpacity: 0,
    shadowRadius: 0,
    shadowOffset: { width: 0, height: 0 },
    elevation: 0,
  },
  
  level1: {
    shadowColor: '#000000',
    shadowOpacity: 0.12,
    shadowRadius: 4,
    shadowOffset: { width: 0, height: 1 },
    elevation: Platform.OS === 'android' ? 2 : 0,
  },
  
  level2: {
    shadowColor: '#000000',
    shadowOpacity: 0.16,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: Platform.OS === 'android' ? 4 : 0,
  },
  
  level3: {
    shadowColor: '#000000',
    shadowOpacity: 0.20,
    shadowRadius: 12,
    shadowOffset: { width: 0, height: 4 },
    elevation: Platform.OS === 'android' ? 6 : 0,
  },
  
  level4: {
    shadowColor: '#000000',
    shadowOpacity: 0.24,
    shadowRadius: 16,
    shadowOffset: { width: 0, height: 6 },
    elevation: Platform.OS === 'android' ? 8 : 0,
  },
  
  level5: {
    shadowColor: '#000000',
    shadowOpacity: 0.28,
    shadowRadius: 20,
    shadowOffset: { width: 0, height: 8 },
    elevation: Platform.OS === 'android' ? 12 : 0,
  },
} as const;

// Colored elevation for expressive design
export const ColoredElevation = {
  primary: {
    level0: { shadowColor: ExpressiveColors.primary[40], shadowOpacity: 0 },
    level1: { shadowColor: ExpressiveColors.primary[40], shadowOpacity: 0.15 },
    level2: { shadowColor: ExpressiveColors.primary[40], shadowOpacity: 0.20 },
    level3: { shadowColor: ExpressiveColors.primary[40], shadowOpacity: 0.25 },
    level4: { shadowColor: ExpressiveColors.primary[40], shadowOpacity: 0.30 },
    level5: { shadowColor: ExpressiveColors.primary[40], shadowOpacity: 0.35 },
  },

  secondary: {
    level0: { shadowColor: ExpressiveColors.secondary[40], shadowOpacity: 0 },
    level1: { shadowColor: ExpressiveColors.secondary[40], shadowOpacity: 0.15 },
    level2: { shadowColor: ExpressiveColors.secondary[40], shadowOpacity: 0.20 },
    level3: { shadowColor: ExpressiveColors.secondary[40], shadowOpacity: 0.25 },
    level4: { shadowColor: ExpressiveColors.secondary[40], shadowOpacity: 0.30 },
    level5: { shadowColor: ExpressiveColors.secondary[40], shadowOpacity: 0.35 },
  },

  tertiary: {
    level0: { shadowColor: ExpressiveColors.tertiary[40], shadowOpacity: 0 },
    level1: { shadowColor: ExpressiveColors.tertiary[40], shadowOpacity: 0.15 },
    level2: { shadowColor: ExpressiveColors.tertiary[40], shadowOpacity: 0.20 },
    level3: { shadowColor: ExpressiveColors.tertiary[40], shadowOpacity: 0.25 },
    level4: { shadowColor: ExpressiveColors.tertiary[40], shadowOpacity: 0.30 },
    level5: { shadowColor: ExpressiveColors.tertiary[40], shadowOpacity: 0.35 },
  },

  success: {
    level0: { shadowColor: ExpressiveColors.success[40], shadowOpacity: 0 },
    level1: { shadowColor: ExpressiveColors.success[40], shadowOpacity: 0.15 },
    level2: { shadowColor: ExpressiveColors.success[40], shadowOpacity: 0.20 },
    level3: { shadowColor: ExpressiveColors.success[40], shadowOpacity: 0.25 },
    level4: { shadowColor: ExpressiveColors.success[40], shadowOpacity: 0.30 },
    level5: { shadowColor: ExpressiveColors.success[40], shadowOpacity: 0.35 },
  },

  warning: {
    level0: { shadowColor: ExpressiveColors.warning[40], shadowOpacity: 0 },
    level1: { shadowColor: ExpressiveColors.warning[40], shadowOpacity: 0.15 },
    level2: { shadowColor: ExpressiveColors.warning[40], shadowOpacity: 0.20 },
    level3: { shadowColor: ExpressiveColors.warning[40], shadowOpacity: 0.25 },
    level4: { shadowColor: ExpressiveColors.warning[40], shadowOpacity: 0.30 },
    level5: { shadowColor: ExpressiveColors.warning[40], shadowOpacity: 0.35 },
  },

  error: {
    level0: { shadowColor: ExpressiveColors.error[40], shadowOpacity: 0 },
    level1: { shadowColor: ExpressiveColors.error[40], shadowOpacity: 0.15 },
    level2: { shadowColor: ExpressiveColors.error[40], shadowOpacity: 0.20 },
    level3: { shadowColor: ExpressiveColors.error[40], shadowOpacity: 0.25 },
    level4: { shadowColor: ExpressiveColors.error[40], shadowOpacity: 0.30 },
    level5: { shadowColor: ExpressiveColors.error[40], shadowOpacity: 0.35 },
  },
} as const;

// Surface container background colors
export const SurfaceContainers = {
  light: {
    surface: ExpressiveColors.neutral[99],
    surfaceContainer: ExpressiveColors.neutral[94],
    surfaceContainerLow: ExpressiveColors.neutral[96],
    surfaceContainerHigh: ExpressiveColors.neutral[92],
    surfaceContainerHighest: ExpressiveColors.neutral[90],
  },
  
  dark: {
    surface: ExpressiveColors.neutral[10],
    surfaceContainer: ExpressiveColors.neutral[12],
    surfaceContainerLow: ExpressiveColors.neutral[10],
    surfaceContainerHigh: ExpressiveColors.neutral[17],
    surfaceContainerHighest: ExpressiveColors.neutral[22],
  },
} as const;

// Blur effects for iOS
export const BlurEffects = {
  light: {
    level1: { blurType: 'light', blurAmount: 10 },
    level2: { blurType: 'light', blurAmount: 15 },
    level3: { blurType: 'light', blurAmount: 20 },
    level4: { blurType: 'light', blurAmount: 25 },
    level5: { blurType: 'light', blurAmount: 30 },
  },
  
  dark: {
    level1: { blurType: 'dark', blurAmount: 10 },
    level2: { blurType: 'dark', blurAmount: 15 },
    level3: { blurType: 'dark', blurAmount: 20 },
    level4: { blurType: 'dark', blurAmount: 25 },
    level5: { blurType: 'dark', blurAmount: 30 },
  },
} as const;

// ===========================================
// 🛠️ ELEVATION UTILITIES
// ===========================================

// Get elevation style for a specific level
export const getElevationStyle = (
  level: ElevationLevel | number,
  colorVariant?: keyof typeof ColoredElevation
) => {
  // Clamp level to valid range (0-5)
  const clampedLevel = Math.max(0, Math.min(5, level)) as ElevationLevel;
  const baseElevation = ExpressiveElevation[`level${clampedLevel}`];

  if (colorVariant && clampedLevel >= 0) {
    const coloredShadow = ColoredElevation[colorVariant][`level${clampedLevel}`];
    if (coloredShadow) {
      return {
        ...baseElevation,
        shadowColor: coloredShadow.shadowColor,
        shadowOpacity: coloredShadow.shadowOpacity,
      };
    }
  }

  return baseElevation;
};

// Get animated elevation style
export const getAnimatedElevationStyle = (
  animatedValue: Animated.Value,
  colorVariant?: keyof typeof ColoredElevation
) => {
  const inputRange = [0, 1, 2, 3, 4, 5];
  const shadowHeights = [0, 1, 2, 4, 6, 8];
  const shadowRadii = [0, 4, 8, 12, 16, 20];
  const shadowOpacities = [0, 0.12, 0.16, 0.20, 0.24, 0.28];
  const elevations = [0, 2, 4, 6, 8, 12];
  
  const baseStyle = {
    shadowColor: colorVariant 
      ? ColoredElevation[colorVariant].level1.shadowColor 
      : '#000000',
    shadowOffset: {
      width: 0,
      height: animatedValue.interpolate({
        inputRange,
        outputRange: shadowHeights,
      }),
    },
    shadowOpacity: animatedValue.interpolate({
      inputRange,
      outputRange: shadowOpacities,
    }),
    shadowRadius: animatedValue.interpolate({
      inputRange,
      outputRange: shadowRadii,
    }),
  };
  
  if (Platform.OS === 'android') {
    return {
      ...baseStyle,
      elevation: animatedValue.interpolate({
        inputRange,
        outputRange: elevations,
      }),
    };
  }
  
  return baseStyle;
};

// Get surface container background color
export const getSurfaceContainerColor = (
  container: SurfaceContainer,
  isDark: boolean = false
) => {
  return isDark 
    ? SurfaceContainers.dark[container]
    : SurfaceContainers.light[container];
};

// Create elevation with border radius
export const getElevationWithRadius = (
  level: ElevationLevel,
  borderRadius: number = 12,
  colorVariant?: keyof typeof ColoredElevation
) => {
  return {
    ...getElevationStyle(level, colorVariant),
    borderRadius,
  };
};

// Get elevation for specific use cases
export const getContextualElevation = (context: string, level: ElevationLevel = 2) => {
  const contextMap: Record<string, keyof typeof ColoredElevation> = {
    primary: 'primary',
    secondary: 'secondary',
    tertiary: 'tertiary',
    success: 'success',
    warning: 'warning',
    error: 'error',
    focus: 'primary',
    energy: 'warning',
    calm: 'secondary',
    creativity: 'tertiary',
    alert: 'error',
  };
  
  const colorVariant = contextMap[context.toLowerCase()];
  return getElevationStyle(level, colorVariant);
};

// Combine elevation with surface container
export const getSurfaceElevation = (
  level: ElevationLevel,
  container: SurfaceContainer = 'surface',
  isDark: boolean = false,
  colorVariant?: keyof typeof ColoredElevation
) => {
  return {
    backgroundColor: getSurfaceContainerColor(container, isDark),
    ...getElevationStyle(level, colorVariant),
  };
};
