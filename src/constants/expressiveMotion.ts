import { Easing } from 'react-native';

// ===========================================
// 🎭 MATERIAL 3 EXPRESSIVE MOTION SYSTEM
// ===========================================

// Material 3 Easing Curves - Enhanced for Expressive Design
export const ExpressiveEasing = {
  // Standard Material 3 easing curves
  standard: Easing.bezier(0.2, 0.0, 0, 1.0),           // Default smooth easing
  decelerate: Easing.bezier(0.0, 0.0, 0.2, 1.0),       // Slow out, natural end
  accelerate: Easing.bezier(0.4, 0.0, 1.0, 1.0),       // Fast out, energetic start
  
  // Expressive easing curves for emotional impact
  bounce: Easing.bezier(0.68, -0.55, 0.265, 1.55),     // Playful bounce effect
  elastic: Easing.bezier(0.175, 0.885, 0.32, 1.275),   // Elastic, spring-like
  dramatic: Easing.bezier(0.25, 0.46, 0.45, 0.94),     // Dramatic, theatrical
  gentle: Easing.bezier(0.25, 0.1, 0.25, 1.0),         // Gentle, calming
  energetic: Easing.bezier(0.55, 0.085, 0.68, 0.53),   // Quick, energetic
  focus: Easing.bezier(0.4, 0.0, 0.2, 1.0),            // Sharp, focused
  creative: Easing.bezier(0.645, 0.045, 0.355, 1.0),   // Creative, artistic flow
  
  // Specialized curves
  overshoot: Easing.bezier(0.34, 1.56, 0.64, 1.0),     // Slight overshoot
  anticipate: Easing.bezier(0.36, 0.0, 0.66, -0.56),   // Anticipation before action
  backOut: Easing.bezier(0.175, 0.885, 0.32, 1.275),   // Back out with overshoot
  
  // Utility easings
  linear: Easing.linear,
  easeIn: Easing.in(Easing.ease),
  easeOut: Easing.out(Easing.ease),
  easeInOut: Easing.inOut(Easing.ease),
} as const;

// Duration Constants - Optimized for different interaction types
export const ExpressiveDurations = {
  // Micro-interactions (0-100ms)
  instant: 0,           // Immediate feedback
  micro: 50,            // Button press feedback
  quick: 100,           // Quick state changes
  
  // Short animations (100-300ms)
  short: 150,           // Small UI changes
  snappy: 200,          // Snappy transitions
  brief: 250,           // Brief content changes
  fast: 300,            // Fast page transitions
  
  // Medium animations (300-500ms)
  medium: 350,          // Standard transitions
  smooth: 400,          // Smooth content transitions
  comfortable: 450,     // Comfortable pacing
  standard: 500,        // Standard duration
  
  // Long animations (500ms+)
  slow: 600,            // Slow, deliberate animations
  relaxed: 750,         // Relaxed, calming pace
  dramatic: 1000,       // Dramatic reveals
  cinematic: 1200,      // Cinematic transitions
  epic: 1500,           // Epic, grand animations
  
  // Context-specific durations
  focus: 200,           // Focus state changes
  loading: 800,         // Loading animations
  success: 600,         // Success feedback
  error: 400,           // Error feedback
  warning: 350,         // Warning feedback
  
  // Page transitions
  pageEnter: 400,       // Page entrance
  pageExit: 300,        // Page exit
  modalEnter: 350,      // Modal appearance
  modalExit: 250,       // Modal dismissal
  
  // Content animations
  fadeIn: 300,          // Content fade in
  fadeOut: 200,         // Content fade out
  slideIn: 350,         // Content slide in
  slideOut: 250,        // Content slide out
  scaleIn: 300,         // Content scale in
  scaleOut: 200,        // Content scale out
} as const;

// Spring Configuration - For natural, physics-based animations
export const ExpressiveSprings = {
  // Gentle springs - for calm, peaceful interactions
  gentle: {
    damping: 20,
    stiffness: 120,
    mass: 1,
    overshootClamping: false,
    restDisplacementThreshold: 0.01,
    restSpeedThreshold: 0.01,
  },
  
  // Smooth springs - for standard interactions
  smooth: {
    damping: 15,
    stiffness: 150,
    mass: 1,
    overshootClamping: false,
    restDisplacementThreshold: 0.01,
    restSpeedThreshold: 0.01,
  },
  
  // Bouncy springs - for playful interactions
  bouncy: {
    damping: 8,
    stiffness: 100,
    mass: 1,
    overshootClamping: false,
    restDisplacementThreshold: 0.01,
    restSpeedThreshold: 0.01,
  },
  
  // Snappy springs - for quick, responsive interactions
  snappy: {
    damping: 25,
    stiffness: 200,
    mass: 0.8,
    overshootClamping: false,
    restDisplacementThreshold: 0.01,
    restSpeedThreshold: 0.01,
  },
  
  // Wobbly springs - for attention-grabbing animations
  wobbly: {
    damping: 7,
    stiffness: 180,
    mass: 1,
    overshootClamping: false,
    restDisplacementThreshold: 0.01,
    restSpeedThreshold: 0.01,
  },
  
  // Stiff springs - for precise, controlled animations
  stiff: {
    damping: 30,
    stiffness: 300,
    mass: 0.8,
    overshootClamping: true,
    restDisplacementThreshold: 0.01,
    restSpeedThreshold: 0.01,
  },
  
  // Context-specific springs
  focus: {
    damping: 20,
    stiffness: 250,
    mass: 0.9,
    overshootClamping: false,
    restDisplacementThreshold: 0.01,
    restSpeedThreshold: 0.01,
  },
  
  success: {
    damping: 12,
    stiffness: 150,
    mass: 1,
    overshootClamping: false,
    restDisplacementThreshold: 0.01,
    restSpeedThreshold: 0.01,
  },
  
  error: {
    damping: 18,
    stiffness: 200,
    mass: 0.9,
    overshootClamping: false,
    restDisplacementThreshold: 0.01,
    restSpeedThreshold: 0.01,
  },
  
  creative: {
    damping: 10,
    stiffness: 120,
    mass: 1.2,
    overshootClamping: false,
    restDisplacementThreshold: 0.01,
    restSpeedThreshold: 0.01,
  },
} as const;

// Animation Delays - For orchestrated sequences
export const ExpressiveDelays = {
  none: 0,
  tiny: 25,
  small: 50,
  medium: 100,
  large: 150,
  huge: 200,
  
  // Staggered delays for list animations
  stagger: {
    tiny: 25,
    small: 50,
    medium: 75,
    large: 100,
    huge: 125,
  },
  
  // Sequential delays
  sequence: {
    first: 0,
    second: 100,
    third: 200,
    fourth: 300,
    fifth: 400,
  },
} as const;

// Transform Origins - For different animation anchor points
export const ExpressiveOrigins = {
  center: { x: 0.5, y: 0.5 },
  topLeft: { x: 0, y: 0 },
  topCenter: { x: 0.5, y: 0 },
  topRight: { x: 1, y: 0 },
  centerLeft: { x: 0, y: 0.5 },
  centerRight: { x: 1, y: 0.5 },
  bottomLeft: { x: 0, y: 1 },
  bottomCenter: { x: 0.5, y: 1 },
  bottomRight: { x: 1, y: 1 },
} as const;

// Animation Types - Predefined animation configurations
export const ExpressiveAnimations = {
  // Fade animations
  fadeIn: {
    duration: ExpressiveDurations.fadeIn,
    easing: ExpressiveEasing.gentle,
    from: { opacity: 0 },
    to: { opacity: 1 },
  },
  
  fadeOut: {
    duration: ExpressiveDurations.fadeOut,
    easing: ExpressiveEasing.standard,
    from: { opacity: 1 },
    to: { opacity: 0 },
  },
  
  // Scale animations
  scaleIn: {
    duration: ExpressiveDurations.scaleIn,
    easing: ExpressiveEasing.bounce,
    from: { opacity: 0, transform: [{ scale: 0.8 }] },
    to: { opacity: 1, transform: [{ scale: 1 }] },
  },
  
  scaleOut: {
    duration: ExpressiveDurations.scaleOut,
    easing: ExpressiveEasing.standard,
    from: { opacity: 1, transform: [{ scale: 1 }] },
    to: { opacity: 0, transform: [{ scale: 0.8 }] },
  },
  
  // Slide animations
  slideInFromRight: {
    duration: ExpressiveDurations.slideIn,
    easing: ExpressiveEasing.decelerate,
    from: { transform: [{ translateX: 300 }] },
    to: { transform: [{ translateX: 0 }] },
  },
  
  slideInFromLeft: {
    duration: ExpressiveDurations.slideIn,
    easing: ExpressiveEasing.decelerate,
    from: { transform: [{ translateX: -300 }] },
    to: { transform: [{ translateX: 0 }] },
  },
  
  slideInFromBottom: {
    duration: ExpressiveDurations.slideIn,
    easing: ExpressiveEasing.decelerate,
    from: { transform: [{ translateY: 300 }] },
    to: { transform: [{ translateY: 0 }] },
  },
  
  slideInFromTop: {
    duration: ExpressiveDurations.slideIn,
    easing: ExpressiveEasing.decelerate,
    from: { transform: [{ translateY: -300 }] },
    to: { transform: [{ translateY: 0 }] },
  },
} as const;

// Emotional Motion Profiles - Animations that convey specific emotions
export const EmotionalMotion = {
  // Joy - Bouncy, playful animations
  joy: {
    easing: ExpressiveEasing.bounce,
    duration: ExpressiveDurations.medium,
    spring: ExpressiveSprings.bouncy,
    scale: { from: 0.9, to: 1.1, back: 1.0 },
  },

  // Calm - Gentle, flowing animations
  calm: {
    easing: ExpressiveEasing.gentle,
    duration: ExpressiveDurations.relaxed,
    spring: ExpressiveSprings.gentle,
    opacity: { from: 0, to: 1 },
  },

  // Focus - Sharp, precise animations
  focus: {
    easing: ExpressiveEasing.focus,
    duration: ExpressiveDurations.focus,
    spring: ExpressiveSprings.focus,
    transform: { scale: 1.02 },
  },

  // Energy - Quick, dynamic animations
  energy: {
    easing: ExpressiveEasing.energetic,
    duration: ExpressiveDurations.fast,
    spring: ExpressiveSprings.snappy,
    vibration: { intensity: 'medium' },
  },

  // Success - Satisfying, complete animations
  success: {
    easing: ExpressiveEasing.elastic,
    duration: ExpressiveDurations.success,
    spring: ExpressiveSprings.success,
    scale: { from: 1.0, to: 1.05, back: 1.0 },
  },

  // Error - Attention-grabbing, urgent animations
  error: {
    easing: ExpressiveEasing.dramatic,
    duration: ExpressiveDurations.error,
    spring: ExpressiveSprings.error,
    shake: { intensity: 'medium', direction: 'horizontal' },
  },

  // Creativity - Flowing, artistic animations
  creativity: {
    easing: ExpressiveEasing.creative,
    duration: ExpressiveDurations.cinematic,
    spring: ExpressiveSprings.creative,
    rotation: { from: -2, to: 2, back: 0 },
  },
} as const;

// Gesture Response Configurations
export const GestureMotion = {
  // Tap responses
  tap: {
    press: {
      scale: 0.95,
      duration: ExpressiveDurations.micro,
      easing: ExpressiveEasing.standard,
    },
    release: {
      scale: 1.0,
      duration: ExpressiveDurations.quick,
      easing: ExpressiveEasing.bounce,
    },
  },

  // Long press responses
  longPress: {
    start: {
      scale: 1.05,
      duration: ExpressiveDurations.medium,
      easing: ExpressiveEasing.gentle,
    },
    end: {
      scale: 1.0,
      duration: ExpressiveDurations.short,
      easing: ExpressiveEasing.standard,
    },
  },

  // Swipe responses
  swipe: {
    start: {
      scale: 0.98,
      duration: ExpressiveDurations.micro,
      easing: ExpressiveEasing.accelerate,
    },
    end: {
      scale: 1.0,
      duration: ExpressiveDurations.short,
      easing: ExpressiveEasing.decelerate,
    },
  },

  // Drag responses
  drag: {
    active: {
      scale: 1.02,
      elevation: 8,
      duration: ExpressiveDurations.quick,
      easing: ExpressiveEasing.standard,
    },
    inactive: {
      scale: 1.0,
      elevation: 0,
      duration: ExpressiveDurations.medium,
      easing: ExpressiveEasing.decelerate,
    },
  },
} as const;

// Loading Animation Configurations
export const LoadingMotion = {
  // Spinner configurations
  spinner: {
    duration: ExpressiveDurations.loading,
    easing: ExpressiveEasing.linear,
    rotation: { from: 0, to: 360 },
    repeat: -1, // Infinite
  },

  // Pulse configurations
  pulse: {
    duration: ExpressiveDurations.relaxed,
    easing: ExpressiveEasing.easeInOut,
    scale: { from: 1.0, to: 1.1, back: 1.0 },
    opacity: { from: 0.7, to: 1.0, back: 0.7 },
    repeat: -1,
  },

  // Breathing configurations
  breathing: {
    duration: ExpressiveDurations.epic,
    easing: ExpressiveEasing.gentle,
    scale: { from: 0.95, to: 1.05, back: 0.95 },
    repeat: -1,
  },

  // Wave configurations
  wave: {
    duration: ExpressiveDurations.dramatic,
    easing: ExpressiveEasing.easeInOut,
    translateY: { from: 0, to: -10, back: 0 },
    stagger: ExpressiveDelays.stagger.medium,
    repeat: -1,
  },
} as const;

// Page Transition Configurations
export const PageTransitions = {
  // Slide transitions
  slideLeft: {
    enter: {
      duration: ExpressiveDurations.pageEnter,
      easing: ExpressiveEasing.decelerate,
      from: { transform: [{ translateX: 300 }] },
      to: { transform: [{ translateX: 0 }] },
    },
    exit: {
      duration: ExpressiveDurations.pageExit,
      easing: ExpressiveEasing.accelerate,
      from: { transform: [{ translateX: 0 }] },
      to: { transform: [{ translateX: -300 }] },
    },
  },

  slideRight: {
    enter: {
      duration: ExpressiveDurations.pageEnter,
      easing: ExpressiveEasing.decelerate,
      from: { transform: [{ translateX: -300 }] },
      to: { transform: [{ translateX: 0 }] },
    },
    exit: {
      duration: ExpressiveDurations.pageExit,
      easing: ExpressiveEasing.accelerate,
      from: { transform: [{ translateX: 0 }] },
      to: { transform: [{ translateX: 300 }] },
    },
  },

  // Fade transitions
  fade: {
    enter: {
      duration: ExpressiveDurations.pageEnter,
      easing: ExpressiveEasing.gentle,
      from: { opacity: 0 },
      to: { opacity: 1 },
    },
    exit: {
      duration: ExpressiveDurations.pageExit,
      easing: ExpressiveEasing.standard,
      from: { opacity: 1 },
      to: { opacity: 0 },
    },
  },

  // Scale transitions
  scale: {
    enter: {
      duration: ExpressiveDurations.pageEnter,
      easing: ExpressiveEasing.bounce,
      from: { opacity: 0, transform: [{ scale: 0.9 }] },
      to: { opacity: 1, transform: [{ scale: 1 }] },
    },
    exit: {
      duration: ExpressiveDurations.pageExit,
      easing: ExpressiveEasing.standard,
      from: { opacity: 1, transform: [{ scale: 1 }] },
      to: { opacity: 0, transform: [{ scale: 1.1 }] },
    },
  },
} as const;

// Utility Functions
export const MotionUtils = {
  // Create staggered animation delays
  createStaggerDelay: (index: number, baseDelay: number = ExpressiveDelays.stagger.medium) => {
    return index * baseDelay;
  },

  // Get easing by emotion
  getEmotionalEasing: (emotion: keyof typeof EmotionalMotion) => {
    return EmotionalMotion[emotion].easing;
  },

  // Get duration by context
  getContextualDuration: (context: 'micro' | 'short' | 'medium' | 'long' | 'epic') => {
    const durationMap = {
      micro: ExpressiveDurations.micro,
      short: ExpressiveDurations.short,
      medium: ExpressiveDurations.medium,
      long: ExpressiveDurations.slow,
      epic: ExpressiveDurations.epic,
    };
    return durationMap[context];
  },

  // Get spring by feel
  getSpringByFeel: (feel: 'gentle' | 'smooth' | 'bouncy' | 'snappy' | 'wobbly' | 'stiff') => {
    return ExpressiveSprings[feel];
  },

  // Create animation sequence
  createSequence: (animations: any[], baseDelay: number = ExpressiveDelays.medium) => {
    return animations.map((animation, index) => ({
      ...animation,
      delay: index * baseDelay,
    }));
  },

  // Interpolate between values for custom animations
  interpolate: (
    inputRange: number[],
    outputRange: number[],
    extrapolate: 'extend' | 'clamp' | 'identity' = 'clamp'
  ) => {
    return { inputRange, outputRange, extrapolate };
  },

  // Create responsive animation based on screen size
  createResponsiveAnimation: (
    baseAnimation: any,
    screenWidth: number,
    breakpoints = { small: 320, medium: 768, large: 1024 }
  ) => {
    let scale = 1;
    if (screenWidth <= breakpoints.small) scale = 0.8;
    else if (screenWidth <= breakpoints.medium) scale = 0.9;
    else if (screenWidth >= breakpoints.large) scale = 1.1;

    return {
      ...baseAnimation,
      duration: Math.round(baseAnimation.duration * scale),
    };
  },
} as const;
