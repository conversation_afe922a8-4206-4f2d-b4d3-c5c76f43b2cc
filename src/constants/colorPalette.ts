/**
 * Material 3 Expressive Color Palette System
 * Provides predefined color palettes and utilities for color management
 */

export interface ColorInfo {
  name: string;
  hex: string;
  rgb: { r: number; g: number; b: number };
  hsl: { h: number; s: number; l: number };
  category: string;
  accessibility: {
    contrastRatio: number;
    wcagLevel: 'AA' | 'AAA' | 'FAIL';
  };
}

export interface ColorPalette {
  name: string;
  description: string;
  colors: ColorInfo[];
  category: 'primary' | 'secondary' | 'tertiary' | 'neutral' | 'semantic' | 'custom';
}

// Material 3 Expressive Primary Colors
export const MATERIAL_PRIMARY_COLORS: ColorInfo[] = [
  {
    name: 'Violet',
    hex: '#6750A4',
    rgb: { r: 103, g: 80, b: 164 },
    hsl: { h: 256, s: 34, l: 48 },
    category: 'primary',
    accessibility: { contrastRatio: 4.5, wcagLevel: 'AA' },
  },
  {
    name: 'Deep Purple',
    hex: '#7C4DFF',
    rgb: { r: 124, g: 77, b: 255 },
    hsl: { h: 256, s: 100, l: 65 },
    category: 'primary',
    accessibility: { contrastRatio: 4.2, wcagLevel: 'AA' },
  },
  {
    name: 'Indigo',
    hex: '#3F51B5',
    rgb: { r: 63, g: 81, b: 181 },
    hsl: { h: 231, s: 48, l: 48 },
    category: 'primary',
    accessibility: { contrastRatio: 5.1, wcagLevel: 'AA' },
  },
  {
    name: 'Blue',
    hex: '#2196F3',
    rgb: { r: 33, g: 150, b: 243 },
    hsl: { h: 207, s: 90, l: 54 },
    category: 'primary',
    accessibility: { contrastRatio: 3.8, wcagLevel: 'AA' },
  },
];

// Material 3 Expressive Secondary Colors
export const MATERIAL_SECONDARY_COLORS: ColorInfo[] = [
  {
    name: 'Cyan',
    hex: '#00BCD4',
    rgb: { r: 0, g: 188, b: 212 },
    hsl: { h: 187, s: 100, l: 42 },
    category: 'secondary',
    accessibility: { contrastRatio: 4.1, wcagLevel: 'AA' },
  },
  {
    name: 'Teal',
    hex: '#009688',
    rgb: { r: 0, g: 150, b: 136 },
    hsl: { h: 174, s: 100, l: 29 },
    category: 'secondary',
    accessibility: { contrastRatio: 5.2, wcagLevel: 'AA' },
  },
  {
    name: 'Green',
    hex: '#4CAF50',
    rgb: { r: 76, g: 175, b: 80 },
    hsl: { h: 122, s: 39, l: 49 },
    category: 'secondary',
    accessibility: { contrastRatio: 4.3, wcagLevel: 'AA' },
  },
  {
    name: 'Light Green',
    hex: '#8BC34A',
    rgb: { r: 139, g: 195, b: 74 },
    hsl: { h: 88, s: 50, l: 53 },
    category: 'secondary',
    accessibility: { contrastRatio: 3.9, wcagLevel: 'AA' },
  },
];

// Material 3 Expressive Tertiary Colors
export const MATERIAL_TERTIARY_COLORS: ColorInfo[] = [
  {
    name: 'Lime',
    hex: '#CDDC39',
    rgb: { r: 205, g: 220, b: 57 },
    hsl: { h: 66, s: 70, l: 54 },
    category: 'tertiary',
    accessibility: { contrastRatio: 2.8, wcagLevel: 'FAIL' },
  },
  {
    name: 'Yellow',
    hex: '#FFEB3B',
    rgb: { r: 255, g: 235, b: 59 },
    hsl: { h: 54, s: 100, l: 62 },
    category: 'tertiary',
    accessibility: { contrastRatio: 2.1, wcagLevel: 'FAIL' },
  },
  {
    name: 'Amber',
    hex: '#FFC107',
    rgb: { r: 255, g: 193, b: 7 },
    hsl: { h: 45, s: 100, l: 51 },
    category: 'tertiary',
    accessibility: { contrastRatio: 2.9, wcagLevel: 'FAIL' },
  },
  {
    name: 'Orange',
    hex: '#FF9800',
    rgb: { r: 255, g: 152, b: 0 },
    hsl: { h: 36, s: 100, l: 50 },
    category: 'tertiary',
    accessibility: { contrastRatio: 3.2, wcagLevel: 'AA' },
  },
];

// Material 3 Expressive Warm Colors
export const MATERIAL_WARM_COLORS: ColorInfo[] = [
  {
    name: 'Deep Orange',
    hex: '#FF5722',
    rgb: { r: 255, g: 87, b: 34 },
    hsl: { h: 14, s: 100, l: 57 },
    category: 'tertiary',
    accessibility: { contrastRatio: 4.1, wcagLevel: 'AA' },
  },
  {
    name: 'Red',
    hex: '#F44336',
    rgb: { r: 244, g: 67, b: 54 },
    hsl: { h: 4, s: 90, l: 58 },
    category: 'tertiary',
    accessibility: { contrastRatio: 4.4, wcagLevel: 'AA' },
  },
  {
    name: 'Pink',
    hex: '#E91E63',
    rgb: { r: 233, g: 30, b: 99 },
    hsl: { h: 340, s: 82, l: 52 },
    category: 'tertiary',
    accessibility: { contrastRatio: 4.8, wcagLevel: 'AA' },
  },
  {
    name: 'Purple',
    hex: '#9C27B0',
    rgb: { r: 156, g: 39, b: 176 },
    hsl: { h: 291, s: 64, l: 42 },
    category: 'tertiary',
    accessibility: { contrastRatio: 5.3, wcagLevel: 'AA' },
  },
];

// Semantic Colors
export const SEMANTIC_COLORS: ColorInfo[] = [
  {
    name: 'Success',
    hex: '#4CAF50',
    rgb: { r: 76, g: 175, b: 80 },
    hsl: { h: 122, s: 39, l: 49 },
    category: 'semantic',
    accessibility: { contrastRatio: 4.3, wcagLevel: 'AA' },
  },
  {
    name: 'Warning',
    hex: '#FF9800',
    rgb: { r: 255, g: 152, b: 0 },
    hsl: { h: 36, s: 100, l: 50 },
    category: 'semantic',
    accessibility: { contrastRatio: 3.2, wcagLevel: 'AA' },
  },
  {
    name: 'Error',
    hex: '#F44336',
    rgb: { r: 244, g: 67, b: 54 },
    hsl: { h: 4, s: 90, l: 58 },
    category: 'semantic',
    accessibility: { contrastRatio: 4.4, wcagLevel: 'AA' },
  },
  {
    name: 'Info',
    hex: '#2196F3',
    rgb: { r: 33, g: 150, b: 243 },
    hsl: { h: 207, s: 90, l: 54 },
    category: 'semantic',
    accessibility: { contrastRatio: 3.8, wcagLevel: 'AA' },
  },
];

// Predefined Color Palettes
export const COLOR_PALETTES: ColorPalette[] = [
  {
    name: 'Material Primary',
    description: 'Core Material 3 primary colors for main actions and branding',
    colors: MATERIAL_PRIMARY_COLORS,
    category: 'primary',
  },
  {
    name: 'Material Secondary',
    description: 'Secondary colors for supporting elements and accents',
    colors: MATERIAL_SECONDARY_COLORS,
    category: 'secondary',
  },
  {
    name: 'Material Tertiary',
    description: 'Tertiary colors for additional variety and expression',
    colors: MATERIAL_TERTIARY_COLORS,
    category: 'tertiary',
  },
  {
    name: 'Warm Tones',
    description: 'Warm, energetic colors for motivation and focus',
    colors: MATERIAL_WARM_COLORS,
    category: 'tertiary',
  },
  {
    name: 'Semantic',
    description: 'Colors with specific meanings for status and feedback',
    colors: SEMANTIC_COLORS,
    category: 'semantic',
  },
];

// All colors combined for easy access
export const ALL_COLORS: ColorInfo[] = [
  ...MATERIAL_PRIMARY_COLORS,
  ...MATERIAL_SECONDARY_COLORS,
  ...MATERIAL_TERTIARY_COLORS,
  ...MATERIAL_WARM_COLORS,
  ...SEMANTIC_COLORS,
];

// Subject-specific color recommendations
export const SUBJECT_COLOR_RECOMMENDATIONS: Record<string, string[]> = {
  mathematics: ['#6750A4', '#3F51B5', '#2196F3'],
  science: ['#009688', '#4CAF50', '#00BCD4'],
  language: ['#E91E63', '#9C27B0', '#7C4DFF'],
  history: ['#FF5722', '#F44336', '#FF9800'],
  art: ['#CDDC39', '#FFEB3B', '#FFC107'],
  music: ['#9C27B0', '#E91E63', '#7C4DFF'],
  sports: ['#4CAF50', '#8BC34A', '#009688'],
  technology: ['#2196F3', '#00BCD4', '#6750A4'],
};

// Utility functions
export const ColorUtils = {
  /**
   * Convert hex to RGB
   */
  hexToRgb: (hex: string): { r: number; g: number; b: number } | null => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : null;
  },

  /**
   * Convert RGB to hex
   */
  rgbToHex: (r: number, g: number, b: number): string => {
    return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
  },

  /**
   * Convert hex to HSL
   */
  hexToHsl: (hex: string): { h: number; s: number; l: number } | null => {
    const rgb = ColorUtils.hexToRgb(hex);
    if (!rgb) return null;

    const { r, g, b } = rgb;
    const rNorm = r / 255;
    const gNorm = g / 255;
    const bNorm = b / 255;

    const max = Math.max(rNorm, gNorm, bNorm);
    const min = Math.min(rNorm, gNorm, bNorm);
    let h = 0;
    let s = 0;
    const l = (max + min) / 2;

    if (max !== min) {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

      switch (max) {
        case rNorm:
          h = (gNorm - bNorm) / d + (gNorm < bNorm ? 6 : 0);
          break;
        case gNorm:
          h = (bNorm - rNorm) / d + 2;
          break;
        case bNorm:
          h = (rNorm - gNorm) / d + 4;
          break;
      }
      h /= 6;
    }

    return {
      h: Math.round(h * 360),
      s: Math.round(s * 100),
      l: Math.round(l * 100),
    };
  },

  /**
   * Calculate contrast ratio between two colors
   */
  getContrastRatio: (color1: string, color2: string = '#FFFFFF'): number => {
    const getLuminance = (hex: string): number => {
      const rgb = ColorUtils.hexToRgb(hex);
      if (!rgb) return 0;

      const { r, g, b } = rgb;
      const [rNorm, gNorm, bNorm] = [r, g, b].map((c) => {
        c = c / 255;
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
      });

      return 0.2126 * rNorm + 0.7152 * gNorm + 0.0722 * bNorm;
    };

    const lum1 = getLuminance(color1);
    const lum2 = getLuminance(color2);
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);

    return (brightest + 0.05) / (darkest + 0.05);
  },

  /**
   * Get WCAG compliance level
   */
  getWcagLevel: (contrastRatio: number): 'AA' | 'AAA' | 'FAIL' => {
    if (contrastRatio >= 7) return 'AAA';
    if (contrastRatio >= 4.5) return 'AA';
    return 'FAIL';
  },

  /**
   * Generate color variations (lighter/darker)
   */
  generateVariations: (hex: string): { lighter: string; darker: string } => {
    const hsl = ColorUtils.hexToHsl(hex);
    if (!hsl) return { lighter: hex, darker: hex };

    const lighter = {
      ...hsl,
      l: Math.min(hsl.l + 20, 90),
    };

    const darker = {
      ...hsl,
      l: Math.max(hsl.l - 20, 10),
    };

    return {
      lighter: ColorUtils.hslToHex(lighter.h, lighter.s, lighter.l),
      darker: ColorUtils.hslToHex(darker.h, darker.s, darker.l),
    };
  },

  /**
   * Convert HSL to hex
   */
  hslToHex: (h: number, s: number, l: number): string => {
    const hNorm = h / 360;
    const sNorm = s / 100;
    const lNorm = l / 100;

    const hue2rgb = (p: number, q: number, t: number): number => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1 / 6) return p + (q - p) * 6 * t;
      if (t < 1 / 2) return q;
      if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
      return p;
    };

    let r: number, g: number, b: number;

    if (sNorm === 0) {
      r = g = b = lNorm;
    } else {
      const q = lNorm < 0.5 ? lNorm * (1 + sNorm) : lNorm + sNorm - lNorm * sNorm;
      const p = 2 * lNorm - q;
      r = hue2rgb(p, q, hNorm + 1 / 3);
      g = hue2rgb(p, q, hNorm);
      b = hue2rgb(p, q, hNorm - 1 / 3);
    }

    return ColorUtils.rgbToHex(
      Math.round(r * 255),
      Math.round(g * 255),
      Math.round(b * 255)
    );
  },

  /**
   * Get recommended colors for a subject
   */
  getSubjectColors: (subjectName: string): string[] => {
    const normalizedName = subjectName.toLowerCase();
    
    // Check for exact matches first
    for (const [key, colors] of Object.entries(SUBJECT_COLOR_RECOMMENDATIONS)) {
      if (normalizedName.includes(key)) {
        return colors;
      }
    }

    // Return default palette if no match
    return MATERIAL_PRIMARY_COLORS.map(color => color.hex);
  },

  /**
   * Find the closest color from palette
   */
  findClosestColor: (targetHex: string, palette: ColorInfo[] = ALL_COLORS): ColorInfo => {
    const targetRgb = ColorUtils.hexToRgb(targetHex);
    if (!targetRgb) return palette[0];

    let closestColor = palette[0];
    let minDistance = Infinity;

    palette.forEach((color) => {
      const colorRgb = color.rgb;
      const distance = Math.sqrt(
        Math.pow(targetRgb.r - colorRgb.r, 2) +
        Math.pow(targetRgb.g - colorRgb.g, 2) +
        Math.pow(targetRgb.b - colorRgb.b, 2)
      );

      if (distance < minDistance) {
        minDistance = distance;
        closestColor = color;
      }
    });

    return closestColor;
  },
};
