// import { GoogleSignin, statusCodes } from '@react-native-google-signin/google-signin';
import { Platform } from 'react-native';
import { supabase } from '@/services/supabase/client';

// Configure Google Sign-In - TEMPORARILY DISABLED
export const configureGoogleSignIn = () => {
  console.warn('Google Sign-in is temporarily disabled. Please configure Google Services files to enable.');
  return;

  // const webClientId = process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID;
  // const iosClientId = process.env.EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID;
  // const androidClientId = process.env.EXPO_PUBLIC_GOOGLE_ANDROID_CLIENT_ID;

  // let clientId: string;

  // if (Platform.OS === 'ios') {
  //   clientId = iosClientId || webClientId || '';
  // } else if (Platform.OS === 'android') {
  //   clientId = androidClientId || webClientId || '';
  // } else {
  //   clientId = webClientId || '';
  // }

  // if (!clientId) {
  //   console.warn('Google OAuth client ID not configured');
  //   return;
  // }

  // GoogleSignin.configure({
  //   webClientId: webClientId,
  //   iosClientId: iosClientId,
  //   offlineAccess: true,
  //   hostedDomain: '',
  //   forceCodeForRefreshToken: true,
  // });
};

// Sign in with Google - TEMPORARILY DISABLED
export const signInWithGoogle = async () => {
  throw new Error('Google Sign-in is temporarily disabled. Please configure Google Services files to enable.');

  // try {
  //   // Check if device supports Google Play Services
  //   await GoogleSignin.hasPlayServices({
  //     showPlayServicesUpdateDialog: true,
  //   });

  //   // Get user info from Google
  //   const userInfo = await GoogleSignin.signIn();

  //   if (!userInfo.data?.idToken) {
  //     throw new Error('No ID token received from Google');
  //   }

  //   // Sign in to Supabase with Google ID token
  //   const { data, error } = await supabase.auth.signInWithIdToken({
  //     provider: 'google',
  //     token: userInfo.data.idToken,
  //   });

  //   if (error) {
  //     throw error;
  //   }

  //   return {
  //     user: data.user,
  //     session: data.session,
  //   };
  // } catch (error: any) {
  //   console.error('Google Sign-In Error:', error);

  //   if (error.code === statusCodes.SIGN_IN_CANCELLED) {
  //     throw new Error('Sign in was cancelled');
  //   } else if (error.code === statusCodes.IN_PROGRESS) {
  //     throw new Error('Sign in is already in progress');
  //   } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
  //     throw new Error('Google Play Services not available');
  //   } else {
  //     throw new Error(error.message || 'Failed to sign in with Google');
  //   }
  // }
};

// Sign out from Google - TEMPORARILY DISABLED
export const signOutFromGoogle = async () => {
  console.warn('Google Sign-out is temporarily disabled.');
  return;

  // try {
  //   await GoogleSignin.signOut();
  // } catch (error) {
  //   console.error('Error signing out from Google:', error);
  // }
};

// Check if user is signed in to Google
export const isSignedInToGoogle = async () => {
  try {
    const userInfo = await GoogleSignin.getCurrentUser();
    return !!userInfo;
  } catch (error) {
    console.error('Error checking Google sign-in status:', error);
    return false;
  }
};

// Get current Google user
export const getCurrentGoogleUser = async () => {
  try {
    const userInfo = await GoogleSignin.signInSilently();
    return userInfo;
  } catch (error) {
    console.error('Error getting current Google user:', error);
    return null;
  }
};
