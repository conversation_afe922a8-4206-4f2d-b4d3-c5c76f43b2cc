import { supabase } from './client';
import { Database } from '../../types/database';
import { Task } from '../../stores/tasksStore';

type TaskRow = Database['public']['Tables']['todos']['Row'];
type TaskInsert = Database['public']['Tables']['todos']['Insert'];
type TaskUpdate = Database['public']['Tables']['todos']['Update'];

export class TasksService {
  /**
   * Map database row to Task interface
   */
  private mapRowToTask(row: TaskRow): Task {
    return {
      id: row.id,
      user_id: row.user_id,
      group_id: row.group_id,
      title: row.title,
      description: row.description,
      status: row.status,
      priority: row.priority,
      column_id: row.column_id,
      due_date: row.due_date,
      assigned_to: row.assigned_to,
      assigned_to_photo_url: row.assigned_to_photo_url,
      created_at: row.created_at,
      updated_at: row.updated_at,
    };
  }

  /**
   * Get all tasks for a user
   */
  async getUserTasks(userId: string, groupId?: string): Promise<Task[]> {
    try {
      let query = supabase
        .from('todos')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (groupId) {
        query = query.eq('group_id', groupId);
      } else {
        query = query.is('group_id', null);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching tasks:', error);
        throw error;
      }

      return data.map(this.mapRowToTask);
    } catch (error) {
      console.error('Failed to get user tasks:', error);
      throw error;
    }
  }

  /**
   * Get a single task by ID
   */
  async getTaskById(taskId: string, userId: string): Promise<Task | null> {
    try {
      const { data, error } = await supabase
        .from('todos')
        .select('*')
        .eq('id', taskId)
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Task not found
        }
        console.error('Error fetching task:', error);
        throw error;
      }

      return this.mapRowToTask(data);
    } catch (error) {
      console.error('Failed to get task by ID:', error);
      throw error;
    }
  }

  /**
   * Create a new task
   */
  async createTask(
    userId: string,
    taskData: Omit<Task, 'id' | 'user_id' | 'created_at' | 'updated_at'>
  ): Promise<Task> {
    try {
      const insertData: TaskInsert = {
        user_id: userId,
        title: taskData.title.trim(),
        description: taskData.description?.trim() || null,
        status: taskData.status || 'todo',
        priority: taskData.priority || 'medium',
        column_id: taskData.column_id,
        due_date: taskData.due_date,
        assigned_to: taskData.assigned_to,
        assigned_to_photo_url: taskData.assigned_to_photo_url,
        group_id: taskData.group_id,
      };

      const { data, error } = await supabase
        .from('todos')
        .insert(insertData)
        .select()
        .single();

      if (error) {
        console.error('Error creating task:', error);
        throw error;
      }

      return this.mapRowToTask(data);
    } catch (error) {
      console.error('Failed to create task:', error);
      throw error;
    }
  }

  /**
   * Update an existing task
   */
  async updateTask(
    taskId: string,
    userId: string,
    updates: Partial<Omit<Task, 'id' | 'user_id' | 'created_at' | 'updated_at'>>
  ): Promise<Task> {
    try {
      const updateData: TaskUpdate = {
        ...updates,
        updated_at: new Date().toISOString(),
      };

      // Remove undefined values
      Object.keys(updateData).forEach(key => {
        if (updateData[key as keyof TaskUpdate] === undefined) {
          delete updateData[key as keyof TaskUpdate];
        }
      });

      const { data, error } = await supabase
        .from('todos')
        .update(updateData)
        .eq('id', taskId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        console.error('Error updating task:', error);
        throw error;
      }

      return this.mapRowToTask(data);
    } catch (error) {
      console.error('Failed to update task:', error);
      throw error;
    }
  }

  /**
   * Delete a task
   */
  async deleteTask(taskId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('todos')
        .delete()
        .eq('id', taskId)
        .eq('user_id', userId);

      if (error) {
        console.error('Error deleting task:', error);
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Failed to delete task:', error);
      return false;
    }
  }

  /**
   * Bulk update tasks
   */
  async bulkUpdateTasks(
    taskIds: string[],
    userId: string,
    updates: Partial<Omit<Task, 'id' | 'user_id' | 'created_at' | 'updated_at'>>
  ): Promise<Task[]> {
    try {
      const updateData: TaskUpdate = {
        ...updates,
        updated_at: new Date().toISOString(),
      };

      // Remove undefined values
      Object.keys(updateData).forEach(key => {
        if (updateData[key as keyof TaskUpdate] === undefined) {
          delete updateData[key as keyof TaskUpdate];
        }
      });

      const { data, error } = await supabase
        .from('todos')
        .update(updateData)
        .in('id', taskIds)
        .eq('user_id', userId)
        .select();

      if (error) {
        console.error('Error bulk updating tasks:', error);
        throw error;
      }

      return data.map(this.mapRowToTask);
    } catch (error) {
      console.error('Failed to bulk update tasks:', error);
      throw error;
    }
  }

  /**
   * Duplicate a task
   */
  async duplicateTask(taskId: string, userId: string): Promise<Task> {
    try {
      // First get the original task
      const originalTask = await this.getTaskById(taskId, userId);
      if (!originalTask) {
        throw new Error('Task not found');
      }

      // Create a new task with the same data but different title
      const duplicateData: Omit<Task, 'id' | 'user_id' | 'created_at' | 'updated_at'> = {
        title: `${originalTask.title} (Copy)`,
        description: originalTask.description,
        status: 'todo', // Reset status to todo
        priority: originalTask.priority,
        column_id: 'todo', // Reset to todo column
        due_date: originalTask.due_date,
        assigned_to: originalTask.assigned_to,
        assigned_to_photo_url: originalTask.assigned_to_photo_url,
        group_id: originalTask.group_id,
      };

      return await this.createTask(userId, duplicateData);
    } catch (error) {
      console.error('Failed to duplicate task:', error);
      throw error;
    }
  }

  /**
   * Get tasks by status
   */
  async getTasksByStatus(
    userId: string,
    status: Task['status'],
    groupId?: string
  ): Promise<Task[]> {
    try {
      let query = supabase
        .from('todos')
        .select('*')
        .eq('user_id', userId)
        .eq('status', status)
        .order('created_at', { ascending: false });

      if (groupId) {
        query = query.eq('group_id', groupId);
      } else {
        query = query.is('group_id', null);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching tasks by status:', error);
        throw error;
      }

      return data.map(this.mapRowToTask);
    } catch (error) {
      console.error('Failed to get tasks by status:', error);
      throw error;
    }
  }

  /**
   * Get overdue tasks
   */
  async getOverdueTasks(userId: string, groupId?: string): Promise<Task[]> {
    try {
      const now = new Date().toISOString();

      let query = supabase
        .from('todos')
        .select('*')
        .eq('user_id', userId)
        .neq('status', 'done')
        .neq('status', 'archived')
        .not('due_date', 'is', null)
        .lt('due_date', now)
        .order('due_date', { ascending: true });

      if (groupId) {
        query = query.eq('group_id', groupId);
      } else {
        query = query.is('group_id', null);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching overdue tasks:', error);
        throw error;
      }

      return data.map(this.mapRowToTask);
    } catch (error) {
      console.error('Failed to get overdue tasks:', error);
      throw error;
    }
  }

  /**
   * Get tasks assigned to a specific user
   */
  async getTasksAssignedToUser(
    userId: string,
    assignedUserId: string,
    groupId?: string
  ): Promise<Task[]> {
    try {
      let query = supabase
        .from('todos')
        .select('*')
        .eq('user_id', userId)
        .eq('assigned_to', assignedUserId)
        .order('created_at', { ascending: false });

      if (groupId) {
        query = query.eq('group_id', groupId);
      } else {
        query = query.is('group_id', null);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching assigned tasks:', error);
        throw error;
      }

      return data.map(this.mapRowToTask);
    } catch (error) {
      console.error('Failed to get assigned tasks:', error);
      throw error;
    }
  }

  /**
   * Search tasks by title or description
   */
  async searchTasks(
    userId: string,
    searchQuery: string,
    groupId?: string
  ): Promise<Task[]> {
    try {
      let query = supabase
        .from('todos')
        .select('*')
        .eq('user_id', userId)
        .or(`title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`)
        .order('created_at', { ascending: false });

      if (groupId) {
        query = query.eq('group_id', groupId);
      } else {
        query = query.is('group_id', null);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error searching tasks:', error);
        throw error;
      }

      return data.map(this.mapRowToTask);
    } catch (error) {
      console.error('Failed to search tasks:', error);
      throw error;
    }
  }

  /**
   * Get task statistics
   */
  async getTaskStats(userId: string, groupId?: string): Promise<{
    total: number;
    todo: number;
    inProgress: number;
    done: number;
    overdue: number;
  }> {
    try {
      let baseQuery = supabase
        .from('todos')
        .select('status, due_date')
        .eq('user_id', userId);

      if (groupId) {
        baseQuery = baseQuery.eq('group_id', groupId);
      } else {
        baseQuery = baseQuery.is('group_id', null);
      }

      const { data, error } = await baseQuery;

      if (error) {
        console.error('Error fetching task stats:', error);
        throw error;
      }

      const now = new Date().toISOString();
      const stats = {
        total: data.length,
        todo: 0,
        inProgress: 0,
        done: 0,
        overdue: 0,
      };

      data.forEach(task => {
        switch (task.status) {
          case 'todo':
            stats.todo++;
            break;
          case 'in_progress':
            stats.inProgress++;
            break;
          case 'done':
            stats.done++;
            break;
        }

        // Check if task is overdue
        if (
          task.due_date &&
          task.status !== 'done' &&
          task.status !== 'archived' &&
          task.due_date < now
        ) {
          stats.overdue++;
        }
      });

      return stats;
    } catch (error) {
      console.error('Failed to get task stats:', error);
      return {
        total: 0,
        todo: 0,
        inProgress: 0,
        done: 0,
        overdue: 0,
      };
    }
  }

  /**
   * Subscribe to real-time changes for tasks
   */
  subscribeToTasks(
    userId: string,
    callback: (payload: any) => void,
    groupId?: string
  ) {
    const channelName = groupId ? `tasks_${groupId}` : `tasks_${userId}`;

    let filter = `user_id=eq.${userId}`;
    if (groupId) {
      filter += `,group_id=eq.${groupId}`;
    } else {
      filter += `,group_id=is.null`;
    }

    return supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'todos',
          filter,
        },
        callback
      )
      .subscribe();
  }

  /**
   * Unsubscribe from real-time changes
   */
  unsubscribeFromTasks(subscription: any) {
    if (subscription) {
      supabase.removeChannel(subscription);
    }
  }

  /**
   * Bulk create tasks
   */
  async bulkCreateTasks(
    userId: string,
    tasksData: Array<Omit<Task, 'id' | 'user_id' | 'created_at' | 'updated_at'>>
  ): Promise<Task[]> {
    try {
      const insertData: TaskInsert[] = tasksData.map(task => ({
        user_id: userId,
        title: task.title.trim(),
        description: task.description?.trim() || null,
        status: task.status || 'todo',
        priority: task.priority || 'medium',
        column_id: task.column_id,
        due_date: task.due_date,
        assigned_to: task.assigned_to,
        assigned_to_photo_url: task.assigned_to_photo_url,
        group_id: task.group_id,
      }));

      const { data, error } = await supabase
        .from('todos')
        .insert(insertData)
        .select();

      if (error) {
        console.error('Error bulk creating tasks:', error);
        throw error;
      }

      return data.map(this.mapRowToTask);
    } catch (error) {
      console.error('Failed to bulk create tasks:', error);
      throw error;
    }
  }

  /**
   * Archive completed tasks older than specified days
   */
  async archiveOldCompletedTasks(
    userId: string,
    daysOld: number = 30,
    groupId?: string
  ): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);
      const cutoffISO = cutoffDate.toISOString();

      let query = supabase
        .from('todos')
        .update({ status: 'archived', updated_at: new Date().toISOString() })
        .eq('user_id', userId)
        .eq('status', 'done')
        .lt('updated_at', cutoffISO);

      if (groupId) {
        query = query.eq('group_id', groupId);
      } else {
        query = query.is('group_id', null);
      }

      const { data, error } = await query.select();

      if (error) {
        console.error('Error archiving old tasks:', error);
        throw error;
      }

      return data?.length || 0;
    } catch (error) {
      console.error('Failed to archive old completed tasks:', error);
      return 0;
    }
  }
}

// Create and export a singleton instance
export const tasksService = new TasksService();
