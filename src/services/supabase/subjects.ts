import { supabase } from './client';
import { Database } from '../../types/database';
import { Subject } from '../../stores/subjectStore';

type SubjectRow = Database['public']['Tables']['userSubjects']['Row'];
type SubjectInsert = Database['public']['Tables']['userSubjects']['Insert'];
type SubjectUpdate = Database['public']['Tables']['userSubjects']['Update'];

export class SubjectService {
  /**
   * Get all subjects for the current user
   */
  async getSubjects(userId: string): Promise<Subject[]> {
    try {
      const { data, error } = await supabase
        .from('userSubjects')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching subjects:', error);
        throw error;
      }

      return data.map(this.mapRowToSubject);
    } catch (error) {
      console.error('Failed to get subjects:', error);
      throw error;
    }
  }

  /**
   * Get a specific subject by ID
   */
  async getSubject(id: string, userId: string): Promise<Subject | null> {
    try {
      const { data, error } = await supabase
        .from('userSubjects')
        .select('*')
        .eq('id', id)
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Subject not found
        }
        console.error('Error fetching subject:', error);
        throw error;
      }

      return this.mapRowToSubject(data);
    } catch (error) {
      console.error('Failed to get subject:', error);
      throw error;
    }
  }

  /**
   * Create a new subject
   */
  async createSubject(
    userId: string,
    name: string,
    color: string
  ): Promise<Subject> {
    try {
      const subjectData: SubjectInsert = {
        user_id: userId,
        name: name.trim(),
        color,
      };

      const { data, error } = await supabase
        .from('userSubjects')
        .insert(subjectData)
        .select()
        .single();

      if (error) {
        console.error('Error creating subject:', error);
        throw error;
      }

      return this.mapRowToSubject(data);
    } catch (error) {
      console.error('Failed to create subject:', error);
      throw error;
    }
  }

  /**
   * Update an existing subject
   */
  async updateSubject(
    id: string,
    userId: string,
    updates: Partial<Pick<Subject, 'name' | 'color'>>
  ): Promise<Subject> {
    try {
      const updateData: SubjectUpdate = {
        ...updates,
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('userSubjects')
        .update(updateData)
        .eq('id', id)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        console.error('Error updating subject:', error);
        throw error;
      }

      return this.mapRowToSubject(data);
    } catch (error) {
      console.error('Failed to update subject:', error);
      throw error;
    }
  }

  /**
   * Delete a subject
   */
  async deleteSubject(id: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('userSubjects')
        .delete()
        .eq('id', id)
        .eq('user_id', userId);

      if (error) {
        console.error('Error deleting subject:', error);
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Failed to delete subject:', error);
      throw error;
    }
  }

  /**
   * Check if a subject name already exists for the user
   */
  async subjectNameExists(
    userId: string,
    name: string,
    excludeId?: string
  ): Promise<boolean> {
    try {
      let query = supabase
        .from('userSubjects')
        .select('id')
        .eq('user_id', userId)
        .ilike('name', name.trim());

      if (excludeId) {
        query = query.neq('id', excludeId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error checking subject name:', error);
        throw error;
      }

      return data.length > 0;
    } catch (error) {
      console.error('Failed to check subject name:', error);
      throw error;
    }
  }

  /**
   * Get subjects with study statistics
   */
  async getSubjectsWithStats(userId: string): Promise<Subject[]> {
    try {
      // Get subjects
      const subjects = await this.getSubjects(userId);

      // Get study session statistics for each subject
      const subjectsWithStats = await Promise.all(
        subjects.map(async (subject) => {
          const stats = await this.getSubjectStats(subject.id, userId);
          return {
            ...subject,
            ...stats,
          };
        })
      );

      return subjectsWithStats;
    } catch (error) {
      console.error('Failed to get subjects with stats:', error);
      throw error;
    }
  }

  /**
   * Get study statistics for a specific subject
   */
  async getSubjectStats(subjectId: string, userId: string) {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayISO = today.toISOString();

      // Get all sessions for this subject
      const { data: allSessions, error: allError } = await supabase
        .from('study_sessions')
        .select('duration, start_time, completed')
        .eq('user_id', userId)
        .eq('subject', subjectId)
        .eq('completed', true);

      if (allError) {
        console.error('Error fetching all sessions:', allError);
        throw allError;
      }

      // Get today's sessions
      const { data: todaySessions, error: todayError } = await supabase
        .from('study_sessions')
        .select('duration')
        .eq('user_id', userId)
        .eq('subject', subjectId)
        .eq('completed', true)
        .gte('start_time', todayISO);

      if (todayError) {
        console.error('Error fetching today sessions:', todayError);
        throw todayError;
      }

      // Calculate statistics
      const totalTime = allSessions.reduce((sum, session) => sum + session.duration, 0);
      const sessionsToday = todaySessions.length;
      const todayTime = todaySessions.reduce((sum, session) => sum + session.duration, 0);
      
      const lastStudied = allSessions.length > 0
        ? new Date(Math.max(...allSessions.map(s => new Date(s.start_time).getTime())))
        : null;

      const averageSessionLength = allSessions.length > 0
        ? totalTime / allSessions.length
        : 0;

      return {
        totalTime,
        sessionsToday,
        lastStudied: lastStudied?.toISOString(),
        averageSessionLength,
        todayTime,
      };
    } catch (error) {
      console.error('Failed to get subject stats:', error);
      return {
        totalTime: 0,
        sessionsToday: 0,
        lastStudied: null,
        averageSessionLength: 0,
        todayTime: 0,
      };
    }
  }

  /**
   * Subscribe to real-time changes for subjects
   */
  subscribeToSubjects(
    userId: string,
    callback: (payload: any) => void
  ) {
    return supabase
      .channel('subjects_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'userSubjects',
          filter: `user_id=eq.${userId}`,
        },
        callback
      )
      .subscribe();
  }

  /**
   * Unsubscribe from real-time changes
   */
  unsubscribeFromSubjects(subscription: any) {
    if (subscription) {
      supabase.removeChannel(subscription);
    }
  }

  /**
   * Bulk create subjects
   */
  async bulkCreateSubjects(
    userId: string,
    subjects: Array<{ name: string; color: string }>
  ): Promise<Subject[]> {
    try {
      const subjectsData: SubjectInsert[] = subjects.map(subject => ({
        user_id: userId,
        name: subject.name.trim(),
        color: subject.color,
      }));

      const { data, error } = await supabase
        .from('userSubjects')
        .insert(subjectsData)
        .select();

      if (error) {
        console.error('Error bulk creating subjects:', error);
        throw error;
      }

      return data.map(this.mapRowToSubject);
    } catch (error) {
      console.error('Failed to bulk create subjects:', error);
      throw error;
    }
  }

  /**
   * Search subjects by name
   */
  async searchSubjects(
    userId: string,
    query: string,
    limit: number = 10
  ): Promise<Subject[]> {
    try {
      const { data, error } = await supabase
        .from('userSubjects')
        .select('*')
        .eq('user_id', userId)
        .ilike('name', `%${query.trim()}%`)
        .order('name')
        .limit(limit);

      if (error) {
        console.error('Error searching subjects:', error);
        throw error;
      }

      return data.map(this.mapRowToSubject);
    } catch (error) {
      console.error('Failed to search subjects:', error);
      throw error;
    }
  }

  /**
   * Get subjects by color
   */
  async getSubjectsByColor(
    userId: string,
    color: string
  ): Promise<Subject[]> {
    try {
      const { data, error } = await supabase
        .from('userSubjects')
        .select('*')
        .eq('user_id', userId)
        .eq('color', color)
        .order('name');

      if (error) {
        console.error('Error fetching subjects by color:', error);
        throw error;
      }

      return data.map(this.mapRowToSubject);
    } catch (error) {
      console.error('Failed to get subjects by color:', error);
      throw error;
    }
  }

  /**
   * Map database row to Subject interface
   */
  private mapRowToSubject(row: SubjectRow): Subject {
    return {
      id: row.id,
      user_id: row.user_id,
      name: row.name,
      color: row.color,
      created_at: row.created_at,
      updated_at: row.updated_at,
    };
  }
}

// Export singleton instance
export const subjectService = new SubjectService();
